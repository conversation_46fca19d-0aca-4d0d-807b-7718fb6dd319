import Layout from "@/layout/LayoutProxy"

export default {
  path: "/risk-indicators",
  name: "RiskIndicators",
  component: Layout,
  redirect: "/risk-indicators/preference",
  meta: {
    icon: "homeFilled",
    title: "风险指标情况总览",
  },
  children: [
    {
      path: "/risk-indicators/preference",
      name: "RiskIndicatorsPreference",
      component: () => import("@/modules/risk-indicators/views/preference.vue"),
      meta: {
        title: "风险偏好指标明细",
      },
    },
    {
      path: "/risk-indicators/compliance",
      name: "RiskIndicatorsCompliance",
      component: () => import("@/modules/risk-indicators/views/compliance.vue"),
      meta: {
        title: "监管合规风险指标明细",
      },
    },
    {
      path: "/risk-indicators/internal",
      name: "RiskIndicatorsInternal",
      component: () => import("@/modules/risk-indicators/views/internal.vue"),
      meta: {
        title: "内部管理风险指标明细",
      },
    },
  ],
} as RouteConfigsTable
