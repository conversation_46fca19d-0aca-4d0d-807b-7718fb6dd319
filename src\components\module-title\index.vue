<script setup lang="ts">
defineOptions({
  name: "ModuleTitle",
})

const { title, bottomMargin = 12 } = defineProps<{
  /** 模块标题 */
  title?: string
  /** 标题底部边距 */
  bottomMargin?: number
}>()
</script>

<template>
  <div :class="`mb-[${bottomMargin}px] border-b border-dashed border-[#d9d9d9] pb-[4px]`">
    <div class="flex items-center">
      <img src="@/assets/header_title_icon.png" alt="" class="mr-[8px] h-[16px] w-[16px]" />
      <h3 class="h-[30px] text-[14px] leading-[30px] font-bold text-[var(--xq-text-color-primary)]">
        {{ title }}
      </h3>
    </div>
  </div>
</template>
