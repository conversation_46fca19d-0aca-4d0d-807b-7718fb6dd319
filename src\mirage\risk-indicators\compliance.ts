import type {
  ComplianceFormModel,
  ComplianceTableData,
} from "@/modules/risk-indicators/types"

// 监管合规风险指标明细模块
export const createComplianceRoutes = (server: any) => {
  // 获取监管合规风险指标明细列表
  server.get("/api/risk-indicators/compliance/list", (schema, request) => {
    const queryParams = request.queryParams as Partial<ComplianceFormModel> & {
      page?: string
      pageSize?: string
    }

    // 模拟分页数据
    const page = parseInt(queryParams.page || "1")
    const pageSize = parseInt(queryParams.pageSize || "10")

    // 生成模拟数据
    const mockData: ComplianceTableData[] = Array.from(
      { length: 50 },
      (_, index) => ({
        vcFundTypeName: ["固定收益类", "权益类", "混合类"][index % 3],
        vcFundSeriesName: `产品系列${index + 1}`,
        vcFundCode: `FUND${String(index + 1).padStart(4, "0")}`,
        vcFundName: `产品名称${index + 1}`,
        vcManagerName: `投资经理${index + 1}`,
        vcItTypeName: ["投资额类", "联合风控类", "持仓比例类", "债券类"][
          index % 4
        ],
        vcItCode: `IT${String(index + 1).padStart(6, "0")}`,
        vcItName: `风险指标${index + 1}`,
        vcOutlineNum: Math.floor(Math.random() * 1000000) + 100000,
        vcWarnNum: Math.floor(Math.random() * 800000) + 80000,
        fDactualMoney: Math.floor(Math.random() * 900000) + 90000,
        fDactualValue: Math.random() * 100,
        vcItStatus: ["合规", "预警", "违规", "手动合规"][index % 4] as any,
        vcCheckedStatus: ["复核通过", "复核不通过", "待复核"][index % 3] as any,
      })
    )

    // 简单的筛选逻辑
    let filteredData = mockData

    if (queryParams.vcFundCode) {
      filteredData = filteredData.filter(item =>
        item.vcFundCode
          .toLowerCase()
          .includes(queryParams.vcFundCode!.toLowerCase())
      )
    }

    if (queryParams.vcManagerName) {
      filteredData = filteredData.filter(item =>
        item.vcManagerName
          .toLowerCase()
          .includes(queryParams.vcManagerName!.toLowerCase())
      )
    }

    if (queryParams.vcItCode) {
      filteredData = filteredData.filter(item =>
        item.vcItCode
          .toLowerCase()
          .includes(queryParams.vcItCode!.toLowerCase())
      )
    }

    if (queryParams.vcItName) {
      filteredData = filteredData.filter(item =>
        item.vcItName
          .toLowerCase()
          .includes(queryParams.vcItName!.toLowerCase())
      )
    }

    if (queryParams.vcItStatus) {
      filteredData = filteredData.filter(
        item => item.vcItStatus === queryParams.vcItStatus
      )
    }

    if (queryParams.vcCheckedStatus) {
      filteredData = filteredData.filter(
        item => item.vcCheckedStatus === queryParams.vcCheckedStatus
      )
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)

    return {
      code: 200,
      message: "success",
      data: {
        list: paginatedData,
        total: filteredData.length,
        page,
        pageSize,
        totalPages: Math.ceil(filteredData.length / pageSize),
      },
    }
  })

  // 导出监管合规风险指标明细
  server.post("/api/risk-indicators/compliance/export", (schema, request) => {
    const requestBody = JSON.parse(
      request.requestBody
    ) as Partial<ComplianceFormModel>

    // 模拟导出成功响应
    return {
      code: 200,
      message: "导出成功",
      data: {
        downloadUrl: "/api/files/compliance-export.xlsx",
        fileName: `监管合规风险指标明细_${new Date().toISOString().split("T")[0]}.xlsx`,
      },
    }
  })

  // 获取监管合规风险指标明细详情
  server.get("/api/risk-indicators/compliance/:id", (schema, request) => {
    const id = request.params.id

    // 模拟详情数据
    const mockDetail: ComplianceTableData = {
      vcFundTypeName: "固定收益类",
      vcFundSeriesName: "产品系列A",
      vcFundCode: "FUND0001",
      vcFundName: "产品名称A",
      vcManagerName: "张三",
      vcItTypeName: "投资额类",
      vcItCode: "IT000001",
      vcItName: "单一证券投资比例",
      vcOutlineNum: 500000,
      vcWarnNum: 400000,
      fDactualMoney: 350000,
      fDactualValue: 70.5,
      vcItStatus: "合规",
      vcCheckedStatus: "复核通过",
    }

    return {
      code: 200,
      message: "success",
      data: mockDetail,
    }
  })
}
