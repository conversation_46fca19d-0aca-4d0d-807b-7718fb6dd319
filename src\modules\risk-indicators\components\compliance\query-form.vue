<script setup lang="ts">
import {
  DArrowDown,
  DArrowUp,
} from "@xquant/x-ui-plus-icons-vue"

import {
  vcCheckedStatusOptions,
  vcFundTypeOptions,
  vcItStatusOptions,
  vcItTypeOptions,
} from "../../consts"
import { useQueryForm } from "../../hooks/compliance/useQueryForm"

defineOptions({
  name: "ComplianceQueryForm",
})

const { formModels, listLoading, handleQuery, handleReset, handleExport, exportLoading } =
  useQueryForm()

/** 筛选展开状态 */
const isCollapsed = ref(true)

/**
 * 处理筛选表单提交
 */
function handleFinish() {
  handleQuery()
}

/**
 * 处理重置
 */
function handleResetForm() {
  handleReset()
}
</script>

<template>
  <xq-form-query
    label-width="100"
    :collapsed="isCollapsed"
    :model="formModels"
    :form-layout="{ gutter: 20, span: 6 }"
    :default-rows-number="2"
    submitter-fixed
    @finish="handleFinish"
    @reset="handleResetForm"
  >
    <template #toggle-button>
      <xq-button
        :icon="isCollapsed ? DArrowDown : DArrowUp"
        type="primary"
        size="small"
        @click="isCollapsed = !isCollapsed"
      >
        {{ isCollapsed ? "展开筛选" : "收起筛选" }}
      </xq-button>
    </template>

    <!-- 产品类型 -->
    <xq-form-layout-item label="产品类型">
      <xq-select v-model="formModels.vcFundTypeCode" class="w-full" clearable>
        <xq-option
          v-for="option in vcFundTypeOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <!-- 产品系列名称 -->
    <xq-form-layout-item label="产品系列名称">
      <xq-input
        v-model="formModels.vcFundSeriesName"
        class="w-full"
        placeholder="请输入产品系列名称"
        clearable
      />
    </xq-form-layout-item>

    <!-- 产品代码 -->
    <xq-form-layout-item label="产品代码">
      <xq-input
        v-model="formModels.vcFundCode"
        class="w-full"
        placeholder="请输入产品代码"
        clearable
      />
    </xq-form-layout-item>

    <!-- 投资经理 -->
    <xq-form-layout-item label="投资经理">
      <xq-input
        v-model="formModels.vcManagerName"
        class="w-full"
        placeholder="请输入投资经理"
        clearable
      />
    </xq-form-layout-item>

    <!-- 指标类型 -->
    <xq-form-layout-item label="指标类型">
      <xq-select v-model="formModels.vcItType" class="w-full" clearable>
        <xq-option
          v-for="option in vcItTypeOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <!-- 指标编号 -->
    <xq-form-layout-item label="指标编号">
      <xq-input
        v-model="formModels.vcItCode"
        class="w-full"
        placeholder="请输入指标编号"
        clearable
      />
    </xq-form-layout-item>

    <!-- 指标名称 -->
    <xq-form-layout-item label="指标名称">
      <xq-input
        v-model="formModels.vcItName"
        class="w-full"
        placeholder="请输入指标名称"
        clearable
      />
    </xq-form-layout-item>

    <!-- 指标状态 -->
    <xq-form-layout-item label="指标状态">
      <xq-select v-model="formModels.vcItStatus" class="w-full" clearable>
        <xq-option
          v-for="option in vcItStatusOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <!-- 指标处理状态 -->
    <xq-form-layout-item label="指标处理状态">
      <xq-select v-model="formModels.vcCheckedStatus" class="w-full" clearable>
        <xq-option
          v-for="option in vcCheckedStatusOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <!-- 查询区间 -->
    <xq-form-layout-item label="查询区间">
      <xq-date-picker
        v-model="formModels.queryDateRange"
        type="daterange"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="w-full"
        clearable
      />
    </xq-form-layout-item>

    <template #submitter>
      <div class="flex items-center space-x-2">
        <xq-button type="primary" :loading="listLoading" @click="handleFinish">查询</xq-button>
        <xq-button @click="handleResetForm">重置</xq-button>
        <xq-button type="success" :loading="exportLoading" @click="handleExport">导出</xq-button>
      </div>
    </template>
  </xq-form-query>
</template>

<style lang="scss" scoped></style>
