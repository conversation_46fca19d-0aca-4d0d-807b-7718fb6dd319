/**
 * 大文件下载管理器 - 主入口
 * 基于 File System Access API + Web Worker + Streaming
 */

// 导出简化版本的下载器
export {
  createDownloader,
  downloadFile,
  SimpleFileDownloader as LargeFileDownloader,
} from "./simple-downloader"

// 导出工具函数
export {
  formatFileSize,
  formatRemainingTime,
  formatSpeed,
  getBrowserCompatibilityInfo,
  getDownloadCapabilities,
  isFileSystemAccessSupported,
} from "./utils"

// 导出类型
export type {
  DownloadOptions,
  DownloadProgress,
  DownloadResult,
  DownloadTask,
} from "./types"

// 导出异步文件下载器
export {
  AsyncFileDownloader,
  createAsyncDownloader,
  downloadAsyncFile,
  useAsyncFileDownload,
} from "./async-file-downloader"

// 导出异步下载相关类型
export type {
  AsyncDownloadOptions,
  AsyncDownloadResult,
  AsyncFileStatus,
} from "./async-file-downloader"
