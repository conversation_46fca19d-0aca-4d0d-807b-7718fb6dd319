import { defineStore } from "pinia"
import { constantMenus } from "@/router"
import { ascending, filterTree, filterNoPermissionTree } from "@/router/utils"
import { getUserPermissionsByAppcode } from "@/api/user"
import type { PermissionResult } from "@/api/user"

export const usePermissionStore = defineStore("pure-permission", {
  state: () => ({
    // 静态路由生成的菜单
    constantMenus,
    // 整体路由生成的菜单（静态、动态）
    wholeMenus: [] as any[],
    // 操作权限表
    permissions: [] as string[],
  }),
  actions: {
    /** 组装整体路由生成的菜单 */
    handleWholeMenus(routes: any[]) {
      this.wholeMenus = filterNoPermissionTree(
        filterTree(ascending(this.constantMenus.concat(routes)))
      )
    },
    /** 清空缓存页面 */
    clearAllCachePage() {
      this.wholeMenus = []
    },
    // 设置操作权限
    async setPermissions(appCode) {
      try {
        const res = (await getUserPermissionsByAppcode(
          appCode
        )) as PermissionResult
        console.log("Permission API response:", res)

        if (!res || !res.data) {
          console.error("Invalid permission response:", res)
          return
        }

        const menuPermissions = res.data.menuPermissions
        if (!menuPermissions) {
          console.error("menuPermissions is undefined in response:", res.data)
          return
        }

        const allButtonPermissions: string[] = []
        for (const menuPermission of menuPermissions) {
          const buttons = menuPermission.permissionCacheDto.buttons as string[]
          if (buttons && buttons.length > 0) {
            allButtonPermissions.push(...buttons)
          }
        }
        this.permissions = allButtonPermissions
        console.log("Permissions set:", allButtonPermissions)
      } catch (error) {
        console.error("Error setting permissions:", error)
      }
    },
  },
})
