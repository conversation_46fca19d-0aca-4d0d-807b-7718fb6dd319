<script setup lang="ts">
import { createGroupedTableStyleFunctions } from "@/utils/tableStyleUtils"

import { useQueryForm } from "../../hooks/preference/useQueryForm"

defineOptions({
  name: "PreferenceTable",
})

const { tableData, total, pagination, listLoading, handlePageChange, handlePageSizeChange } =
  useQueryForm()

// 使用通用的分组表格样式函数，配置 preference 表格的列索引范围
// 对于 preference 表格，我们希望：
// "指标基础信息" → 蓝色（第一组颜色）
// "指标使用情况" → 橙色（第二组颜色）
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions({
  groupOneLabel: "指标基础信息", // 第一组：蓝色
  groupTwoLabel: "指标使用情况", // 第二组：橙色
  groupThreeLabel: "扩展信息", // 第三组：绿色（预留）
  columnRanges: {
    groupOne: [0, 5], // 第一组：5列（指标类型、指标名称、额度值、预警值、指标管理部门）
    groupTwo: [5, 7], // 第二组：2列（实际值、指标状态）
    groupThree: [7, Infinity], // 第三组：预留扩展
  },
})
</script>

<template>
  <div class="flex h-full w-full flex-col">
    <!-- 表格 -->
    <xq-table
      :data="tableData"
      :loading="listLoading"
      class="flex-1"
      :stripe="false"
      border
      :header-cell-class-name="getHeaderCellClassName"
      :cell-class-name="getCellClassName"
      style="width: 100%"
    >
      <!-- 指标基础信息分组 -->
      <xq-table-column label="指标基础信息" align="center">
        <xq-table-column prop="vcltTypeName" label="指标类型" min-width="120" align="center" />
        <xq-table-column prop="indexName" label="指标名称" min-width="200" align="left" />
        <xq-table-column prop="tolVal" label="额度值" min-width="100" align="right" />
        <xq-table-column prop="earlyWarnVal" label="预警值" min-width="100" align="right" />
        <xq-table-column
          prop="indexManageDept"
          label="指标管理部门"
          min-width="150"
          align="center"
        />
      </xq-table-column>

      <!-- 指标使用情况分组 -->
      <xq-table-column label="指标使用情况" align="center">
        <xq-table-column prop="actualVal" label="实际值" min-width="100" align="right" />
        <xq-table-column prop="vcltStatus" label="指标状态" min-width="100" align="center">
          <template #default="{ row }">
            <span
              :class="{ 'font-medium text-red-500': ['违规', '预警'].includes(row.vcltStatus) }"
            >
              {{ row.vcltStatus }}
            </span>
          </template>
        </xq-table-column>
      </xq-table-column>
    </xq-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <xq-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 组件特有样式可以在这里添加 */
</style>
