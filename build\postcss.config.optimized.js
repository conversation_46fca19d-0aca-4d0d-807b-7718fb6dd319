// 优化版本的 PostCSS 配置
module.exports = {
  plugins: {
    "postcss-import": {},
    "tailwindcss/nesting": {},
    tailwindcss: {},
    autoprefixer: {},
    // 生产环境优化
    ...(process.env.NODE_ENV === "production" ? {
      cssnano: {
        preset: ['default', {
          discardComments: { removeAll: true },
          normalizeWhitespace: false,
          // 新增：优化选项
          mergeRules: true,
          mergeLonghand: true,
          convertValues: true,
          discardDuplicates: true
        }]
      }
    } : {}),
  },
}
