/**
 * File System Access API 类型声明
 * 扩展 Window 接口以支持文件系统访问 API
 */

/**
 * 文件选择器接受的文件类型定义
 */
interface FilePickerAcceptType {
  /** 文件类型的描述文本，会显示在文件选择对话框中 */
  description?: string
  /**
   * 接受的文件类型映射
   * 键：MIME 类型（如 'text/csv', 'image/png'）
   * 值：文件扩展名数组（如 ['.csv'], ['.png', '.jpg']）
   *
   * @example
   * {
   *   'text/csv': ['.csv'],
   *   'image/*': ['.png', '.jpg', '.gif'],
   *   'application/json': ['.json']
   * }
   */
  accept: Record<string, string[]>
}

/**
 * 文件选择器配置选项
 */
interface FilePickerOptions {
  /** 建议的默认文件名，用户可以修改 */
  suggestedName?: string
  /**
   * 允许的文件类型列表
   * 如果未指定，默认允许所有文件类型
   */
  types?: FilePickerAcceptType[]
  /**
   * 是否排除"所有文件"选项
   * 默认 false，会显示"所有文件 (*.*)"选项
   */
  excludeAcceptAllOption?: boolean
}

/**
 * 文件系统文件句柄
 * 代表用户选择的一个文件，可用于读取或写入
 */
interface FileSystemFileHandle {
  /** 文件句柄类型，始终为 'file' */
  readonly kind: "file"
  /** 文件名（包含扩展名） */
  readonly name: string

  /**
   * 创建可写入流（新 API）
   * @returns 返回可写入的文件流，用于写入数据
   */
  createWritable(): Promise<FileSystemWritableFileStream>

  /**
   * 创建可写入文件流（兼容性 API）
   * @returns 返回可写入的文件流，功能同 createWritable()
   * @deprecated 推荐使用 createWritable()
   */
  createWritableFileStream(): Promise<FileSystemWritableFileStream>

  /**
   * 获取文件对象
   * @returns 返回 File 对象，可用于读取文件内容
   */
  getFile(): Promise<File>
}

/**
 * 文件系统可写入流
 * 扩展了标准的 WritableStream，提供文件写入功能
 */
interface FileSystemWritableFileStream extends WritableStream {
  /**
   * 写入数据到文件
   * @param data 要写入的数据，支持二进制数据、Blob 或字符串
   */
  write(data: BufferSource | Blob | string): Promise<void>

  /**
   * 移动文件指针到指定位置
   * @param position 文件中的字节位置
   */
  seek(position: number): Promise<void>

  /**
   * 截断文件到指定大小
   * @param size 新的文件大小（字节）
   */
  truncate(size: number): Promise<void>

  /**
   * 关闭文件流并保存文件
   * 必须调用此方法才能完成文件写入
   */
  close(): Promise<void>

  /**
   * 中止文件操作并丢弃更改
   * 文件不会被保存
   */
  abort(): Promise<void>
}

/**
 * 扩展 Window 接口以支持文件系统访问 API
 */
interface Window {
  /**
   * 显示保存文件对话框
   * 让用户选择保存位置和文件名
   *
   * @param options 文件选择器配置选项
   * @returns 返回文件句柄，用于后续文件操作
   *
   * @example
   * const fileHandle = await window.showSaveFilePicker({
   *   suggestedName: 'data.csv',
   *   types: [{
   *     description: 'CSV files',
   *     accept: { 'text/csv': ['.csv'] }
   *   }]
   * })
   */
  showSaveFilePicker(options?: FilePickerOptions): Promise<FileSystemFileHandle>

  /**
   * 显示打开文件对话框
   * 让用户选择要打开的文件
   *
   * @param options 文件选择器配置选项
   * @returns 返回文件句柄数组（支持多选）
   *
   * @example
   * const [fileHandle] = await window.showOpenFilePicker({
   *   types: [{
   *     description: 'Images',
   *     accept: { 'image/*': ['.png', '.jpg', '.gif'] }
   *   }]
   * })
   */
  showOpenFilePicker(
    options?: FilePickerOptions
  ): Promise<FileSystemFileHandle[]>
}

/**
 * ============================================================================
 * 使用示例和最佳实践
 * ============================================================================
 */

/**
 * 示例1: 保存 CSV 文件
 *
 * ```typescript
 * const csvData = "名称,年龄,城市\n张三,25,北京\n李四,30,上海"
 *
 * const fileHandle = await window.showSaveFilePicker({
 *   suggestedName: 'users.csv',
 *   types: [{
 *     description: 'CSV 文件',
 *     accept: { 'text/csv': ['.csv'] }
 *   }]
 * })
 *
 * const writable = await fileHandle.createWritable()
 * await writable.write(csvData)
 * await writable.close()
 * ```
 */

/**
 * 示例2: 保存图片文件
 *
 * ```typescript
 * const canvas = document.getElementById('myCanvas') as HTMLCanvasElement
 *
 * canvas.toBlob(async (blob) => {
 *   const fileHandle = await window.showSaveFilePicker({
 *     suggestedName: 'chart.png',
 *     types: [{
 *       description: '图片文件',
 *       accept: { 'image/png': ['.png'] }
 *     }]
 *   })
 *
 *   const writable = await fileHandle.createWritable()
 *   await writable.write(blob!)
 *   await writable.close()
 * })
 * ```
 */

/**
 * 示例3: 流式写入大文件
 *
 * ```typescript
 * const fileHandle = await window.showSaveFilePicker({
 *   suggestedName: 'large-data.json',
 *   types: [{ description: 'JSON 文件', accept: { 'application/json': ['.json'] } }]
 * })
 *
 * const writable = await fileHandle.createWritable()
 *
 * // 分块写入大量数据
 * await writable.write('[\n')
 * for (let i = 0; i < 1000000; i++) {
 *   const data = JSON.stringify({ id: i, value: Math.random() })
 *   await writable.write(data + (i < 999999 ? ',\n' : '\n'))
 * }
 * await writable.write(']')
 * await writable.close()
 * ```
 */

/**
 * ============================================================================
 * 浏览器兼容性说明
 * ============================================================================
 *
 * File System Access API 目前支持情况：
 * - ✅ Chrome 86+
 * - ✅ Edge 86+
 * - ❌ Firefox (计划中)
 * - ❌ Safari (未确定)
 *
 * 检查兼容性：
 * ```typescript
 * if ('showSaveFilePicker' in window) {
 *   // 支持现代文件保存 API
 *   await window.showSaveFilePicker(options)
 * } else {
 *   // 降级到传统下载方式
 *   const link = document.createElement('a')
 *   link.href = URL.createObjectURL(blob)
 *   link.download = filename
 *   link.click()
 * }
 * ```
 */

/**
 * ============================================================================
 * 安全和权限说明
 * ============================================================================
 *
 * 1. HTTPS 要求:
 *    - 此 API 只能在安全上下文 (HTTPS) 中使用
 *    - localhost 开发环境例外
 *
 * 2. 用户授权:
 *    - 每次操作都需要用户明确授权
 *    - 用户可以随时取消操作
 *
 * 3. 隐私保护:
 *    - 网站无法直接访问用户文件系统
 *    - 只能操作用户明确选择的文件
 *
 * 4. 错误处理:
 *    ```typescript
 *    try {
 *      const fileHandle = await window.showSaveFilePicker()
 *      // 文件操作...
 *    } catch (error) {
 *      if (error.name === 'AbortError') {
 *        // 用户取消了操作
 *      } else {
 *        // 其他错误
 *        console.error('文件操作失败:', error)
 *      }
 *    }
 *    ```
 */

// 导出类型以供其他文件使用
export type {
  /** 文件选择器接受的文件类型定义 */
  FilePickerAcceptType,
  /** 文件选择器配置选项 */
  FilePickerOptions,
  /** 文件系统文件句柄 */
  FileSystemFileHandle,
  /** 文件系统可写入流 */
  FileSystemWritableFileStream,
}
