import type { Ref } from "vue"

import type {
  InternalFormModel,
  InternalTableData,
} from "@/modules/risk-indicators/types"
import { http } from "@/utils/http"
import { useMutation, useQuery, useQueryClient } from "@tanstack/vue-query"

// API响应类型定义
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

interface ExportResponse {
  downloadUrl: string
  fileName: string
}

// 查询参数类型
interface InternalQueryParams extends Partial<InternalFormModel> {
  page?: number
  pageSize?: number
}

/**
 * 内部管理风险指标明细服务
 */
export function useInternalService() {
  const queryClient = useQueryClient()

  // 查询键工厂
  const queryKeys = {
    all: ["internal"] as const,
    lists: () => [...queryKeys.all, "list"] as const,
    list: (params: InternalQueryParams) =>
      [...queryKeys.lists(), params] as const,
    details: () => [...queryKeys.all, "detail"] as const,
    detail: (id: string) => [...queryKeys.details(), id] as const,
  }

  /**
   * 获取内部管理风险指标明细列表
   */
  const useInternalList = (
    params: Ref<InternalQueryParams> | InternalQueryParams,
    options?: {
      enabled?: Ref<boolean> | boolean
      staleTime?: number
      cacheTime?: number
    }
  ) => {
    return useQuery({
      queryKey: queryKeys.list(
        typeof params === "object" && "value" in params ? params.value : params
      ),
      queryFn: async () => {
        const queryParams =
          typeof params === "object" && "value" in params
            ? params.value
            : params
        console.log("🔍 [Internal Service] 查询参数:", queryParams)
        const response = await http.get<
          ApiResponse<PaginatedResponse<InternalTableData>>
        >("/api/risk-indicators/internal/list", { params: queryParams })
        console.log("🔍 [Internal Service] HTTP 响应:", response)
        console.log("🔍 [Internal Service] response.data:", response.data)
        return response.data
      },
      staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5分钟
      gcTime: options?.cacheTime ?? 10 * 60 * 1000, // 10分钟
      enabled: options?.enabled ?? true,
    })
  }

  /**
   * 获取内部管理风险指标明细详情
   */
  const useInternalDetail = (
    id: Ref<string> | string,
    options?: {
      enabled?: boolean
      staleTime?: number
      cacheTime?: number
    }
  ) => {
    return useQuery({
      queryKey: queryKeys.detail(
        typeof id === "object" && "value" in id ? id.value : id
      ),
      queryFn: async () => {
        const detailId = typeof id === "object" && "value" in id ? id.value : id
        const response = await http.get<ApiResponse<InternalTableData>>(
          `/api/risk-indicators/internal/${detailId}`
        )
        return response.data
      },
      staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5分钟
      gcTime: options?.cacheTime ?? 10 * 60 * 1000, // 10分钟
      enabled: options?.enabled ?? true,
    })
  }

  /**
   * 导出内部管理风险指标明细
   */
  const useInternalExport = () => {
    return useMutation({
      mutationFn: async (params: Partial<InternalFormModel>) => {
        const response = await http.post<ApiResponse<ExportResponse>>(
          "/api/risk-indicators/internal/export",
          params
        )
        return response.data
      },
      onSuccess: data => {
        // 导出成功后可以进行一些操作，比如下载文件
        console.log("导出成功:", data)
      },
      onError: error => {
        console.error("导出失败:", error)
      },
    })
  }

  /**
   * 刷新列表缓存
   */
  const invalidateInternalList = () => {
    return queryClient.invalidateQueries({
      queryKey: queryKeys.lists(),
    })
  }

  /**
   * 刷新详情缓存
   */
  const invalidateInternalDetail = (id?: string) => {
    if (id) {
      return queryClient.invalidateQueries({
        queryKey: queryKeys.detail(id),
      })
    }
    return queryClient.invalidateQueries({
      queryKey: queryKeys.details(),
    })
  }

  /**
   * 预取列表数据
   */
  const prefetchInternalList = (params: InternalQueryParams) => {
    return queryClient.prefetchQuery({
      queryKey: queryKeys.list(params),
      queryFn: async () => {
        const response = await http.get<
          ApiResponse<PaginatedResponse<InternalTableData>>
        >("/api/risk-indicators/internal/list", { params })
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5分钟
    })
  }

  /**
   * 预取详情数据
   */
  const prefetchInternalDetail = (id: string) => {
    return queryClient.prefetchQuery({
      queryKey: queryKeys.detail(id),
      queryFn: async () => {
        const response = await http.get<ApiResponse<InternalTableData>>(
          `/api/risk-indicators/internal/${id}`
        )
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5分钟
    })
  }

  return {
    // 查询hooks
    useInternalList,
    useInternalDetail,

    // 变更hooks
    useInternalExport,

    // 缓存管理
    invalidateInternalList,
    invalidateInternalDetail,
    prefetchInternalList,
    prefetchInternalDetail,

    // 查询键
    queryKeys,
  }
}
