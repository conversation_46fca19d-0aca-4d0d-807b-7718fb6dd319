# 表格分组样式工具函数使用说明

## 概述

`tableStyleUtils.ts` 提供了灵活的表格分组样式工具函数，用于统一处理分组表头和单元格的样式类名。通过 `createGroupedTableStyleFunctions()` 函数可以创建自定义分组配置的样式处理器，配合全局样式 `src/style/index.scss` 中的分组表格样式，可以快速实现统一的分组表格UI效果。

## 全局样式类名

全局样式已在 `src/style/index.scss` 中定义，包含以下样式类：

### 表头样式
- `group-one-header` / `one-header` - 第一组分组表头（蓝色系）
- `group-two-header` / `two-header` - 第二组分组表头（橙色系）
- `group-three-header` / `three-header` - 第三组分组表头（绿色系）

### 单元格样式
- `one-cell` / `one-cell-striped` - 第一组单元格（蓝色系）
- `two-cell` / `two-cell-striped` - 第二组单元格（橙色系）
- `three-cell` / `three-cell-striped` - 第三组单元格（绿色系）
- `normal-cell-striped` - 普通列斑马纹

## 基础用法

### 1. 导入工具函数

```typescript
import { createGroupedTableStyleFunctions } from "@/utils/tableStyleUtils"
```

### 2. 创建样式函数

```typescript
// 使用默认分组标签（最多支持3个分组）
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions()

// 或使用自定义分组标签
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions({
  groupOneLabel: "产品信息",
  groupTwoLabel: "指标基础信息",
  groupThreeLabel: "指标使用情况"
})

// 自定义列索引范围（适用于不同的列分布）
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions({
  columnRanges: {
    groupOne: [0, 6],    // 第一组：6列
    groupTwo: [6, 12],   // 第二组：6列
    groupThree: [12, Infinity], // 第三组：剩余所有列
  }
})

// 也可以只使用部分分组
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions({
  groupOneLabel: "基础信息",
  groupTwoLabel: "详细数据"
  // groupThreeLabel 可以不设置
})
```

### 3. 在表格中使用

```vue
<template>
  <xq-table
    :data="tableData"
    :header-cell-class-name="getHeaderCellClassName"
    :cell-class-name="getCellClassName"
  >
    <!-- 第一个分组 -->
    <xq-table-column label="产品信息" align="center">
      <xq-table-column prop="vcFundCode" label="产品代码" />
      <xq-table-column prop="vcFundName" label="产品名称" />
    </xq-table-column>

    <!-- 第二个分组 -->
    <xq-table-column label="指标基础信息" align="center">
      <xq-table-column prop="vcItCode" label="指标编号" />
      <xq-table-column prop="vcItName" label="指标名称" />
    </xq-table-column>

    <!-- 第三个分组 -->
    <xq-table-column label="指标使用情况" align="center">
      <xq-table-column prop="vcItStatus" label="指标状态" />
    </xq-table-column>
  </xq-table>
</template>
```

**注意**：表格列的 `label` 属性必须与配置中的分组标签完全匹配才能正确应用样式。

## 高级用法

### 自定义单元格内容

对于需要特殊处理的列（如状态列需要显示不同颜色），建议使用作用域插槽：

```vue
<xq-table-column prop="status" label="状态" align="center">
  <template #default="{ row }">
    <span :class="{ 'text-red-500 font-medium': ['违规', '预警'].includes(row.status) }">
      {{ row.status }}
    </span>
  </template>
</xq-table-column>
```

这种方式比在工具函数中处理状态逻辑更灵活，也保持了工具函数的通用性。



## 完整示例

参考 `src/modules/risk-indicators/components/compliance/table.vue` 的实现：

```vue
<script setup lang="ts">
import { createGroupedTableStyleFunctions } from "@/utils/tableStyleUtils"

// 创建样式函数
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions()
</script>

<template>
  <xq-table
    :data="tableData"
    :header-cell-class-name="getHeaderCellClassName"
    :cell-class-name="getCellClassName"
  >
    <!-- 产品信息分组 -->
    <xq-table-column label="产品信息" align="center">
      <xq-table-column prop="vcFundCode" label="产品代码" />
      <xq-table-column prop="vcFundName" label="产品名称" />
    </xq-table-column>

    <!-- 指标基础信息分组 -->
    <xq-table-column label="指标基础信息" align="center">
      <xq-table-column prop="vcItCode" label="指标编号" />
      <xq-table-column prop="vcItName" label="指标名称" />
    </xq-table-column>

    <!-- 指标使用情况分组 -->
    <xq-table-column label="指标使用情况" align="center">
      <xq-table-column prop="fDactualMoney" label="监控数据" />
      <!-- 状态列使用作用域插槽处理特殊样式 -->
      <xq-table-column prop="vcItStatus" label="指标状态">
        <template #default="{ row }">
          <span :class="{ 'text-red-500 font-medium': ['违规', '预警'].includes(row.vcItStatus) }">
            {{ row.vcItStatus }}
          </span>
        </template>
      </xq-table-column>
    </xq-table-column>
  </xq-table>
</template>
```

## 灵活配置示例

### 自定义分组名称

```typescript
// 财务报表场景
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions({
  groupOneLabel: "基本信息",
  groupTwoLabel: "财务数据",
  groupThreeLabel: "分析结果"
})

// 用户管理场景
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions({
  groupOneLabel: "个人信息",
  groupTwoLabel: "权限设置"
  // 只使用两个分组
})
```

### 动态配置

```typescript
// 根据业务场景动态配置
const getTableConfig = (moduleType: string) => {
  switch (moduleType) {
    case 'compliance':
      return {
        groupOneLabel: "产品信息",
        groupTwoLabel: "指标基础信息",
        groupThreeLabel: "指标使用情况"
      }
    case 'internal':
      return {
        groupOneLabel: "产品信息",
        groupTwoLabel: "指标基础信息",
        groupThreeLabel: "指标使用情况"
      }
    case 'financial':
      return {
        groupOneLabel: "基本信息",
        groupTwoLabel: "财务数据",
        groupThreeLabel: "分析结果"
      }
    default:
      return {}
  }
}

const config = getTableConfig('compliance')
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions(config)
```

## API 参考

### createGroupedTableStyleFunctions(config?)

创建自定义分组的样式函数。

**参数：**
- `config` (可选): `TableGroupConfig` - 分组配置对象

**返回值：**
```typescript
{
  getHeaderCellClassName: (params: { column: any; columnIndex?: number }) => string
  getCellClassName: (params: { column: any; row?: any; rowIndex: number }) => string
}
```

### TableGroupConfig

分组配置类型定义。

```typescript
interface TableGroupConfig {
  /** 第一个分组标签 */
  groupOneLabel?: string
  /** 第二个分组标签 */
  groupTwoLabel?: string
  /** 第三个分组标签 */
  groupThreeLabel?: string
}
```

## 注意事项

1. **分组限制**：最多支持3个分组，分别对应蓝色、橙色、绿色样式
2. **分组标签匹配**：确保表格列的 `label` 属性与配置中的分组标签完全一致
3. **全局样式依赖**：需要确保 `src/style/index.scss` 中的分组表格样式已正确引入
4. **动态前缀**：全局样式使用了 `#{$ns}` 动态前缀，确保项目配置正确
5. **样式映射固定**：虽然分组标签可以自定义，但样式类名映射是固定的（第一组→蓝色系，第二组→橙色系，第三组→绿色系）
6. **特殊样式处理**：对于需要特殊样式的列（如状态列），建议使用作用域插槽而不是在工具函数中处理
