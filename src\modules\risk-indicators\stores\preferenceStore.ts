import { computed, ref } from "vue"
import { useQuery } from "@tanstack/vue-query"

import { getPreferenceList, exportPreferenceList } from "../services/preference"
import type { PreferenceFormModel, PreferenceTableData } from "../types"

/**
 * 偏好类指标明细 Store
 */
export function usePreferenceStore() {
  // 表单数据
  const queryForm = ref<PreferenceFormModel>({
    indexCode: "",
    indexManageDept: "",
    vcltStatus: "",
    queryDateRange: ["", ""],
    startDate: "",
    endDate: "",
  })

  // 分页参数
  const pagination = ref({
    page: 1,
    pageSize: 10,
  })

  // 组合查询参数
  const queryParams = computed(() => {
    const { queryDateRange, ...formData } = queryForm.value
    
    // 从 queryDateRange 中提取 startDate 和 endDate
    const [startDate, endDate] = queryDateRange || ["", ""]
    
    return {
      ...formData,
      startDate,
      endDate,
      ...pagination.value,
    }
  })

  // 是否启用查询
  const enableQuery = ref(true)

  // 获取列表数据
  const {
    data: listData,
    isLoading: listLoading,
    error: listError,
    refetch: refetchList,
  } = useQuery({
    queryKey: ["preference-list", queryParams],
    queryFn: () => getPreferenceList(queryParams.value),
    enabled: enableQuery,
  })

  // 导出加载状态
  const exportLoading = ref(false)

  // 计算属性
  const tableData = computed<PreferenceTableData[]>(() => listData.value?.data?.list || [])
  const total = computed(() => listData.value?.data?.total || 0)

  /**
   * 执行查询
   */
  function handleQuery() {
    pagination.value.page = 1
    enableQuery.value = true
    refetchList()
  }

  /**
   * 重置查询条件
   */
  function handleReset() {
    queryForm.value = {
      indexCode: "",
      indexManageDept: "",
      vcltStatus: "",
      queryDateRange: ["", ""],
      startDate: "",
      endDate: "",
    }
    pagination.value.page = 1
    enableQuery.value = false
  }

  /**
   * 分页变化处理
   */
  function handlePageChange(page: number) {
    pagination.value.page = page
    refetchList()
  }

  /**
   * 分页大小变化处理
   */
  function handlePageSizeChange(pageSize: number) {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
    refetchList()
  }

  /**
   * 导出数据
   */
  async function handleExport() {
    try {
      exportLoading.value = true
      await exportPreferenceList(queryParams.value)
    } catch (error) {
      console.error("导出失败:", error)
    } finally {
      exportLoading.value = false
    }
  }

  return {
    // 响应式数据
    queryForm,
    tableData,
    total,
    pagination,
    listLoading,
    exportLoading,
    listError,

    // 方法
    handleQuery,
    handleReset,
    handlePageChange,
    handlePageSizeChange,
    handleExport,
    refetchList,
  }
}
