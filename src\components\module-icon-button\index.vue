<script setup lang="ts">
import { MoreFilled } from "@xquant/x-ui-plus-icons-vue"

interface ModuleIconButtonOption {
  /** 菜单名称 */
  label: string
  /** 菜单 ID */
  value: string
  /** 是否为危险操作（红色文字） */
  danger?: boolean
}

interface TooltipConfig {
  /** tooltip 内容 */
  content?: string
  /** 出现位置 */
  placement?:
    | "top"
    | "top-start"
    | "top-end"
    | "bottom"
    | "bottom-start"
    | "bottom-end"
    | "left"
    | "left-start"
    | "left-end"
    | "right"
    | "right-start"
    | "right-end"
  /** 主题 */
  effect?: "dark" | "light"
  /** 延迟显示时间 */
  showAfter?: number
  /** 延迟隐藏时间 */
  hideAfter?: number
}

defineOptions({
  name: "ModuleIconButton",
})

const {
  menuOptions = [],
  width = 80,
  title,
  tooltipType = "title",
  tooltipConfig = {},
  appendToBody = true,
} = defineProps<{
  /** 按钮下拉菜单配置 */
  menuOptions?: ModuleIconButtonOption[]
  /** 弹出框宽度 */
  width?: number
  /** 按钮提示信息 */
  title?: string
  /** 提示类型 */
  tooltipType?: "title" | "tooltip"
  /** tooltip 配置 */
  tooltipConfig?: TooltipConfig
  /** 是否将弹出层添加到 body，在全屏模式下应设为 false */
  appendToBody?: boolean
}>()

const emit = defineEmits<{
  /** 按钮点击事件 */
  (e: "click", value: string): void
  /** 菜单项点击事件 */
  (e: "menu-item-click", value: string): void
}>()

// 合并默认的 tooltip 配置
const mergedTooltipConfig = computed(() => ({
  content: title,
  placement: "top" as const,
  effect: "dark" as const,
  showAfter: 200,
  hideAfter: 0,
  ...tooltipConfig,
}))

// 是否显示 tooltip
const shouldShowTooltip = computed(() => title && tooltipType === "tooltip")

// HTML title 属性
const htmlTitle = computed(() => (title && tooltipType === "title" ? title : undefined))

// 是否有菜单选项
const hasMenuOptions = computed(() => menuOptions && menuOptions.length > 0)

// 处理按钮点击
function handleButtonClick() {
  if (!hasMenuOptions.value) {
    emit("click", "")
  }
}
</script>

<template>
  <!-- 有菜单时使用 popover -->
  <xq-popover v-if="hasMenuOptions" trigger="hover" :width="width" :teleported="appendToBody">
    <template #reference>
      <xq-tooltip
        v-if="shouldShowTooltip"
        :content="mergedTooltipConfig.content"
        :placement="mergedTooltipConfig.placement"
        :effect="mergedTooltipConfig.effect"
        :show-after="mergedTooltipConfig.showAfter"
        :hide-after="mergedTooltipConfig.hideAfter"
      >
        <span
          class="flex h-[20px] w-[20px] cursor-pointer items-center justify-center rounded-[4px] hover:bg-[var(--xq-color-primary-light-8)]"
        >
          <xq-icon class="h-[16px] w-[16px] text-gray-500">
            <slot>
              <MoreFilled class="rotate-90" />
            </slot>
          </xq-icon>
        </span>
      </xq-tooltip>
      <span
        v-else
        :title="htmlTitle"
        class="flex h-[20px] w-[20px] cursor-pointer items-center justify-center rounded-[4px] hover:bg-[var(--xq-color-primary-light-8)]"
      >
        <xq-icon class="h-[16px] w-[16px] text-gray-500">
          <slot>
            <MoreFilled class="rotate-90" />
          </slot>
        </xq-icon>
      </span>
    </template>
    <div class="flex flex-col">
      <div
        v-for="option in menuOptions"
        :key="option.value"
        :class="[
          'mx-[-4px] h-[30px] cursor-pointer px-[12px] leading-[30px] hover:bg-[var(--xq-color-primary-light-9)]',
          option.danger
            ? 'text-[var(--xq-color-danger)]'
            : 'text-gray-500 hover:text-[var(--xq-color-primary)]',
        ]"
        @click="emit('menu-item-click', option.value)"
      >
        {{ option.label }}
      </div>
    </div>
  </xq-popover>

  <!-- 没有菜单时使用简单按钮 -->
  <xq-tooltip
    v-else-if="shouldShowTooltip"
    :content="mergedTooltipConfig.content"
    :placement="mergedTooltipConfig.placement"
    :effect="mergedTooltipConfig.effect"
    :show-after="mergedTooltipConfig.showAfter"
    :hide-after="mergedTooltipConfig.hideAfter"
  >
    <span
      class="flex h-[20px] w-[20px] cursor-pointer items-center justify-center rounded-[4px] hover:bg-[var(--xq-color-primary-light-8)]"
      @click="handleButtonClick"
    >
      <xq-icon class="h-[16px] w-[16px] text-gray-500">
        <slot>
          <MoreFilled class="rotate-90" />
        </slot>
      </xq-icon>
    </span>
  </xq-tooltip>
  <span
    v-else
    :title="htmlTitle"
    class="flex h-[20px] w-[20px] cursor-pointer items-center justify-center rounded-[4px] hover:bg-[var(--xq-color-primary-light-8)]"
    @click="handleButtonClick"
  >
    <xq-icon class="h-[16px] w-[16px] text-gray-500">
      <slot>
        <MoreFilled class="rotate-90" />
      </slot>
    </xq-icon>
  </span>
</template>

<style scoped lang="scss">
:deep(.#{$ns}-icon svg) {
  height: 12px;
  width: 12px;
}
</style>
