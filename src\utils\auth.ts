import { usePermissionStore } from "@/store/modules/permission"

export interface DataInfo<T> {
  /** token */
  accessToken: string
  /** `accessToken`的过期时间（时间戳） */
  expires: T
  /** 用于调用刷新accessToken的接口时所需的token */
  refreshToken: string
  /** 用户名 */
  username?: string
  /** 当前登陆用户的角色 */
  roles?: Array<string>
}

export const sessionKey = "user-info"
export const TokenKey = "main_token"
export const TokenSourceKey = "main_source"

/** 获取`token` */
// export function getToken(): DataInfo<number> {
//   // 此处与`TokenKey`相同，此写法解决初始化时`Cookies`中不存在`TokenKey`报错
//   return Cookies.get(TokenKey)
//     ? JSON.parse(Cookies.get(TokenKey))
//     : storageSession().getItem(sessionKey);
// }

export function getToken(): string {
  return localStorage.getItem(TokenKey) as string
}

export function getTokenSource(): string {
  return localStorage.getItem(TokenSourceKey) as string
}

/**
 * @description 设置`token`以及一些必要信息并采用无感刷新`token`方案
 * 无感刷新：后端返回`accessToken`（访问接口使用的`token`）、`refreshToken`（用于调用刷新`accessToken`的接口时所需的`token`，`refreshToken`的过期时间（比如30天）应大于`accessToken`的过期时间（比如2小时））、`expires`（`accessToken`的过期时间）
 * 将`accessToken`、`expires`这两条信息放在key值为authorized-token的cookie里（过期自动销毁）
 * 将`username`、`roles`、`refreshToken`、`expires`这四条信息放在key值为`user-info`的sessionStorage里（浏览器关闭自动销毁）
 */
export function setToken(data: DataInfo<Date>) {
  localStorage.setItem(TokenKey, data.accessToken)
  localStorage.setItem(TokenSourceKey, "Cube")
  // let expires = 0;
  // const { accessToken, refreshToken } = data;
  // expires = new Date(data.expires).getTime(); // 如果后端直接设置时间戳，将此处代码改为expires = data.expires，然后把上面的DataInfo<Date>改成DataInfo<number>即可
  // const cookieString = JSON.stringify({ accessToken, expires });

  // expires > 0
  //   ? Cookies.set(TokenKey, cookieString, {
  //       expires: (expires - Date.now()) / 86400000
  //     })
  //   : Cookies.set(TokenKey, cookieString);
  // function setSessionKey(username: string, roles: Array<string>) {
  //   const userStore = useUserStore();
  //   userStore.SET_USERNAME(username);
  //   userStore.SET_ROLES(roles);
  //   storageSession().setItem(sessionKey, {
  //     refreshToken,
  //     expires,
  //     username,
  //     roles
  //   });
  // }

  // if (data.username && data.roles) {
  //   const { username, roles } = data;
  //   setSessionKey(username, roles);
  // } else {
  //   const username =
  //     storageSession().getItem<DataInfo<number>>(sessionKey)?.username ?? "";
  //   const roles =
  //     storageSession().getItem<DataInfo<number>>(sessionKey)?.roles ?? [];
  //   setSessionKey(username, roles);
  // }
}

/** 删除`token`以及key值为`user-info`的session信息 */
export function removeToken() {
  // Cookies.remove(TokenKey);
  // sessionStorage.clear();
  localStorage.clear()
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return "Bearer " + token
}

/** 获取当前页面按钮级别的权限 */
export function getAuths(): Array<string> {
  return usePermissionStore().permissions
}

/**
 * 是否包含指定的权限，任意一个匹配就有权限
 * @param accesses 权限数组
 * @returns boolean
 * @example hasAuth(['app:moudle:action'])
 */
export function hasAuth(accesses: Array<string>): boolean {
  const permissions = getAuths()
  if (accesses && Array.isArray(accesses) && accesses.length > 0) {
    const hasPermission = accesses.some(permission => {
      return permissions.includes(permission)
    })
    return hasPermission
  } else {
    console.error(`need permission code! Like hasAuth(['app:moudle:action'])"`)
    return false
  }
}

/**
 * 是否包含指定的所有权限，必须全部匹配才有权限
 * @param accesses 权限数组
 * @returns boolean
 * @example hasEveryAuth(['app:moudle:action'])
 *
 */
export function hasEveryAuth(accesses: string[]): boolean {
  const permissions = getAuths()
  if (accesses && Array.isArray(accesses) && accesses.length > 0) {
    const hasPermission = accesses.every(permission => {
      return permissions.includes(permission)
    })
    return hasPermission
  } else {
    console.error(`need permission code! Like hasEveryAuth(['app:moudle:action'])"`)
    return false
  }
}
