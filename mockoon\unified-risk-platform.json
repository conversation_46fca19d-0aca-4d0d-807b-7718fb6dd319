{"uuid": "5c3c957d-9740-4622-b6e0-81c94b8f5a5e", "lastMigration": 33, "name": "Unified risk platform", "endpointPrefix": "", "latency": 0, "port": 3001, "hostname": "", "folders": [], "routes": [{"uuid": "07235c6c-e5d0-4c01-afdb-91bc534c6d9a", "type": "http", "documentation": "", "method": "get", "endpoint": "", "responses": [{"uuid": "31688048-01b3-4182-90d8-910edc378cc7", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "07235c6c-e5d0-4c01-afdb-91bc534c6d9a"}], "proxyMode": false, "proxyHost": "", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [], "callbacks": []}