<script setup lang="ts">
// 异步导入组件
const AsyncPreferenceQueryForm = defineAsyncComponent(
  () => import("../components/preference/query-form.vue")
)
const AsyncPreferenceTable = defineAsyncComponent(
  () => import("../components/preference/table.vue")
)

defineOptions({
  name: "RiskIndicatorsPreference",
})

/** 作为 `<suspend />` 加载，不用关闭 */
const isAsyncLoading = ref(true)
</script>

<template>
  <xq-card
    class="h-full !overflow-y-scroll border-none"
    body-class="!p-[12px] h-full"
    shadow="never"
  >
    <div class="flex h-full flex-col">
      <module-title title="风险偏好指标明细" />

      <!-- 查询表单 -->
      <div class="mt-[8px] bg-[var(--xq-color-info-light-9)] pt-[4px]">
        <suspense>
          <AsyncPreferenceQueryForm />
          <template #fallback>
            <portal-target name="module-loading" :slot-props="{ height: 48 }" />
          </template>
        </suspense>
      </div>

      <xq-divider />

      <module-panel title="筛选结果" class="flex flex-1 flex-col">
        <!-- 数据表格 -->
        <div class="flex flex-1 items-center overflow-hidden">
          <suspense>
            <AsyncPreferenceTable />
            <template #fallback>
              <portal-target name="module-loading" />
            </template>
          </suspense>
        </div>
      </module-panel>
    </div>
  </xq-card>

  <portal v-slot="{ height }" to="module-loading">
    <module-loading v-model="isAsyncLoading" :height="height" />
  </portal>
</template>
