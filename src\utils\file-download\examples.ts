/**
 * 大文件下载使用示例
 */

import {
  downloadFile,
  isFileSystemAccessSupported,
  formatFileSize,
  formatSpeed,
  type DownloadProgress,
} from "./index"

/**
 * 基础下载示例
 */
export async function basicDownloadExample() {
  const url = "https://example.com/large-file.zip"

  try {
    const result = await downloadFile(url, "my-file.zip", {
      onProgress: (progress: DownloadProgress) => {
        console.log(`下载进度: ${progress.percentage.toFixed(2)}%`)
        console.log(`已下载: ${formatFileSize(progress.downloaded)}`)
        console.log(`速度: ${formatSpeed(progress.speed)}`)
      },
      onError: (error: Error) => {
        console.error("下载失败:", error.message)
      },
    })

    if (result.success) {
      console.log("下载成功!", result.filename)
    } else {
      console.error("下载失败:", result.error)
    }
  } catch (error) {
    console.error("下载异常:", error)
  }
}

/**
 * 带进度 UI 的下载示例
 */
export class DownloadProgressUI {
  private progressElement: HTMLElement
  private statusElement: HTMLElement

  constructor(containerId: string) {
    const container = document.getElementById(containerId)
    if (!container) {
      throw new Error(`Container element with id "${containerId}" not found`)
    }

    // 创建进度 UI
    container.innerHTML = `
      <div class="download-progress">
        <div class="progress-bar">
          <div class="progress-fill" style="width: 0%"></div>
        </div>
        <div class="progress-info">
          <span class="status">准备下载...</span>
          <span class="percentage">0%</span>
        </div>
        <div class="download-details">
          <span class="size">0 B / 0 B</span>
          <span class="speed">0 B/s</span>
          <span class="time">--</span>
        </div>
      </div>
    `

    this.progressElement = container.querySelector(".progress-fill")!
    this.statusElement = container.querySelector(".status")!
  }

  updateProgress(progress: DownloadProgress) {
    // 更新进度条
    this.progressElement.style.width = `${progress.percentage}%`

    // 更新百分比
    const percentageElement = document.querySelector(".percentage")!
    percentageElement.textContent = `${progress.percentage.toFixed(1)}%`

    // 更新大小信息
    const sizeElement = document.querySelector(".size")!
    const totalText = progress.total ? formatFileSize(progress.total) : "未知"
    sizeElement.textContent = `${formatFileSize(progress.downloaded)} / ${totalText}`

    // 更新速度
    const speedElement = document.querySelector(".speed")!
    speedElement.textContent = formatSpeed(progress.speed)

    // 更新剩余时间
    const timeElement = document.querySelector(".time")!
    if (progress.remainingTime) {
      const minutes = Math.floor(progress.remainingTime / 60)
      const seconds = Math.floor(progress.remainingTime % 60)
      timeElement.textContent = `${minutes}:${seconds.toString().padStart(2, "0")}`
    } else {
      timeElement.textContent = "--"
    }

    // 更新状态
    if (progress.percentage >= 100) {
      this.statusElement.textContent = "下载完成"
    } else if (progress.percentage > 0) {
      this.statusElement.textContent = "下载中..."
    }
  }

  showError(error: string) {
    this.statusElement.textContent = `错误: ${error}`
    this.statusElement.style.color = "red"
  }
}

/**
 * 金融数据导出示例（适用于您的项目）
 */
export async function exportSpreadAnalysisData(
  data: any[],
  filename?: string,
  onProgress?: (progress: DownloadProgress) => void
) {
  // 检查浏览器支持
  if (!isFileSystemAccessSupported()) {
    throw new Error(
      "您的浏览器不支持高级下载功能，请使用最新版本的 Chrome、Edge 或 Firefox"
    )
  }

  try {
    // 将数据转换为 CSV 格式
    const csvContent = convertToCSV(data)
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" })

    // 创建临时 URL
    const url = URL.createObjectURL(blob)

    try {
      const result = await downloadFile(
        url,
        filename || `spread-analysis-${Date.now()}.csv`,
        {
          types: [
            {
              description: "CSV files",
              accept: { "text/csv": [".csv"] },
            },
          ],
          onProgress,
          onError: error => {
            console.error("导出失败:", error)
          },
        }
      )

      return result
    } finally {
      // 清理临时 URL
      URL.revokeObjectURL(url)
    }
  } catch (error) {
    throw new Error(
      `数据导出失败: ${error instanceof Error ? error.message : String(error)}`
    )
  }
}

/**
 * 将数据转换为 CSV 格式
 */
function convertToCSV(data: any[]): string {
  if (!data || data.length === 0) {
    return ""
  }

  // 获取表头
  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(",")

  // 转换数据行
  const csvRows = data.map(row => {
    return headers
      .map(header => {
        const value = row[header]
        // 处理包含逗号或换行符的值
        if (
          typeof value === "string" &&
          (value.includes(",") || value.includes("\\n"))
        ) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value ?? ""
      })
      .join(",")
  })

  return [csvHeaders, ...csvRows].join("\\n")
}

/**
 * Vue 组合式函数：用于在 Vue 组件中使用下载功能
 */
export function useFileDownload() {
  const downloadProgress = ref<DownloadProgress | null>(null)
  const isDownloading = ref(false)
  const downloadError = ref<string | null>(null)

  const download = async (url: string, filename?: string) => {
    try {
      isDownloading.value = true
      downloadError.value = null
      downloadProgress.value = null

      const result = await downloadFile(url, filename, {
        onProgress: progress => {
          downloadProgress.value = progress
        },
        onError: error => {
          downloadError.value = error.message
        },
      })

      return result
    } finally {
      isDownloading.value = false
    }
  }

  const formatProgress = computed(() => {
    if (!downloadProgress.value) return null

    return {
      percentage: downloadProgress.value.percentage.toFixed(1),
      downloaded: formatFileSize(downloadProgress.value.downloaded),
      total: downloadProgress.value.total
        ? formatFileSize(downloadProgress.value.total)
        : "未知",
      speed: formatSpeed(downloadProgress.value.speed),
    }
  })

  return {
    download,
    downloadProgress,
    isDownloading,
    downloadError,
    formatProgress,
    isSupported: isFileSystemAccessSupported(),
  }
}
