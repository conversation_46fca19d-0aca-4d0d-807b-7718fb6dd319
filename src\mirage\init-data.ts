import { ColorEnum, CustomViewCodeEnum } from "@/constants"
import { faker } from "@faker-js/faker"

/**
 * 生成的初始数据结构：
 * {
 *   views: {
 *     "01": [CustomViewConfig, CustomViewConfig, ...], // 利差视图（静态）
 *     "02": [CustomViewConfig, CustomViewConfig, ...], // 利差视图（动态）
 *   }
 * }
 *
 * 每个视图编码对应5条 CustomViewConfig 类型的数据
 * 符合 MirageJS database 的数据格式要求
 */

// 生成自定义视图配置数据
const generateCustomViewConfig = (code: string, index: number) => {
  const colorCodes = Object.values(ColorEnum)
  const now = new Date()
  const createdAt = faker.date.past({ years: 1 }).toISOString()

  return {
    id: faker.string.uuid(),
    name: `自定义视图 ${index + 1}`,
    code: code,
    description: faker.lorem.sentence({ min: 5, max: 15 }),
    colorCode: faker.helpers.arrayElement([...colorCodes, ""]),
    order: index + 1,
    createdAt,
    updatedAt: faker.date.between({ from: createdAt, to: now }).toISOString(),
    data: {
      queryConfig: {
        // 查询表单配置
        dateRange: [
          faker.date.past({ years: 1 }).toISOString().split("T")[0],
          faker.date.recent().toISOString().split("T")[0],
        ],
        bondTypes: faker.helpers.arrayElements(
          ["政府债", "企业债", "金融债", "可转债"],
          { min: 1, max: 3 }
        ),
        ratingRange: faker.helpers.arrayElement([
          ["AAA", "AA+"],
          ["AA", "AA-"],
          ["A+", "A"],
        ]),
        industryCategories: faker.helpers.arrayElements(
          ["银行业", "房地产业", "制造业", "能源业", "科技业"],
          { min: 1, max: 3 }
        ),
        maturityRange: [
          faker.number.int({ min: 1, max: 10 }),
          faker.number.int({ min: 11, max: 30 }),
        ],
      },
      chartConfig: {
        // 图表展示配置
        chartType: faker.helpers.arrayElement([
          "line",
          "bar",
          "scatter",
          "bubble",
        ]),
        xAxis: faker.helpers.arrayElement([
          "date",
          "rating",
          "maturity",
          "industry",
        ]),
        yAxis: faker.helpers.arrayElement([
          "spread",
          "yield",
          "duration",
          "volume",
        ]),
        groupBy: faker.helpers.arrayElement([
          "bondType",
          "rating",
          "industry",
          "issuer",
        ]),
        showLegend: faker.datatype.boolean(),
        showGrid: faker.datatype.boolean(),
        colorScheme: faker.helpers.arrayElement([
          "default",
          "warm",
          "cool",
          "rainbow",
        ]),
        aggregationType: faker.helpers.arrayElement([
          "avg",
          "sum",
          "max",
          "min",
          "median",
        ]),
      },
    },
  }
}

// 生成视图数据，按视图编码分组
const generateViewsByCode = () => {
  const views = {}
  const codes = Object.values(CustomViewCodeEnum)

  codes.forEach(code => {
    // 每个视图编码生成5条数据
    views[code] = Array.from({ length: 5 }, (_, index) =>
      generateCustomViewConfig(code, index)
    )
  })

  return views
}

export default {
  // 模板视图数据，按视图编码分组
  views: generateViewsByCode(),
}
