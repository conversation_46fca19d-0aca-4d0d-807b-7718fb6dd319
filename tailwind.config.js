/** @type {import('tailwindcss').Config} */
module.exports = {
  // Tailwind CSS 4.x 使用新的配置格式
  darkMode: "class",
  corePlugins: {
    preflight: false,
  },
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        bg_color: "var(--xq-bg-color)",
        primary: "var(--xq-color-primary)",
        text_color_primary: "var(--xq-text-color-primary)",
        text_color_regular: "var(--xq-text-color-regular)",
      },
    },
  },
  // Tailwind CSS 4.x 新增的配置选项
  future: {
    // 启用新的默认值
    hoverOnlyWhenSupported: true,
  },
}
