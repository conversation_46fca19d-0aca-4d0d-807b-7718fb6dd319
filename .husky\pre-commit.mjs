// #!/usr/bin/env node

// import { execSync } from "child_process"

// // 这里编写 你的受保护的目录（不可随意修改）
// const PROTECTED_PATHS = [
//   "src/common/",
//   "src/components/",
//   "src/utils/",
//   "src/hooks/",
//   "src/styles/",
//   ".husky/",
// ]
// // 这里编写 允许修改公共目录的管理员用户
// const ALLOWED_EMAILS = ["<EMAIL>"]

// // Git 状态码与中文描述映射
// const STATUS_MAP = {
//   A: "新增文件",
//   M: "修改文件",
//   D: "删除文件",
//   R: "重命名文件夹",
// }

// try {
//   // 获取当前提交者的邮箱
//   const userEmail = execSync("git config user.email").toString().trim()

//   // 获取所有已暂存的变更文件
//   const changedFiles = execSync("git diff --cached --name-status").toString().trim().split("\n")

//   let hasProtectedChanges = false
//   let isAdmin = ALLOWED_EMAILS.includes(userEmail)
//   let unauthorizedChanges = [] // 普通用户违规修改的文件列表
//   let allProtectedChanges = [] // 受保护目录的所有变更

//   for (const line of changedFiles) {
//     if (!line) continue
//     const parts = line.split(/\s+/) // 分割出文件状态 (A/M/D/Rxxx) 和文件路径
//     const status = parts[0]
//     const file = parts[1]
//     const renameFile = parts[2] // 重命名文件时，第三个参数是重命名后的文件名

//     const statusText = STATUS_MAP[status]
//       ? STATUS_MAP[status]
//       : renameFile
//       ? STATUS_MAP.R
//       : "未知操作" // 映射状态码

//     // 检查是否是受保护目录下的文件
//     if (PROTECTED_PATHS.some(path => file.startsWith(path) || renameFile?.startsWith(path))) {
//       hasProtectedChanges = true
//       const changedFile = renameFile ? `${file} -> ${renameFile}` : file
//       allProtectedChanges.push(`${statusText}: ${changedFile}`)

//       if (!isAdmin) {
//         // **普通用户违规修改**
//         if (status === "M" || status === "D" || status.startsWith("R")) {
//           unauthorizedChanges.push(`${statusText}: ${changedFile}`)
//         }
//       }
//     }
//   }

//   // **如果普通用户有违规修改，列出所有违规文件并退出**
//   if (unauthorizedChanges.length > 0) {
//     console.error(`\n❌ 你没有权限修改或删除以下受保护文件:`)
//     unauthorizedChanges.forEach(file => console.error(`   🚫 ${file}`))
//     console.error(`\n🚨 受保护目录: ${PROTECTED_PATHS.join(", ")}`)
//     console.error(`📞 请联系管理员: ${ALLOWED_EMAILS.join(", ")}`)
//     process.exit(1)
//   }

//   // **所有人: 打印所有受保护目录的变更**
//   if (hasProtectedChanges) {
//     console.warn("\n👮 友情提醒：受保护的公共目录发生变更，请谨慎提交！")
//     console.warn("\n⚠️ 受保护目录变更总览:")
//     allProtectedChanges.forEach(file => console.warn(`   🔹 ${file}`))
//   } else {
//     console.log("✅ 没有受保护目录的变更, 继续提交...")
//   }
//   // 执行 lint-staged
//   execSync("npx --no-install npm run lint:lint-staged", { stdio: "inherit" })
//   // 执行 pretty
//   execSync("npx --no-install npm run lint:pretty", { stdio: "inherit" })
// } catch (error) {
//   console.error("❌ 发生错误:", error.message)
//   process.exit(1)
// }
