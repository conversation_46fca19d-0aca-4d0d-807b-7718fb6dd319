<script setup lang="ts">
import { useQueryForm } from "../../hooks/preference/useQueryForm"

defineOptions({
  name: "PreferenceQueryForm",
})

const { formModels, handleQuery, handleReset } = useQueryForm()

/**
 * 处理筛选表单提交
 */
function handleFinish() {
  handleQuery()
}

/**
 * 处理重置
 */
function handleResetForm() {
  handleReset()
}
</script>

<template>
  <xq-form-query
    label-width="100"
    :model="formModels"
    :form-layout="{ gutter: 20, span: 6 }"
    :default-rows-number="1"
    submitter-fixed
    @finish="handleFinish"
    @reset="handleResetForm"
  >
    <!-- 指标编码 -->
    <xq-form-layout-item label="指标编码">
      <xq-input
        v-model="formModels.indexCode"
        class="w-full"
        placeholder="请输入指标编码"
        clearable
      />
    </xq-form-layout-item>

    <!-- 指标管理部门 -->
    <xq-form-layout-item label="指标管理部门">
      <xq-input
        v-model="formModels.indexManageDept"
        class="w-full"
        placeholder="请输入指标管理部门"
        clearable
      />
    </xq-form-layout-item>

    <!-- 指标状态 -->
    <xq-form-layout-item label="指标状态">
      <xq-input
        v-model="formModels.vcItStatus"
        class="w-full"
        placeholder="请输入指标状态"
        clearable
      />
    </xq-form-layout-item>

    <!-- 查询区间 -->
    <xq-form-layout-item label="查询区间">
      <xq-date-picker
        v-model="formModels.queryDateRange"
        type="daterange"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="w-full"
        clearable
      />
    </xq-form-layout-item>
  </xq-form-query>
</template>
