import type { IconifyIcon } from "@iconify/vue"

export const routerArrays: Array<RouteConfigs> = []

export type RouteMetaType = {
  title?: string
  icon?: string | IconifyIcon
  showLink?: boolean
  savedPosition?: boolean
  auths?: Array<string>
}

export type RouteConfigs = {
  path?: string
  query?: object
  params?: object
  meta?: RouteMetaType
  children?: RouteConfigs[]
  name?: string
}

export type MultiTagsType = {
  tags: Array<RouteConfigs>
}

export type TagsViewsType = {
  icon: string | IconifyIcon
  text: string
  divided: boolean
  disabled: boolean
  show: boolean
}

export interface SetType {
  sidebar: {
    opened: boolean
    withoutAnimation: boolean
    isClickCollapse: boolean
  }
  device: string
  fixedHeader: boolean
  classes: {
    hideSidebar: boolean
    openSidebar: boolean
    withoutAnimation: boolean
    mobile: boolean
  }
  hideTabs: boolean
}

export type MenuType = {
  id?: number
  path?: string
  noShowingChildren?: boolean
  children?: MenuType[]
  value: unknown
  meta?: {
    icon?: string
    title?: string
    rank?: number
    showParent?: boolean
    extraIcon?: string
  }
  showTooltip?: boolean
  parentId?: number
  pathList?: number[]
  redirect?: string
}

export type ThemeColorsType = {
  color: string
  themeColor: string
}

export interface ScrollbarDomType extends HTMLElement {
  wrap?: {
    offsetWidth: number
  }
}
