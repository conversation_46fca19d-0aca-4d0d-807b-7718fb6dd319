import { usePreferenceStore } from "../../stores/preferenceStore"

/**
 * 偏好类指标明细查询表单钩子
 */
export function useQueryForm() {
  const store = usePreferenceStore()

  return {
    // 表单数据
    formModels: store.queryForm,

    // 加载状态
    listLoading: store.listLoading,
    exportLoading: store.exportLoading,

    // 表格数据
    tableData: store.tableData,
    total: store.total,

    // 分页
    pagination: store.pagination,

    // 方法
    handleQuery: store.handleQuery,
    handleReset: store.handleReset,
    handlePageChange: store.handlePageChange,
    handlePageSizeChange: store.handlePageSizeChange,
    handleExport: store.handleExport,
  }
}
