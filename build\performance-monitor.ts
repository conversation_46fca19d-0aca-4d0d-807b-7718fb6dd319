import { promises as fs } from "fs"
import path from "path"
import { performance } from "perf_hooks"

interface PerformanceMetrics {
  buildTime: number
  bundleSize: number
  chunkCount: number
  assetCount: number
  timestamp: string
}

interface PerformanceReport {
  current: PerformanceMetrics
  previous?: PerformanceMetrics
  improvement?: {
    buildTime: string
    bundleSize: string
  }
}

/**
 * 性能监控工具
 */
export class VitePerformanceMonitor {
  private metricsFile = "performance-metrics.json"
  private distDir = "dist"

  /**
   * 开始性能监控
   */
  startMonitoring(): number {
    return performance.now()
  }

  /**
   * 结束监控并生成报告
   */
  async endMonitoring(startTime: number): Promise<PerformanceReport> {
    const buildTime = performance.now() - startTime
    const bundleAnalysis = await this.analyzeBundles()

    const currentMetrics: PerformanceMetrics = {
      buildTime: Math.round(buildTime),
      bundleSize: bundleAnalysis.totalSize,
      chunkCount: bundleAnalysis.chunkCount,
      assetCount: bundleAnalysis.assetCount,
      timestamp: new Date().toISOString(),
    }

    const previousMetrics = await this.loadPreviousMetrics()
    const report = this.generateReport(currentMetrics, previousMetrics)

    await this.saveMetrics(currentMetrics)
    return report
  }

  /**
   * 分析构建产物
   */
  private async analyzeBundles() {
    const distPath = path.resolve(this.distDir)
    let totalSize = 0
    let chunkCount = 0
    let assetCount = 0

    try {
      const files = await this.getAllFiles(distPath)

      for (const file of files) {
        const stats = await fs.stat(file)
        totalSize += stats.size

        const ext = path.extname(file)
        if (ext === ".js") {
          chunkCount++
        } else {
          assetCount++
        }
      }
    } catch (error) {
      console.warn("Failed to analyze bundles:", error)
    }

    return {
      totalSize: Math.round(totalSize / 1024), // KB
      chunkCount,
      assetCount,
    }
  }

  /**
   * 递归获取所有文件
   */
  private async getAllFiles(dir: string): Promise<string[]> {
    const files: string[] = []

    try {
      const entries = await fs.readdir(dir, { withFileTypes: true })

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name)

        if (entry.isDirectory()) {
          files.push(...(await this.getAllFiles(fullPath)))
        } else {
          files.push(fullPath)
        }
      }
    } catch (error) {
      // 目录不存在或无法访问
    }

    return files
  }

  /**
   * 加载之前的性能指标
   */
  private async loadPreviousMetrics(): Promise<PerformanceMetrics | undefined> {
    try {
      const data = await fs.readFile(this.metricsFile, "utf-8")
      return JSON.parse(data)
    } catch {
      return undefined
    }
  }

  /**
   * 保存性能指标
   */
  private async saveMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      await fs.writeFile(this.metricsFile, JSON.stringify(metrics, null, 2))
    } catch (error) {
      console.warn("Failed to save metrics:", error)
    }
  }

  /**
   * 生成性能报告
   */
  private generateReport(
    current: PerformanceMetrics,
    previous?: PerformanceMetrics
  ): PerformanceReport {
    const report: PerformanceReport = { current }

    if (previous) {
      report.previous = previous
      report.improvement = {
        buildTime: this.calculateImprovement(previous.buildTime, current.buildTime),
        bundleSize: this.calculateImprovement(previous.bundleSize, current.bundleSize),
      }
    }

    return report
  }

  /**
   * 计算改进百分比
   */
  private calculateImprovement(oldValue: number, newValue: number): string {
    const improvement = ((oldValue - newValue) / oldValue) * 100
    const sign = improvement > 0 ? "+" : ""
    return `${sign}${improvement.toFixed(1)}%`
  }

  /**
   * 打印性能报告
   */
  printReport(report: PerformanceReport): void {
    console.log("\n📊 Vite 性能报告")
    console.log("==================")
    console.log(`🕐 构建时间: ${report.current.buildTime}ms`)
    console.log(`📦 包体积: ${report.current.bundleSize}KB`)
    console.log(`🔗 JS chunks: ${report.current.chunkCount}`)
    console.log(`📄 资源文件: ${report.current.assetCount}`)

    if (report.improvement) {
      console.log("\n📈 与上次构建对比:")
      console.log(`🕐 构建时间: ${report.improvement.buildTime}`)
      console.log(`📦 包体积: ${report.improvement.bundleSize}`)
    }

    console.log(`\n⏰ 时间戳: ${report.current.timestamp}`)
    console.log("==================\n")
  }
}

/**
 * Vite 插件版本的性能监控
 */
export function vitePerformancePlugin() {
  const monitor = new VitePerformanceMonitor()
  let startTime: number

  return {
    name: "vite-performance-monitor",
    buildStart() {
      startTime = monitor.startMonitoring()
    },
    async writeBundle() {
      const report = await monitor.endMonitoring(startTime)
      monitor.printReport(report)
    },
  }
}
