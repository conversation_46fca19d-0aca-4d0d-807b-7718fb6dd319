import { hasAuth, hasEveryAuth } from "@/utils/auth"
import type { Directive, DirectiveBinding } from "vue"

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const { value, modifiers } = binding
  if (value && Array.isArray(value) && value.length > 0) {
    const hasPermission = modifiers.every ? hasEveryAuth(value) : hasAuth(value)
    if (!hasPermission && el.parentNode) {
      el.parentNode.removeChild(el)
    }
  } else {
    throw new Error(`need permission code! Like v-auth="['app:moudle:action']"`)
  }
}

export const auth: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  },
}
