{"version": "2.0.0", "tasks": [{"label": "重启 TypeScript 服务器", "type": "shell", "command": "echo", "args": ["请手动按 Ctrl+Shift+P 并运行 'TypeScript: Restart TS Server'"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "修复 Auto Import 类型错误", "type": "shell", "command": "node", "args": ["scripts/fix-auto-import.js"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "重新生成 Auto Import 声明", "type": "shell", "command": "pnpm", "args": ["run", "dev"], "group": "build", "isBackground": true, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}