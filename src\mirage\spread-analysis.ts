// 利差分析业务模块
export const createSpreadAnalysisRoutes = (server: any) => {
  // 获取模板视图列表
  server.get("/api/spread-analysis/views", (schema, request) => {
    // 从数据库中获取所有模板视图
    return schema.db.views
  })
  // 保存模板视图模板（如果有 `id` 则为更新）
  server.post("/api/spread-analysis/views", (schema, request) => {
    const data = JSON.parse(request.requestBody)
    if (data.id) {
      // 更新操作
      return schema.db.views.update(data.id, data)
    } else {
      // 创建操作
      return schema.db.views.insert(data)
    }
  })
  // 删除模板视图模板
  server.delete("/api/spread-analysis/views/:id", (schema, request) => {
    const id = request.params.id
    return schema.db.views.remove(id)
  })
}
