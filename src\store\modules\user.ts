import { defineStore } from "pinia"
import getRouter, { resetRouter } from "@/router"
import { storageSession } from "@/utils/legacy"
import { getLogin, refreshTokenApi } from "@/api/user"
import { UserResult, RefreshTokenResult } from "@/api/user"
import { type DataInfo, setToken, removeToken, sessionKey } from "@/utils/auth"

export const useUserStore = defineStore("pure-user", {
  state: () => ({
    // 用户名
    username: storageSession().getItem<DataInfo<number>>(sessionKey)?.username ?? "",
    // 页面级别权限
    roles: storageSession().getItem<DataInfo<number>>(sessionKey)?.roles ?? [],
  }),
  actions: {
    /** 存储用户名 */
    SET_USERNAME(username: string) {
      this.username = username
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.roles = roles
    },
    /** 登入 */
    async loginByUsername(data) {
      return new Promise<UserResult>((resolve, reject) => {
        getLogin(data)
          .then(data => {
            if (data) {
              setToken(data.data)
              resolve(data)
            }
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    /** 前端登出（不调用接口） */
    logOut() {
      this.username = ""
      this.roles = []
      removeToken()
      resetRouter()
      getRouter().push("/login")
    },
    /** 刷新`token` */
    async handleRefreshToken(data) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshTokenApi(data)
          .then(data => {
            if (data) {
              setToken(data.data)
              resolve(data)
            }
          })
          .catch(error => {
            reject(error)
          })
      })
    },
  },
})
