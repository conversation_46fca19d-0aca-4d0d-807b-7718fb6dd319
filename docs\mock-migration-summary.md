# Mock 服务迁移完成

## 概述

本项目已成功将 Mock 服务从 `vite-plugin-mock` 迁移到 `MirageJS + Faker.js`。

## 变更内容

### 1. 依赖更新
- ✅ 移除 `vite-plugin-mock` 依赖
- ✅ 保留 `miragejs` 和 `@faker-js/faker` 依赖

### 2. 配置清理
- ✅ 删除 `build/plugins.ts` 中的 `viteMockServe` 配置
- ✅ 删除 `vite.config.ts` 中的相关配置
- ✅ 更新 `package.json` 中的 lint 脚本

### 3. 文件结构变更
- ✅ 删除 `mock/` 目录及其所有文件
- ✅ 删除 `src/mockProdServer.ts` 文件
- ✅ 保留 `src/mirage/` 目录结构

### 4. Mock 服务实现
- ✅ `src/mirage/system.ts` - 系统相关 Mock 接口
- ✅ `src/mirage/index.ts` - MirageJS 服务配置和路由集成
- ✅ `src/main.ts` - 开发环境自动启用 MirageJS

## 迁移后的 Mock 接口

### 系统接口
- `POST /login` - 用户登录
- `POST /refreshToken` - 刷新token
- `GET /user/permissions` - 获取用户权限
- `GET /getAsyncRoutes` - 获取异步路由
- `GET /user/info` - 获取用户信息
- `POST /logout` - 用户登出
- `POST /user/change-password` - 修改密码
- `GET /system/config` - 获取系统配置
- `GET /system/notifications` - 获取系统通知
- `PUT /system/notifications/:id/read` - 标记通知已读
- `GET /system/logs` - 获取操作日志

### 自定义视图接口
- `GET /custom-views` - 获取视图列表
- `POST /custom-views` - 创建视图
- `GET /custom-views/:id` - 获取单个视图
- `PUT /custom-views/:id` - 更新视图
- `DELETE /custom-views/:id` - 删除视图
- `DELETE /custom-views` - 批量删除视图
- `POST /custom-views/:id/copy` - 复制视图
- `PUT /custom-views/reorder` - 更新视图排序

## 使用方式

### 开发环境
Mock 服务在开发环境下自动启用，当 `VITE_USE_MOCK=on` 时生效。

```bash
# 设置环境变量启用 Mock
$env:VITE_USE_MOCK="on"  # Windows PowerShell
export VITE_USE_MOCK=on   # Linux/Mac

# 启动开发服务器
pnpm run dev
```

### 生产环境
生产环境不会启用 Mock 服务，确保生产环境的稳定性。

## 技术特点

1. **类型安全**: 使用 TypeScript 编写，提供完整的类型支持
2. **数据真实性**: 使用 Faker.js 生成真实的模拟数据
3. **接口一致性**: 保持与原 vite-plugin-mock 接口的完全兼容
4. **易于维护**: 集中管理所有 Mock 路由，便于扩展和维护
5. **开发友好**: 支持热重载，修改 Mock 数据立即生效

## 注意事项

- Mock 服务仅在开发环境下启用，生产环境会被自动禁用
- 所有 Mock 接口都在 `src/mirage/` 目录下管理
- 接口路径保持与原项目一致，无需修改前端调用代码
- 建议在添加新的 Mock 接口时，同时更新相应的类型定义

## 验证清单

- [x] 项目启动成功
- [x] TypeScript 编译通过
- [x] 登录接口正常工作
- [x] 权限接口正常工作
- [x] 自定义视图接口正常工作
- [x] 异步路由正常工作
- [x] 数据生成真实合理
- [x] 热重载功能正常
