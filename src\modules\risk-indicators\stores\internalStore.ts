import { computed, ref } from "vue"

import { defineStore } from "pinia"

import { useInternalService } from "@/services/useInternalService"

import type { InternalFormModel } from "../types"

/**
 * 内部管理风险指标明细 Store
 */
export const useInternalStore = defineStore("risk-indicators-internal", () => {
  const { useInternalList, useInternalExport } = useInternalService()

  // 表单数据
  const queryForm = ref<InternalFormModel>({
    vcFundCode: "",
    vcFundName: "",
    vcZhCode: "",
    vcZhName: "",
    vcManagerName: "",
    vcDeptName: "",
    vcItCode: "",
    vcItName: "",
    vcItStatus: "",
    queryDateRange: ["", ""],
    startDate: "",
    endDate: "",
  })

  // 分页参数
  const pagination = ref({
    page: 1,
    pageSize: 10,
  })

  // 组合查询参数
  const queryParams = computed(() => {
    const { queryDateRange, ...formData } = queryForm.value

    // 从 queryDateRange 中提取 startDate 和 endDate
    const [startDate, endDate] = queryDateRange || ["", ""]

    return {
      ...formData,
      startDate,
      endDate,
      ...pagination.value,
    }
  })

  // 是否启用查询
  const enableQuery = ref(true)

  // 获取列表数据
  const {
    data: listData,
    isLoading: listLoading,
    error: listError,
    refetch: refetchList,
  } = useInternalList(queryParams, {
    enabled: enableQuery,
  })

  // 导出功能
  const { mutate: exportData, isPending: exportLoading } = useInternalExport()

  // 表格数据
  const tableData = computed(() => {
    console.log("🔍 [Internal Store] listData.value:", listData.value)
    console.log(
      "🔍 [Internal Store] listData.value?.data:",
      listData.value?.data
    )
    console.log(
      "🔍 [Internal Store] listData.value?.data?.list:",
      listData.value?.data?.list
    )
    return listData.value?.data?.list || []
  })
  const total = computed(() => {
    const totalValue = listData.value?.data?.total || 0
    console.log("🔍 [Internal Store] total:", totalValue)
    return totalValue
  })

  /**
   * 执行查询
   */
  function handleQuery() {
    pagination.value.page = 1
    enableQuery.value = true
    refetchList()
  }

  /**
   * 重置查询条件
   */
  function handleReset() {
    queryForm.value = {
      vcFundCode: "",
      vcFundName: "",
      vcZhCode: "",
      vcZhName: "",
      vcManagerName: "",
      vcDeptName: "",
      vcItCode: "",
      vcItName: "",
      vcItStatus: "",
      queryDateRange: ["", ""],
      startDate: "",
      endDate: "",
    }
    pagination.value.page = 1
    enableQuery.value = false
  }

  /**
   * 分页变化
   */
  function handlePageChange(page: number) {
    pagination.value.page = page
    if (enableQuery.value) {
      refetchList()
    }
  }

  /**
   * 页面大小变化
   */
  function handlePageSizeChange(pageSize: number) {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
    if (enableQuery.value) {
      refetchList()
    }
  }

  /**
   * 导出数据
   */
  function handleExport() {
    exportData(queryForm.value, {
      onSuccess: data => {
        console.log("导出成功:", data)
        // 这里可以添加下载逻辑
      },
      onError: error => {
        console.error("导出失败:", error)
      },
    })
  }

  return {
    // 状态
    queryForm,
    pagination,
    queryParams,
    enableQuery,

    // 计算属性
    tableData,
    total,

    // 加载状态
    listLoading,
    exportLoading,
    listError,

    // 方法
    handleQuery,
    handleReset,
    handlePageChange,
    handlePageSizeChange,
    handleExport,
    refetchList,
  }
})
