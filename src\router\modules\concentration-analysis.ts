import Layout from "@/layout/LayoutProxy"

export default {
  path: "/concentration-analysis",
  name: "ConcentrationAnalysis",
  component: Layout,
  redirect: "/concentration-analysis/entity",
  meta: {
    icon: "homeFilled",
    title: "集中度分析",
  },
  children: [
    {
      path: "/concentration-analysis/entity",
      name: "ConcentrationAnalysisEntity",
      component: () =>
        import("@/modules/concentration-analysis/views/entity.vue"),
      meta: {
        title: "主体集中度",
      },
    },
    {
      path: "/concentration-analysis/asset",
      name: "ConcentrationAnalysisAsset",
      component: () =>
        import("@/modules/concentration-analysis/views/asset.vue"),
      meta: {
        title: "资产集中度",
      },
    },
    {
      path: "/concentration-analysis/single-bond",
      name: "ConcentrationAnalysisSingleBond",
      component: () =>
        import("@/modules/concentration-analysis/views/single-bond.vue"),
      meta: {
        title: "单券集中度",
      },
    },
    {
      path: "/concentration-analysis/partner-institution",
      name: "ConcentrationAnalysisPartnerInstitution",
      component: () =>
        import(
          "@/modules/concentration-analysis/views/partner-institution.vue"
        ),
      meta: {
        title: "合作机构集中度",
      },
    },
  ],
} as RouteConfigsTable
