import { ref } from "vue"
import type { InternalFormModel } from "../../types"

/**
 * 内部管理风险指标明细查询表单钩子
 */
export function useQueryForm() {
  // 表单模型
  const formModels = ref<InternalFormModel>({
    /** 产品代码 */
    vcFundCode: "",
    /** 产品名称 */
    vcFundName: "",
    /** 专户代码 */
    vcZhCode: "",
    /** 专户名称 */
    vcZhName: "",
    /** 投资经理 */
    vcManagerName: "",
    /** 投资部门 */
    vcDeptName: "",
    /** 指标编号 */
    vcItCode: "",
    /** 指标名称 */
    vcItName: "",
    /** 指标状态 */
    vcItStatus: "",
    /** 日期开始时间 */
    startDate: "",
    /** 日期结束时间 */
    endDate: "",
  })

  // TODO: 集成 store 相关逻辑
  // const { queryList } = useRiskIndicatorsInternalStore()

  return {
    formModels,
  }
}
