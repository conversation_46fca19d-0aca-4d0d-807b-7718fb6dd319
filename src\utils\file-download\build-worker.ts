/**
 * 构建时注入 Worker 代码的脚本
 */

import fs from "fs"
import path from "path"

// 读取 worker 源码
const workerPath = path.resolve(__dirname, "download-worker.ts")
const workerSource = fs.readFileSync(workerPath, "utf-8")

// 编译 TypeScript 为 JavaScript (简化版本，实际应该使用 TypeScript 编译器)
function compileWorkerCode(tsCode: string): string {
  // 这里应该使用正确的 TypeScript 编译器
  // 为了简化，我们直接移除类型注解
  return tsCode
    .replace(/import type .+/g, "")
    .replace(/: [A-Za-z<>[\]|]+/g, "")
    .replace(/interface .+\{[^}]+\}/gs, "")
    .replace(/declare .+/g, "")
}

const compiledWorkerCode = compileWorkerCode(workerSource)

// 更新主文件
const indexPath = path.resolve(__dirname, "index.ts")
let indexContent = fs.readFileSync(indexPath, "utf-8")

// 替换 Worker 代码占位符
indexContent = indexContent.replace(
  "// Worker code will be injected here during build",
  compiledWorkerCode.replace(/'/g, "\\'").replace(/\n/g, "\\n")
)

fs.writeFileSync(indexPath, indexContent)
