<!--
  异步文件下载示例组件
  展示如何优雅地处理后端文件生成场景
-->
<template>
  <div class="async-download-demo">
    <div class="download-section">
      <h3>异步文件下载示例</h3>
      <p class="description">模拟后端从云服务获取数据、处理并压缩后提供下载的场景</p>

      <!-- 下载按钮 -->
      <div class="action-buttons">
        <XqButton
          type="primary"
          :loading="isDownloading"
          :disabled="isDownloading"
          @click="handleAsyncDownload"
        >
          {{ isDownloading ? "处理中..." : "导出大数据文件" }}
        </XqButton>

        <XqButton v-if="isDownloading" type="danger" plain @click="handleCancel">取消</XqButton>
      </div>

      <!-- 状态显示区域 -->
      <div v-if="downloadStatus" class="status-section">
        <div class="status-card" :class="`status-${downloadStatus.status}`">
          <!-- 状态图标 -->
          <div class="status-icon">
            <i v-if="downloadStatus.status === 'pending'" class="el-icon-loading" />
            <i v-else-if="downloadStatus.status === 'processing'" class="el-icon-loading" />
            <i v-else-if="downloadStatus.status === 'ready'" class="el-icon-check" />
            <i v-else-if="downloadStatus.status === 'failed'" class="el-icon-close" />
          </div>

          <!-- 状态信息 -->
          <div class="status-info">
            <h4>{{ getStatusTitle(downloadStatus.status) }}</h4>
            <p>{{ downloadStatus.message }}</p>

            <!-- 预计时间 -->
            <p v-if="formatWaitTime" class="wait-time">
              {{ formatWaitTime }}
            </p>
          </div>
        </div>

        <!-- 文件生成进度 -->
        <div v-if="downloadStatus.status === 'processing'" class="progress-section">
          <div class="progress-header">
            <span>文件生成进度</span>
            <span class="progress-text">{{ formatGenerationProgress }}</span>
          </div>
          <el-progress
            :percentage="generationProgress"
            :stroke-width="8"
            :show-text="false"
            status="warning"
          />

          <!-- 进度步骤 -->
          <div class="progress-steps">
            <div class="step" :class="{ active: generationProgress >= 20 }">
              <i class="step-icon">1</i>
              <span>从云服务获取数据</span>
            </div>
            <div class="step" :class="{ active: generationProgress >= 60 }">
              <i class="step-icon">2</i>
              <span>数据处理与清洗</span>
            </div>
            <div class="step" :class="{ active: generationProgress >= 90 }">
              <i class="step-icon">3</i>
              <span>压缩文件生成</span>
            </div>
          </div>
        </div>

        <!-- 下载进度 -->
        <div v-if="downloadProgress" class="download-progress">
          <div class="progress-header">
            <span>下载进度</span>
            <span class="progress-text">{{ downloadProgress.percentage.toFixed(1) }}%</span>
          </div>
          <el-progress
            :percentage="downloadProgress.percentage"
            :stroke-width="8"
            :show-text="false"
            status="success"
          />
          <div class="download-info">
            <span>速度: {{ formatSpeed(downloadProgress.speed) }}</span>
            <span v-if="downloadProgress.remainingTime">
              剩余: {{ formatRemainingTime(downloadProgress.remainingTime) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 历史下载记录 -->
      <div v-if="downloadHistory.length" class="history-section">
        <h4>下载历史</h4>
        <div class="history-list">
          <div
            v-for="(record, index) in downloadHistory"
            :key="index"
            class="history-item"
            :class="{ success: record.success }"
          >
            <div class="history-info">
              <span class="filename">{{ record.filename }}</span>
              <span class="time">{{ formatTime(record.timestamp) }}</span>
            </div>
            <div class="history-stats">
              <span v-if="record.generationTime">生成: {{ record.generationTime }}ms</span>
              <span v-if="record.waitTime">总耗时: {{ record.waitTime }}ms</span>
            </div>
            <div class="history-status">
              <i
                :class="record.success ? 'el-icon-check text-success' : 'el-icon-close text-error'"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 帮助信息 -->
    <div class="help-section">
      <h4>使用说明</h4>
      <ul class="help-list">
        <li>点击"导出大数据文件"模拟异步文件生成场景</li>
        <li>系统会显示文件生成的实时进度</li>
        <li>生成完成后自动开始下载</li>
        <li>可以随时取消正在进行的操作</li>
        <li>支持多个文件同时处理（队列管理）</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"

import { useAsyncFileDownload } from "./async-file-downloader"
import {
  formatRemainingTime,
  formatSpeed,
} from "./utils"

// 使用异步下载组合式函数
const {
  isDownloading,
  downloadStatus,
  downloadProgress,
  generationProgress,
  download,
  cancel,
  formatGenerationProgress,
  formatWaitTime,
} = useAsyncFileDownload()

// 下载历史
const downloadHistory = ref<
  Array<{
    filename: string
    timestamp: number
    success: boolean
    generationTime?: number
    waitTime?: number
  }>
>([])

/**
 * 处理异步下载
 */
async function handleAsyncDownload() {
  try {
    // 模拟请求大数据导出接口
    const result = await download(
      "/api/export/large-dataset", // 后端会从云服务获取数据
      `数据导出_${new Date().toISOString().slice(0, 10)}.zip`,
      {
        pollInterval: 1500, // 1.5秒轮询一次
        maxWaitTime: 10 * 60 * 1000, // 最大等待10分钟
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
        },
      }
    )

    // 记录下载历史
    downloadHistory.value.unshift({
      filename: result.filename || "未知文件",
      timestamp: Date.now(),
      success: result.success,
      generationTime: result.generationTime,
      waitTime: result.waitTime,
    })

    if (result.success) {
      XqMessage.success(`文件下载完成: ${result.filename}`)
    } else {
      XqMessage.error(`下载失败: ${result.error}`)
    }
  } catch (error) {
    console.error("下载错误:", error)
    XqMessage.error(error.message || "下载失败")

    // 记录失败历史
    downloadHistory.value.unshift({
      filename: "下载失败",
      timestamp: Date.now(),
      success: false,
    })
  }
}

/**
 * 取消下载
 */
function handleCancel() {
  cancel()
  XqMessage.info("已取消下载")
}

/**
 * 获取状态标题
 */
function getStatusTitle(status: string): string {
  const titles = {
    pending: "准备中",
    processing: "文件生成中",
    ready: "准备就绪",
    failed: "生成失败",
  }
  return titles[status] || status
}

/**
 * 格式化时间
 */
function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleString()
}

/**
 * 获取认证令牌
 */
function getAuthToken(): string {
  // 实际项目中从存储中获取
  return localStorage.getItem("auth_token") || ""
}
</script>

<style scoped>
.async-download-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.download-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.status-section {
  margin-bottom: 24px;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  border-left: 4px solid;
}

.status-pending {
  background: #f0f9ff;
  border-left-color: #0ea5e9;
}

.status-processing {
  background: #fefce8;
  border-left-color: #eab308;
}

.status-ready {
  background: #f0fdf4;
  border-left-color: #22c55e;
}

.status-failed {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.status-icon {
  margin-right: 12px;
  font-size: 20px;
}

.status-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.status-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.wait-time {
  color: #0ea5e9 !important;
  font-weight: 500;
}

.progress-section {
  background: #f8fafc;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-weight: 600;
  color: #0ea5e9;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding: 0 10px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.step.active {
  opacity: 1;
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
}

.step.active .step-icon {
  background: #0ea5e9;
  color: white;
}

.step span {
  font-size: 12px;
  color: #6b7280;
}

.step.active span {
  color: #374151;
  font-weight: 500;
}

.download-progress {
  background: #f0fdf4;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #bbf7d0;
}

.download-info {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #059669;
}

.history-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.history-section h4 {
  margin: 0 0 16px 0;
  color: #374151;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f9fafb;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.history-item.success {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.history-info .filename {
  font-weight: 500;
  margin-right: 12px;
}

.history-info .time {
  font-size: 12px;
  color: #6b7280;
}

.history-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.history-status {
  font-size: 16px;
}

.text-success {
  color: #22c55e;
}

.text-error {
  color: #ef4444;
}

.help-section {
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.help-section h4 {
  margin: 0 0 12px 0;
  color: #374151;
}

.help-list {
  margin: 0;
  padding-left: 20px;
  color: #6b7280;
  line-height: 1.6;
}

.help-list li {
  margin-bottom: 4px;
}
</style>
