# 风险指标服务使用指南

本目录包含使用 `@tanstack/vue-query` 封装的风险指标相关服务。

## 服务列表

- `useComplianceService` - 监管合规风险指标明细服务
- `useInternalService` - 内部管理风险指标明细服务

## 使用方式

### 1. 基本查询

```typescript
<script setup lang="ts">
import { ref } from "vue"
import { useComplianceService } from "@/services"

const { useComplianceList } = useComplianceService()

// 查询参数
const queryParams = ref({
  page: 1,
  pageSize: 10,
  vcFundCode: "",
  vcManagerName: "",
})

// 获取列表数据
const { data, isLoading, error, refetch } = useComplianceList(queryParams)
</script>
```

### 2. 响应式查询参数

```typescript
<script setup lang="ts">
import { ref, computed } from "vue"
import { useComplianceService } from "@/services"

const { useComplianceList } = useComplianceService()

const searchForm = ref({
  vcFundCode: "",
  vcManagerName: "",
})

const pagination = ref({
  page: 1,
  pageSize: 10,
})

// 组合查询参数
const queryParams = computed(() => ({
  ...searchForm.value,
  ...pagination.value,
}))

// 查询会自动响应参数变化
const { data, isLoading } = useComplianceList(queryParams)
</script>
```

### 3. 条件查询

```typescript
<script setup lang="ts">
import { ref } from "vue"
import { useComplianceService } from "@/services"

const { useComplianceList } = useComplianceService()

const shouldFetch = ref(false)
const queryParams = ref({ page: 1, pageSize: 10 })

// 只有当 shouldFetch 为 true 时才执行查询
const { data, isLoading } = useComplianceList(queryParams, {
  enabled: shouldFetch.value,
})

// 手动触发查询
function handleSearch() {
  shouldFetch.value = true
}
</script>
```

### 4. 详情查询

```typescript
<script setup lang="ts">
import { ref } from "vue"
import { useComplianceService } from "@/services"

const { useComplianceDetail } = useComplianceService()

const selectedId = ref("")

// 获取详情数据
const { data: detail, isLoading } = useComplianceDetail(selectedId, {
  enabled: !!selectedId.value, // 只有当有ID时才查询
})
</script>
```

### 5. 导出功能

```typescript
<script setup lang="ts">
import { ref } from "vue"
import { useComplianceService } from "@/services"

const { useComplianceExport } = useComplianceService()

const exportParams = ref({
  vcFundCode: "",
  vcManagerName: "",
  startDate: "",
  endDate: "",
})

// 导出变更
const { mutate: exportData, isPending: isExporting } = useComplianceExport()

function handleExport() {
  exportData(exportParams.value, {
    onSuccess: (data) => {
      // 处理导出成功
      console.log("导出成功:", data)
      // 可以在这里触发文件下载
    },
    onError: (error) => {
      // 处理导出失败
      console.error("导出失败:", error)
    },
  })
}
</script>
```

### 6. 缓存管理

```typescript
<script setup lang="ts">
import { useComplianceService } from "@/services"

const { 
  invalidateComplianceList, 
  invalidateComplianceDetail,
  prefetchComplianceList 
} = useComplianceService()

// 刷新列表缓存
function refreshList() {
  invalidateComplianceList()
}

// 刷新特定详情缓存
function refreshDetail(id: string) {
  invalidateComplianceDetail(id)
}

// 预取数据
function preloadData() {
  prefetchComplianceList({ page: 1, pageSize: 10 })
}
</script>
```

## 最佳实践

### 1. 查询键管理

每个服务都提供了 `queryKeys` 对象，用于统一管理查询键：

```typescript
const { queryKeys } = useComplianceService()

// 访问查询键
console.log(queryKeys.all) // ["compliance"]
console.log(queryKeys.lists()) // ["compliance", "list"]
console.log(queryKeys.list({ page: 1 })) // ["compliance", "list", { page: 1 }]
```

### 2. 错误处理

```typescript
const { data, error, isError } = useComplianceList(queryParams)

// 在模板中处理错误
if (isError.value) {
  console.error("查询失败:", error.value)
}
```

### 3. 加载状态

```typescript
const { data, isLoading, isFetching } = useComplianceList(queryParams)

// isLoading: 首次加载
// isFetching: 任何时候的数据获取（包括后台刷新）
```

### 4. 缓存配置

```typescript
const { data } = useComplianceList(queryParams, {
  staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
  cacheTime: 10 * 60 * 1000, // 10分钟后清除缓存
})
```

## 注意事项

1. **响应式参数**: 当使用 `ref` 或 `computed` 作为查询参数时，查询会自动响应参数变化
2. **条件查询**: 使用 `enabled` 选项控制查询的执行时机
3. **缓存管理**: 合理使用缓存失效和预取功能来优化用户体验
4. **错误处理**: 始终处理可能的错误情况
5. **类型安全**: 所有服务都提供了完整的 TypeScript 类型支持
