import dayjs from "dayjs"
import { resolve } from "path"
import { ConfigEnv, loadEnv, UserConfigExport } from "vite"

import { wrapperEnv } from "./build"
import { exclude, include } from "./build/optimize"
import { getPluginsList } from "./build/plugins"
import pkg from "./package.json"
import namespace, { cssVarNS, cubeUIBizNS } from "./src/namespace"

/** 当前执行node命令时文件夹的地址（工作目录） */
const root: string = process.cwd()

/** 路径查找 */
const pathResolve = (dir: string): string => {
  return resolve(__dirname, ".", dir)
}

/** 设置别名 */
const alias: Record<string, string> = {
  "@": pathResolve("src"),
  "@build": pathResolve("build"),
}

const { dependencies, devDependencies, name, version } = pkg
const APP_INFO = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
}

export default ({ command, mode }: ConfigEnv): UserConfigExport => {
  const {
    VITE_CDN,
    VITE_PORT,
    VITE_COMPRESSION,
    VITE_PUBLIC_PATH,
    VITE_APP_NAME,
    VITE_APP_BASE_API,
  } = wrapperEnv(loadEnv(mode, root))

  const SCSS_VARS = [
    `$ns: ${namespace};`, // 配置给业务层使用 x-ui-plus namespace
    `$var-ns: ${cssVarNS};`, // 配置给业务层使用 x-ui-plus css var namespace
    `$ns-biz: ${cubeUIBizNS};`, // 配置给业务层使用 cube-ui-biz namespace
    `$xui-namespace: $ns;`,
    `$xui-css-var-namespace: $var-ns;`,
    `@forward "@xquant/x-ui-plus/theme-chalk/src/mixins/config.scss" with (
      $namespace: $ns,
      $var-namespace: $var-ns
    );`, // 配置 x-ui namespace
    `@forward "@cube-ui/biz/styles/src/mixins/config.scss" with (
      $namespaceBiz: $ns-biz,
    );`, // 配置 @cube-ui/biz Css namespace
    `$app: #app-${VITE_APP_NAME};`, // 配置子应用入口
  ]

  // 是否为生产环境
  const isProd = mode === "production"

  return {
    base: VITE_PUBLIC_PATH,
    // base: `http://localhost:${VITE_PORT}/`,
    root,
    resolve: {
      alias,
    },
    // 服务端渲染
    server: {
      // 端口号
      port: VITE_PORT,
      host: "0.0.0.0",
      // HMR 优化
      hmr: {
        overlay: !isProd, // 生产环境禁用错误遮罩
      },
      // 本地跨域代理 https://cn.vitejs.dev/config/server-options.html#server-proxy
      proxy: {
        [VITE_APP_BASE_API]: {
          // 根据环境变量决定使用 Mockoon 还是真实服务器
          target:
            process.env.VITE_USE_MOCK === "on"
              ? "http://localhost:3001/" // Mockoon 服务地址
              : "http://***********:8988/", // 公司dev
          changeOrigin: true,
          // rewrite: path => path.replace(/^\/base_api/, ""),
          rewrite: path =>
            path.replace(new RegExp(`^${VITE_APP_BASE_API}`), ""),
        },
      },
    },
    plugins: getPluginsList(command, VITE_CDN, VITE_COMPRESSION, VITE_APP_NAME),
    // https://cn.vitejs.dev/config/dep-optimization-options.html#dep-optimization-options
    optimizeDeps: {
      include,
      exclude,
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: SCSS_VARS.join(""),
          charset: false,
          // 静默 SCSS deprecation 警告以提升编译速度
          quietDeps: true,
          silenceDeprecations: ["legacy-js-api"],
        },
      },
      devSourcemap: false, // 开发环境禁用 CSS sourcemap 提升速度
    },
    build: {
      // 启用css代码拆分以提升缓存效率
      cssCodeSplit: true,
      sourcemap: false,
      // 消除打包大小超过500kb警告
      chunkSizeWarningLimit: 4000,
      // Terser 优化
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: isProd,
          drop_debugger: isProd,
          pure_funcs: isProd ? ["console.log"] : [],
        },
      },
      rollupOptions: {
        input: {
          index: pathResolve("index.html"),
        },
        output: {
          // 智能分包策略
          manualChunks: {
            // 框架核心
            vue: ["vue", "vue-router", "pinia"],
            // UI 组件库
            "ui-lib": ["@xquant/x-ui-plus", "@cube-ui/biz"],
            // 工具库
            utils: ["es-toolkit", "dayjs", "axios"],
            // 图表库
            charts: ["echarts"],
            // 第三方工具
            vendor: ["qs", "mitt", "nprogress"],
          },
          // 静态资源分类打包
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]",
        },
      },
    },
    define: {
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(APP_INFO),
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: mode !== "production",
    },
  }
}
