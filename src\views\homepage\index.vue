<script setup lang="tsx">
import { ref } from "vue"

// TSX 配置测试
const message = ref("Hello Xquant")
const count = ref(0)

// 测试渲染函数 - 使用 TSX 语法
const renderTestComponent = () => {
  return (
    <div class='rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white shadow-lg'>
      <h2 class='mb-4 text-2xl font-bold'>TSX 功能测试</h2>
      <p class='mb-4'>当前计数: {count.value}</p>
      <button
        class='rounded bg-white px-4 py-2 text-blue-600 transition-colors hover:bg-gray-100'
        onClick={() => count.value++}
      >
        点击增加计数
      </button>
    </div>
  )
}

// 测试复杂 TSX 组件
const renderAdvancedTest = () => {
  const items = ["TypeScript", "Vue 3", "JSX/TSX", "Vite", "TailwindCSS"]

  return (
    <div class='mt-6 rounded-lg border border-gray-200 p-4'>
      <h3 class='mb-3 text-lg font-semibold text-gray-800'>技术栈列表</h3>
      <ul class='space-y-2'>
        {items.map((item, index) => (
          <li key={index} class='flex items-center space-x-2 text-gray-600'>
            <span class='h-2 w-2 rounded-full bg-green-500'></span>
            <span>{item}</span>
          </li>
        ))}
      </ul>
    </div>
  )
}
</script>

<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 原始模板内容 -->
    <div class="mb-8 text-center text-4xl text-blue-500">{{ message }}</div>

    <!-- TSX 渲染函数测试 -->
    <div class="mx-auto max-w-2xl space-y-6">
      <div class="text-center">
        <h1 class="mb-2 text-3xl font-bold text-gray-800">TSX 配置验证</h1>
        <p class="text-gray-600">测试 Vue 3 + TypeScript + JSX 功能</p>
      </div>

      <!-- 使用 TSX 渲染函数 -->
      <component :is="renderTestComponent" />

      <!-- 使用复杂 TSX 组件 -->
      <component :is="renderAdvancedTest" />

      <!-- 状态显示 -->
      <div class="mt-8 text-center text-sm text-gray-500">
        <p>✅ TSX 配置正常工作</p>
        <p>✅ 响应式数据绑定正常</p>
        <p>✅ 事件处理正常</p>
        <p>✅ 条件渲染和列表渲染正常</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 可以添加额外的样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}
</style>
