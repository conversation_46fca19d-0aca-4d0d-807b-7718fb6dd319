<script setup lang="ts">
import { useQueryForm } from "../../hooks/compliance/useQueryForm"

defineOptions({
  name: "ComplianceTable",
})

const { tableData, total, pagination, listLoading, handlePageChange, handlePageSizeChange } =
  useQueryForm()

// 表头单元格样式配置
const getHeaderCellClassName = ({ column }: { column: any; columnIndex: number }) => {
  const prop = column.property

  // 产品信息相关列 - 蓝色系表头
  if (prop === "vcFundCode" || prop === "vcFundName" || prop === "vcManagerName") {
    return "product-header"
  }

  // 指标基础信息相关列 - 橙色系表头
  if (prop === "vcItCode" || prop === "vcItName" || prop === "vcItTypeName") {
    return "indicator-header"
  }

  // 指标状态相关列 - 绿色系表头
  if (prop === "vcItStatus" || prop === "vcCheckedStatus") {
    return "status-header"
  }

  return ""
}

// 单元格样式配置
const getCellClassName = ({ column, rowIndex }: { column: any; rowIndex: number }) => {
  const prop = column.property
  const isOddRow = rowIndex % 2 === 1

  // 产品信息相关列 - 蓝色系单元格
  if (prop === "vcFundCode" || prop === "vcFundName" || prop === "vcManagerName") {
    return isOddRow ? "product-cell-striped" : "product-cell"
  }

  // 指标基础信息相关列 - 橙色系单元格
  if (prop === "vcItCode" || prop === "vcItName" || prop === "vcItTypeName") {
    return isOddRow ? "indicator-cell-striped" : "indicator-cell"
  }

  // 指标状态相关列 - 绿色系单元格
  if (prop === "vcItStatus" || prop === "vcCheckedStatus") {
    return isOddRow ? "status-cell-striped" : "status-cell"
  }

  // 其他列的斑马纹
  return isOddRow ? "normal-cell-striped" : ""
}
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- 表格 -->
    <xq-table
      :data="tableData"
      :loading="listLoading"
      class="flex-1"
      :stripe="false"
      border
      :header-cell-class-name="getHeaderCellClassName"
      :cell-class-name="getCellClassName"
      style="width: 100%"
    >
      <!-- 产品信息分组 -->
      <xq-table-column label="产品信息" align="center" class-name="product-group-header">
        <xq-table-column prop="vcFundCode" label="产品代码" min-width="120" align="center" />
        <xq-table-column prop="vcFundName" label="产品名称" min-width="150" align="left" />
        <xq-table-column prop="vcManagerName" label="投资经理" min-width="100" align="center" />
      </xq-table-column>

      <!-- 指标基础信息分组 -->
      <xq-table-column label="指标基础信息" align="center" class-name="indicator-group-header">
        <xq-table-column prop="vcItCode" label="指标编号" min-width="120" align="center" />
        <xq-table-column prop="vcItName" label="指标名称" min-width="200" align="left" />
        <xq-table-column prop="vcItTypeName" label="指标类型" min-width="100" align="center" />
      </xq-table-column>

      <!-- 指标状态信息分组 -->
      <xq-table-column label="指标状态信息" align="center" class-name="status-group-header">
        <xq-table-column prop="vcItStatus" label="指标状态" min-width="100" align="center" />
        <xq-table-column prop="vcCheckedStatus" label="处理状态" min-width="100" align="center" />
      </xq-table-column>

      <!-- 指标数值信息 -->
      <xq-table-column prop="vcOutlineNum" label="限额值" min-width="100" align="right" />
      <xq-table-column prop="vcWarnNum" label="预警值" min-width="100" align="right" />
      <xq-table-column prop="fDactualMoney" label="使用值" min-width="100" align="right" />
      <xq-table-column prop="fDactualValue" label="使用比例" min-width="100" align="right" />
    </xq-table>

    <!-- 分页 -->
    <div class="flex justify-end mt-4">
      <xq-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 产品信息相关样式 - 蓝色系 */
:deep(.product-group-header) {
  background-color: #dbeafe !important;
  color: #1e40af !important;
  font-weight: 600;
}

:deep(.product-header) {
  background-color: #bfdbfe !important;
  color: #1d4ed8 !important;
  font-weight: 500;
}

:deep(.product-cell) {
  background-color: #f0f9ff !important;
  color: #1e40af;
}

:deep(.product-cell-striped) {
  background-color: #e0f2fe !important;
  color: #1e40af;
}

/* 指标基础信息相关样式 - 橙色系 */
:deep(.indicator-group-header) {
  background-color: #fed7aa !important;
  color: #c2410c !important;
  font-weight: 600;
}

:deep(.indicator-header) {
  background-color: #fdba74 !important;
  color: #ea580c !important;
  font-weight: 500;
}

:deep(.indicator-cell) {
  background-color: #fff7ed !important;
  color: #c2410c;
}

:deep(.indicator-cell-striped) {
  background-color: #ffedd5 !important;
  color: #c2410c;
}

/* 指标状态相关样式 - 绿色系 */
:deep(.status-group-header) {
  background-color: #dcfce7 !important;
  color: #14532d !important;
  font-weight: 600;
}

:deep(.status-header) {
  background-color: #bbf7d0 !important;
  color: #15803d !important;
  font-weight: 500;
}

:deep(.status-cell) {
  background-color: #f0fdf4 !important;
  color: #14532d;
}

:deep(.status-cell-striped) {
  background-color: #ecfdf5 !important;
  color: #14532d;
}

/* 普通列的斑马纹 */
:deep(.normal-cell-striped) {
  background-color: #f9fafb !important;
}

/* 悬停效果 */
:deep(.xq-table__row:hover td) {
  background-color: rgba(59, 130, 246, 0.05) !important;
}

/* 表头居中对齐 */
:deep(.#{$ns}-table__header .#{$ns}-table__cell > .cell) {
  text-align: center;
  justify-content: center;
}

/* 分组表头居中对齐 */
:deep(.#{$ns}-table__header-wrapper .#{$ns}-table__header .#{$ns}-table__cell) {
  text-align: center;
}

/* 确保表格占满容器宽度 */
:deep(.#{$ns}-table) {
  width: 100% !important;
}

/* 表格列自适应 */
:deep(.#{$ns}-table__body-wrapper) {
  width: 100%;
}
</style>
