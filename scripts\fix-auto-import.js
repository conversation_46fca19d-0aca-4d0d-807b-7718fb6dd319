#!/usr/bin/env node

/**
 * Auto Import 类型错误修复脚本
 * 用于解决 VS Code 中 "cannot find name 'xxx'" 的问题
 */

const fs = require('fs');
const path = require('path');

// 切换到项目根目录
process.chdir(path.dirname(__dirname));

console.log('🔧 开始修复 Auto Import 类型错误...\n');
console.log('当前工作目录:', process.cwd());

// 1. 检查必要文件是否存在
const requiredFiles = [
  'auto-imports.d.ts',
  'components.d.ts',
  'tsconfig.json',
  '.eslintrc-auto-import.json'
];

console.log('1. 检查必要文件...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} 存在`);
  } else {
    console.log(`   ❌ ${file} 不存在`);
  }
});

// 2. 检查 tsconfig.json 配置
console.log('\n2. 检查 tsconfig.json 配置...');
try {
  const tsconfigPath = path.resolve('tsconfig.json');
  const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));

  const requiredIncludes = ['auto-imports.d.ts', 'components.d.ts'];
  const includes = tsconfig.include || [];

  requiredIncludes.forEach(file => {
    if (includes.includes(file)) {
      console.log(`   ✅ ${file} 已包含在 include 中`);
    } else {
      console.log(`   ❌ ${file} 未包含在 include 中`);
    }
  });
} catch (error) {
  console.log(`   ❌ 读取 tsconfig.json 失败: ${error.message}`);
}

// 3. 检查 auto-imports.d.ts 文件格式
console.log('\n3. 检查 auto-imports.d.ts 文件格式...');
try {
  const autoImportsPath = path.resolve('auto-imports.d.ts');
  if (fs.existsSync(autoImportsPath)) {
    const content = fs.readFileSync(autoImportsPath, 'utf8');

    if (content.includes('declare global')) {
      console.log('   ✅ auto-imports.d.ts 包含 declare global 声明');
    } else {
      console.log('   ❌ auto-imports.d.ts 缺少 declare global 声明');
    }

    if (content.includes('export {}')) {
      console.log('   ✅ auto-imports.d.ts 包含 export {} 声明');
    } else {
      console.log('   ❌ auto-imports.d.ts 缺少 export {} 声明');
    }
  }
} catch (error) {
  console.log(`   ❌ 检查 auto-imports.d.ts 失败: ${error.message}`);
}

console.log('\n🔧 修复建议:');
console.log('1. 确保在 VS Code 中按 Ctrl+Shift+P，运行 "TypeScript: Restart TS Server"');
console.log('2. 检查 VS Code 的 TypeScript 版本是否为最新');
console.log('3. 确保项目中的 unplugin-auto-import 版本为最新');
console.log('4. 如果问题仍然存在，尝试删除 node_modules 和 pnpm-lock.yaml 重新安装依赖');

console.log('\n✨ 修复脚本执行完成！');
