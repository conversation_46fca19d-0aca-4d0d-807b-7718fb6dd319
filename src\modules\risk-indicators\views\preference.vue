<script setup lang="ts">
import {
  defineAsyncComponent,
  Suspense,
} from "vue"

import ModuleLoading from "@/components/module-loading/index.vue"

// 异步加载组件
const QueryForm = defineAsyncComponent(() => import("../components/preference/query-form.vue"))
const Table = defineAsyncComponent(() => import("../components/preference/table.vue"))

defineOptions({
  name: "RiskIndicatorsPreference",
})
</script>

<template>
  <xq-card
    class="h-full !overflow-y-scroll border-none"
    body-class="!p-[12px] h-full"
    shadow="never"
  >
    <div class="flex h-full flex-col">
      <module-title title="偏好类指标明细" />
      <div class="mt-[8px] flex-1 bg-[var(--xq-color-info-light-9)] p-[12px]">
        <div class="flex h-full flex-col space-y-[12px]">
          <!-- 查询表单 -->
          <Suspense>
            <QueryForm />
            <template #fallback>
              <ModuleLoading :height="120" />
            </template>
          </Suspense>

          <!-- 表格区域 -->
          <module-panel class="flex-1">
            <Suspense>
              <Table />
              <template #fallback>
                <ModuleLoading :height="400" />
              </template>
            </Suspense>
          </module-panel>
        </div>
      </div>
    </div>
  </xq-card>
</template>
