import { http } from "@/utils/http"

import type {
  ComplianceFormModel,
  ComplianceTableData,
} from "../types"

/**
 * 监管合规风险指标查询参数
 */
export interface ComplianceQueryParams extends ComplianceFormModel {
  page: number
  pageSize: number
}

/**
 * 监管合规风险指标查询响应
 */
export interface ComplianceQueryResponse {
  data: ComplianceTableData[]
  total: number
  page: number
  pageSize: number
}

/**
 * 获取监管合规风险指标列表
 */
export async function getComplianceList(
  params: ComplianceQueryParams
): Promise<ComplianceQueryResponse> {
  const response = (await http.get("/api/risk-indicators/compliance/list", {
    params,
  })) as any
  return response.data || response
}

/**
 * 导出监管合规风险指标数据
 */
export async function exportComplianceData(
  params: ComplianceFormModel
): Promise<Blob> {
  const response = (await http.get("/api/risk-indicators/compliance/export", {
    params,
    responseType: "blob",
  })) as any
  return response.data || response
}
