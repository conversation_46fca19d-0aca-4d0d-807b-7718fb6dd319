{"name": "unified-risk-platform", "version": "4.5.0", "packageManager": "pnpm@7.3.0", "private": true, "scripts": {"dev": "vite", "serve": "pnpm dev", "build": "rimraf dist && vite build", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f src/assets/svg -o src/assets/svg", "cloc": "cloc . --exclude-dir=node_modules --exclude-lang=YAML", "clean:cache": "rimraf node_modules && rimraf .eslintcache && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0 \"src/**/*.{vue,js,ts,tsx}\" \"build/**/*.{js,ts}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint \"**/*.{html,vue,css,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky install", "preinstall": "npx only-allow pnpm"}, "browserslist": ["chrome 64", "not ie 11", "not op_mini all"], "dependencies": {"@cube-ui/biz": "^1.0.4", "@tanstack/vue-query": "^5.80.6", "@tanstack/vue-query-devtools": "^5.80.6", "@vueuse/core": "^13.3.0", "@xquant/x-ui-plus": "^2.2.0", "@xquant/x-ui-plus-icons-vue": "^3.0.0", "@xquant/xp-admin-layout": "^3.0.1", "axios": "^1.9.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "es-toolkit": "1.38.0", "js-file-download": "^0.4.12", "mitt": "^3.0.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.3", "portal-vue": "^3.0.0", "qs": "^6.14.0", "tailwind-merge": "^3.3.0", "vue": "^3.5.16", "vue-echarts": "^7.0.3", "vue-loading-overlay": "^6.0", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@faker-js/faker": "^9.8.0", "@iconify/vue": "^5.0.0", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^22.15.29", "@types/nprogress": "0.2.0", "@types/qs": "^6.14.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@xquant/x-ui-plus-resolver": "1.0.2", "autoprefixer": "^10.4.21", "cloc": "^2.11.0", "cssnano": "^6.1.2", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^9.29.1", "husky": "^9.1.7", "lint-staged": "^16.1.0", "miragejs": "^0.1.48", "picocolors": "^1.1.1", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-import": "^15.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "pretty-quick": "^4.2.2", "rimraf": "^5.0.10", "rollup-plugin-visualizer": "^6.0.1", "sass": "^1.89.1", "sass-loader": "^13.3.3", "stylelint": "^16.20.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended": "^16.0.0", "stylelint-config-recommended-scss": "^15.0.1", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-order": "^6.0.4", "stylelint-prettier": "^3.0.0", "stylelint-scss": "^6.12.0", "svgo": "^3.3.2", "tailwindcss": "^4.1.10", "terser": "^5.40.0", "typescript": "5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression2": "^1.4.0", "vite-plugin-qiankun-lite": "^1.1.2", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.5", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.2.10"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["rollup", "webpack", "core-js"]}, "allowedDeprecatedVersions": {"sourcemap-codec": "*", "w3c-hr-time": "*", "stable": "*"}}, "license": "ISC", "volta": {"node": "20.19.3"}}