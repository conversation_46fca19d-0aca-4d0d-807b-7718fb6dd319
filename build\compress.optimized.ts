import type { Plugin } from "vite"
import { compression } from "vite-plugin-compression2"

// 优化版本的压缩插件配置
export const configCompressPlugin = (compress: ViteCompression): Plugin | Plugin[] | null => {
  if (compress === "none") return null

  // 检查是否需要删除原始文件
  const deleteOriginalAssets = compress.includes("clear")

  // 基础配置 - 优化版本
  const baseConfig = {
    threshold: 1024, // 调整：只压缩大于 1KB 的文件
    deleteOriginalAssets,
    skipIfLargerOrEqual: true, // 如果压缩后文件更大则跳过
    // 新增：文件过滤优化
    include: /\.(js|css|html|svg)$/i,
    exclude: /\.(png|jpg|jpeg|gif|ico|woff|woff2|ttf|eot)$/i,
  }

  const plugins: Plugin[] = []

  // 根据配置类型选择算法
  if (compress.includes("gzip") || compress.includes("both")) {
    // 添加 gzip 压缩 - 优化版本
    plugins.push(
      compression({
        ...baseConfig,
        algorithm: "gzip",
        compressionOptions: {
          level: 9, // 最高压缩级别
          // 新增：gzip 优化选项
          windowBits: 15,
          memLevel: 8,
        },
      })
    )
  }

  if (compress.includes("brotli") || compress.includes("both")) {
    // 添加 brotli 压缩 - 优化版本
    plugins.push(
      compression({
        ...baseConfig,
        algorithm: "brotliCompress",
        compressionOptions: {
          params: {
            [require("zlib").constants.BROTLI_PARAM_QUALITY]: 11, // 最高质量
            // 新增：brotli 优化选项
            [require("zlib").constants.BROTLI_PARAM_MODE]:
              require("zlib").constants.BROTLI_MODE_TEXT,
          },
        },
      })
    )
  }

  return plugins.length === 1 ? plugins[0] : plugins.length > 1 ? plugins : null
}
