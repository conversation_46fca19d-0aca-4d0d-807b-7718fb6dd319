/**
 * 表格分组样式工具函数
 * 用于统一处理分组表头和单元格的样式类名
 */

/**
 * 表格分组配置类型定义
 */
export interface TableGroupConfig {
  /** 第一个分组标签 */
  groupOneLabel?: string
  /** 第二个分组标签 */
  groupTwoLabel?: string
  /** 第三个分组标签 */
  groupThreeLabel?: string
}

/**
 * 创建自定义分组的样式函数
 * @param config 分组配置，最多支持3个分组
 * @returns 样式函数对象
 */
export function createGroupedTableStyleFunctions(
  config: TableGroupConfig = {}
) {
  const {
    groupOneLabel = "产品信息",
    groupTwoLabel = "指标基础信息",
    groupThreeLabel = "指标使用情况",
  } = config

  // 分组标签到样式类名的映射
  const groupLabelToStyleMap = {
    [groupOneLabel]: {
      groupHeader: "group-one-header",
      header: "one-header",
      cell: "one-cell",
      cellStriped: "one-cell-striped",
    },
    [groupTwoLabel]: {
      groupHeader: "group-two-header",
      header: "two-header",
      cell: "two-cell",
      cellStriped: "two-cell-striped",
    },
    [groupThreeLabel]: {
      groupHeader: "group-three-header",
      header: "three-header",
      cell: "three-cell",
      cellStriped: "three-cell-striped",
    },
  }

  return {
    getHeaderCellClassName: ({
      column,
    }: {
      column: any
      columnIndex?: number
    }) => {
      if (column.level === 1) {
        // 第一级表头（分组表头）
        const label = column.label
        const styleConfig = groupLabelToStyleMap[label]
        return styleConfig?.groupHeader || ""
      }

      // 第二级表头（子表头）
      const parentLabel = column.parent?.label
      const styleConfig = groupLabelToStyleMap[parentLabel]
      return styleConfig?.header || ""
    },

    getCellClassName: ({
      column,
      rowIndex,
    }: {
      column: any
      row?: any
      rowIndex: number
    }) => {
      const isOddRow = rowIndex % 2 === 1
      const parentLabel = column.parent?.label
      const styleConfig = groupLabelToStyleMap[parentLabel]

      if (styleConfig) {
        return isOddRow ? styleConfig.cellStriped : styleConfig.cell
      }

      // 其他列的斑马纹
      return isOddRow ? "normal-cell-striped" : ""
    },
  }
}
