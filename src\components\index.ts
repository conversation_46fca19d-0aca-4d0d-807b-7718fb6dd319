import ModuleIconButton from "@/components/module-icon-button/index.vue"
import ModuleLoading from "@/components/module-loading/index.vue"
import ModulePanel from "@/components/module-panel/index.vue"
import ModuleTitle from "@/components/module-title/index.vue"
import SvgIcon from "@/components/SvgIcon/index.vue"

const components = [
  SvgIcon,
  ModuleLoading,
  ModuleTitle,
  ModuleIconButton,
  ModulePanel,
]

export default {
  install(app) {
    // 注册组件
    components.forEach(component => {
      app.component(component.name as string, component)
    })
  },
}
