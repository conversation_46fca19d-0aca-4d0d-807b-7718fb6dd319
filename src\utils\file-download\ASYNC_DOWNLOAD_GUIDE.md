# 异步文件下载快速入门指南

## 🎯 什么是异步文件下载？

异步文件下载专门解决这样的场景：
- 用户点击"导出"按钮
- 后端需要从云服务获取数据
- 需要对数据进行处理、清洗、压缩
- 整个过程需要几分钟时间
- 前端需要优雅地等待并显示进度

## 🚀 快速开始

### 1. 基础使用

```typescript
import { downloadAsyncFile } from '@/utils/file-download'

// 发起异步文件生成请求
const result = await downloadAsyncFile(
  '/api/export/large-dataset',  // 后端接口
  'export-data.zip'             // 文件名
)

if (result.success) {
  console.log('下载成功')
} else {
  console.error('下载失败:', result.error)
}
```

### 2. 带进度监控

```typescript
const result = await downloadAsyncFile(
  '/api/export/large-dataset',
  'export-data.zip',
  {
    onStatusChange: (status) => {
      console.log('状态:', status.message)
    },
    onGenerationProgress: (progress) => {
      console.log('生成进度:', progress + '%')
    },
    onProgress: (progress) => {
      console.log('下载进度:', progress.percentage + '%')
    }
  }
)
```

### 3. Vue 组件中使用

```vue
<script setup>
import { useAsyncFileDownload } from '@/utils/file-download/async-file-downloader'

const {
  isDownloading,
  downloadStatus,
  downloadProgress,
  generationProgress,
  download,
  cancel
} = useAsyncFileDownload()

async function handleExport() {
  await download('/api/export/data', 'export.zip')
}
</script>

<template>
  <div>
    <button @click="handleExport" :disabled="isDownloading">
      {{ isDownloading ? '导出中...' : '导出数据' }}
    </button>
    
    <button v-if="isDownloading" @click="cancel">
      取消
    </button>
    
    <div v-if="downloadStatus">
      <p>{{ downloadStatus.message }}</p>
      <progress v-if="generationProgress" :value="generationProgress" max="100" />
    </div>
  </div>
</template>
```

## 🔧 后端接口要求

### 1. 发起文件生成请求

```http
POST /api/export/large-dataset
```

**请求体：**
```json
{
  "dataType": "user_data",
  "filters": { "dateRange": "2024-01-01,2024-12-31" }
}
```

**响应：**
```json
{
  "success": true,
  "taskId": "task_123456",
  "statusUrl": "/api/export/status/task_123456"
}
```

### 2. 查询任务状态

```http
GET /api/export/status/:taskId
```

**响应（处理中）：**
```json
{
  "status": "processing",
  "progress": 45,
  "message": "正在处理数据...",
  "estimatedTime": 120
}
```

**响应（完成）：**
```json
{
  "status": "ready",
  "progress": 100,
  "message": "文件生成完成",
  "downloadUrl": "/api/download/task_123456/export.zip"
}
```

### 3. 下载文件

```http
GET /api/download/:taskId/:filename
```

返回文件流，支持断点续传。

## 📋 完整示例

### 前端代码

```typescript
// 导出管理器
class DataExportManager {
  async exportUserData(filters: any) {
    try {
      // 显示开始提示
      showMessage('正在准备导出数据...')
      
      const result = await downloadAsyncFile(
        '/api/export/user-data',
        `用户数据_${new Date().toISOString().slice(0, 10)}.zip`,
        {
          // 配置
          pollInterval: 2000,          // 2秒轮询
          maxWaitTime: 10 * 60 * 1000, // 10分钟超时
          
          // 请求数据
          headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
          },
          
          // 状态回调
          onStatusChange: (status) => {
            switch (status.status) {
              case 'pending':
                showMessage('正在发起数据导出请求...')
                break
              case 'processing':
                showMessage(`正在处理数据: ${status.progress}%`)
                showProgress(status.progress)
                break
              case 'ready':
                showMessage('数据处理完成，开始下载...')
                break
              case 'failed':
                showError(status.error)
                break
            }
          },
          
          // 下载进度
          onProgress: (progress) => {
            showMessage(`下载进度: ${progress.percentage.toFixed(1)}%`)
            showDownloadProgress(progress.percentage)
          }
        }
      )
      
      if (result.success) {
        showSuccess(`导出成功！处理耗时: ${Math.round(result.generationTime / 1000)}秒`)
      } else {
        showError(`导出失败: ${result.error}`)
      }
      
    } catch (error) {
      showError(`导出失败: ${error.message}`)
    }
  }
}

// 使用示例
const exportManager = new DataExportManager()
await exportManager.exportUserData({
  dateRange: '2024-01-01,2024-12-31',
  userTypes: ['premium', 'standard']
})
```

### 后端代码（Node.js/Express）

```javascript
// 文件生成请求
app.post('/api/export/user-data', async (req, res) => {
  const taskId = generateTaskId()
  
  // 异步开始处理
  processDataExport(taskId, req.body).catch(console.error)
  
  res.json({
    success: true,
    taskId,
    statusUrl: `/api/export/status/${taskId}`
  })
})

// 状态查询
app.get('/api/export/status/:taskId', (req, res) => {
  const task = getTask(req.params.taskId)
  
  if (!task) {
    return res.status(404).json({ error: '任务不存在' })
  }
  
  res.json({
    status: task.status,
    progress: task.progress,
    message: task.message,
    estimatedTime: task.estimatedTime,
    downloadUrl: task.downloadUrl
  })
})

// 文件下载
app.get('/api/download/:taskId/:filename', (req, res) => {
  const filePath = getTaskFilePath(req.params.taskId)
  
  res.setHeader('Content-Type', 'application/zip')
  res.setHeader('Content-Disposition', `attachment; filename="${req.params.filename}"`)
  
  const stream = fs.createReadStream(filePath)
  stream.pipe(res)
})
```

## 🎨 用户体验优化

### 1. 智能提示信息

```typescript
const getStatusMessage = (status, progress) => {
  const messages = {
    'pending': '正在准备数据导出...',
    'processing': (() => {
      if (progress < 30) return '正在从云端获取数据...'
      if (progress < 70) return '正在处理和分析数据...'
      return '正在生成导出文件...'
    })()
  }
  return messages[status] || '处理中...'
}
```

### 2. 预估时间显示

```typescript
const formatEstimatedTime = (seconds) => {
  if (seconds < 60) return `预计还需 ${seconds} 秒`
  const minutes = Math.floor(seconds / 60)
  return `预计还需 ${minutes} 分钟`
}
```

### 3. 分步骤进度

```html
<div class="progress-steps">
  <div class="step" :class="{ active: progress >= 0 }">获取数据</div>
  <div class="step" :class="{ active: progress >= 30 }">数据处理</div>
  <div class="step" :class="{ active: progress >= 70 }">文件生成</div>
</div>
```

## ✅ 最佳实践

1. **合理设置超时时间**：根据数据量大小设置 `maxWaitTime`
2. **适当的轮询频率**：`pollInterval` 建议 1-3 秒
3. **友好的错误处理**：提供重试机制和详细错误信息
4. **进度反馈**：及时更新状态和进度
5. **取消功能**：允许用户取消长时间运行的任务
6. **权限验证**：每次请求都要验证用户权限

## 🚨 注意事项

- 确保后端接口支持任务状态查询
- 处理网络异常和超时情况
- 避免同时发起多个相同的导出任务
- 及时清理服务端的临时文件
- 考虑文件大小限制和存储成本

## 📚 更多资源

- [完整API文档](./README.md)
- [后端实现示例](./backend-api-example.ts)
- [Vue组件示例](./async-download-demo.vue)
- [性能测试指南](./README.md#🧪-性能测试)
