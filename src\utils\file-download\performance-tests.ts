/**
 * 性能测试和演示文件
 * 验证下载过程中页面不卡顿，以及取消功能
 */

import { downloadFile, createDownloader } from "./index"
import type { DownloadProgress } from "./types"

/**
 * 性能测试：下载大文件时页面保持响应
 */
export class PerformanceTest {
  private testResults: Array<{
    testName: string
    fileSize: string
    duration: number
    avgFPS: number
    memoryUsage: number
    canceled: boolean
  }> = []

  /**
   * 测试1: 下载过程中页面响应性
   */
  async testPageResponsiveness() {
    console.log("🧪 开始测试页面响应性...")

    // 监控 FPS
    const fpsMonitor = this.startFPSMonitor()

    // 监控内存使用
    const memoryMonitor = this.startMemoryMonitor()

    // 创建测试按钮验证页面交互
    this.createTestUI()

    const startTime = Date.now()

    try {
      // 模拟下载大文件
      const testUrl = this.generateLargeFile(100 * 1024 * 1024) // 100MB

      const result = await downloadFile(testUrl, "performance-test.dat", {
        onProgress: (progress: DownloadProgress) => {
          // 验证进度回调不阻塞主线程
          this.updateTestProgress(progress)
        },
      })

      const duration = Date.now() - startTime
      const avgFPS = fpsMonitor.getAverage()
      const memoryUsage = memoryMonitor.getPeak()

      this.testResults.push({
        testName: "Large File Download",
        fileSize: "100MB",
        duration,
        avgFPS,
        memoryUsage,
        canceled: false,
      })

      console.log("✅ 测试完成:", {
        duration: `${duration}ms`,
        avgFPS: `${avgFPS}fps`,
        memoryUsage: `${memoryUsage}MB`,
        pageResponsive: avgFPS > 30, // 30fps 以上认为流畅
      })
    } finally {
      fpsMonitor.stop()
      memoryMonitor.stop()
      URL.revokeObjectURL(testUrl)
    }
  }

  /**
   * 测试2: 取消功能响应时间
   */
  async testCancelResponse() {
    console.log("🧪 开始测试取消响应时间...")

    const downloader = createDownloader()
    const testUrl = this.generateLargeFile(1024 * 1024 * 1024) // 1GB

    let progressCount = 0
    let cancelTime = 0
    let actualCancelTime = 0

    // 开始下载
    const downloadPromise = downloader.download(testUrl, "cancel-test.dat", {
      onProgress: (progress: DownloadProgress) => {
        progressCount++

        // 下载到 10% 时取消
        if (progress.percentage >= 10 && cancelTime === 0) {
          cancelTime = Date.now()

          // 异步取消，测试响应时间
          setTimeout(() => {
            downloader.cancel()
            actualCancelTime = Date.now()
          }, 0)
        }
      },
      onError: (error: Error) => {
        if (error.message.includes("取消")) {
          const responseTime = actualCancelTime - cancelTime
          console.log(`✅ 取消响应时间: ${responseTime}ms`)

          this.testResults.push({
            testName: "Cancel Response",
            fileSize: "1GB (partial)",
            duration: responseTime,
            avgFPS: 0,
            memoryUsage: 0,
            canceled: true,
          })
        }
      },
    })

    try {
      await downloadPromise
    } catch (error) {
      // 预期的取消错误
      console.log("📝 下载已取消（符合预期）")
    } finally {
      URL.revokeObjectURL(testUrl)
    }
  }

  /**
   * 测试3: 并发下载性能
   */
  async testConcurrentDownloads() {
    console.log("🧪 开始测试并发下载性能...")

    const fpsMonitor = this.startFPSMonitor()
    const concurrentCount = 3
    const fileSize = 50 * 1024 * 1024 // 50MB each

    const downloadPromises = Array.from({ length: concurrentCount }, (_, i) => {
      const testUrl = this.generateLargeFile(fileSize)

      return downloadFile(testUrl, `concurrent-test-${i}.dat`, {
        onProgress: (progress: DownloadProgress) => {
          console.log(`文件 ${i + 1} 进度: ${progress.percentage.toFixed(1)}%`)
        },
      }).finally(() => {
        URL.revokeObjectURL(testUrl)
      })
    })

    const startTime = Date.now()

    try {
      const results = await Promise.all(downloadPromises)
      const duration = Date.now() - startTime
      const avgFPS = fpsMonitor.getAverage()

      console.log("✅ 并发下载测试完成:", {
        concurrentFiles: concurrentCount,
        totalSize: `${((fileSize * concurrentCount) / 1024 / 1024).toFixed(1)}MB`,
        duration: `${duration}ms`,
        avgFPS: `${avgFPS}fps`,
        successCount: results.filter(r => r.success).length,
      })
    } finally {
      fpsMonitor.stop()
    }
  }

  /**
   * 创建测试 UI 验证页面交互
   */
  private createTestUI() {
    // 创建测试按钮
    const testButton = document.createElement("button")
    testButton.textContent = "点击测试页面响应性"
    testButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      padding: 10px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    `

    let clickCount = 0
    testButton.addEventListener("click", () => {
      clickCount++
      testButton.textContent = `已点击 ${clickCount} 次`
      console.log(`✅ 页面响应正常，点击次数: ${clickCount}`)
    })

    document.body.appendChild(testButton)

    // 5秒后移除
    setTimeout(() => {
      document.body.removeChild(testButton)
    }, 5000)
  }

  /**
   * 更新测试进度显示
   */
  private updateTestProgress(progress: DownloadProgress) {
    // 验证这个函数不会阻塞主线程
    const updateTime = Date.now()

    // 模拟一些 UI 更新操作
    const progressElement = document.getElementById("test-progress")
    if (progressElement) {
      progressElement.textContent = `下载进度: ${progress.percentage.toFixed(1)}%`
    }

    // 如果这个函数执行时间超过 16ms (60fps)，说明可能有性能问题
    const executionTime = Date.now() - updateTime
    if (executionTime > 16) {
      console.warn(`⚠️ 进度更新函数执行时间过长: ${executionTime}ms`)
    }
  }

  /**
   * FPS 监控器
   */
  private startFPSMonitor() {
    let fps = 0
    let lastTime = Date.now()
    let frameCount = 0
    const fpsHistory: number[] = []
    let running = true

    function updateFPS() {
      if (!running) return

      frameCount++
      const currentTime = Date.now()

      if (currentTime - lastTime >= 1000) {
        fps = frameCount
        fpsHistory.push(fps)

        // 保持最近 10 秒的历史
        if (fpsHistory.length > 10) {
          fpsHistory.shift()
        }

        frameCount = 0
        lastTime = currentTime
      }

      requestAnimationFrame(updateFPS)
    }

    requestAnimationFrame(updateFPS)

    return {
      getAverage: () => {
        return fpsHistory.length > 0
          ? fpsHistory.reduce((a, b) => a + b, 0) / fpsHistory.length
          : 0
      },
      stop: () => {
        running = false
      },
    }
  }

  /**
   * 内存监控器
   */
  private startMemoryMonitor() {
    let peakMemory = 0
    let running = true

    const monitor = () => {
      if (!running) return

      // 检查内存使用（如果浏览器支持）
      if ("memory" in performance) {
        const memory = (performance as any).memory
        const currentMemory = memory.usedJSHeapSize / 1024 / 1024 // MB
        peakMemory = Math.max(peakMemory, currentMemory)
      }

      setTimeout(monitor, 1000) // 每秒检查一次
    }

    monitor()

    return {
      getPeak: () => peakMemory,
      stop: () => {
        running = false
      },
    }
  }

  /**
   * 生成测试用的大文件 URL
   */
  private generateLargeFile(sizeBytes: number): string {
    // 生成指定大小的测试数据
    const chunkSize = 1024 * 1024 // 1MB chunks
    const chunks = Math.ceil(sizeBytes / chunkSize)
    const data = new Uint8Array(sizeBytes)

    // 填充一些模式数据
    for (let i = 0; i < sizeBytes; i++) {
      data[i] = i % 256
    }

    const blob = new Blob([data], { type: "application/octet-stream" })
    return URL.createObjectURL(blob)
  }

  /**
   * 获取测试结果
   */
  getTestResults() {
    return this.testResults
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log("🚀 开始运行所有性能测试...")

    try {
      await this.testPageResponsiveness()
      await this.testCancelResponse()
      await this.testConcurrentDownloads()

      console.log("✅ 所有测试完成！")
      console.table(this.testResults)

      return this.testResults
    } catch (error) {
      console.error("❌ 测试过程中出现错误:", error)
      throw error
    }
  }
}

// 使用示例
export async function runPerformanceTests() {
  const tester = new PerformanceTest()
  return await tester.runAllTests()
}

// 快速验证函数
export function quickResponsivenessTest() {
  console.log("🧪 快速响应性测试")

  // 创建一个简单的动画来验证页面不卡顿
  let animationCount = 0
  const startTime = Date.now()

  function animate() {
    animationCount++

    // 显示动画帧计数
    const element =
      document.getElementById("animation-test") ||
      (() => {
        const div = document.createElement("div")
        div.id = "animation-test"
        div.style.cssText = `
        position: fixed;
        top: 60px;
        right: 20px;
        z-index: 9999;
        padding: 10px;
        background: rgba(0,0,0,0.8);
        color: white;
        border-radius: 4px;
        font-family: monospace;
      `
        document.body.appendChild(div)
        return div
      })()

    const elapsed = Date.now() - startTime
    const fps = Math.round((animationCount / elapsed) * 1000)

    element.textContent = `动画帧: ${animationCount} | FPS: ${fps}`

    if (elapsed < 10000) {
      // 运行 10 秒
      requestAnimationFrame(animate)
    } else {
      document.body.removeChild(element)
      console.log(`✅ 动画测试完成，平均FPS: ${fps}`)
    }
  }

  requestAnimationFrame(animate)
}
