import Layout from "@/layout/LayoutProxy"

export default {
  path: "/spread-analysis",
  name: "SpreadAnalysis",
  component: Layout,
  redirect: "/dashboard",
  meta: {
    icon: "homeFilled",
    title: "利差分析",
    rank: 0,
  },
  children: [
    {
      path: "/spread-analysis/static-view",
      name: "SpreadAnalysisStaticView",
      component: () => import("@/modules/spread-analysis/views/static-view.vue"),
      meta: {
        title: "持仓利差视图（静态）",
      },
    },
    {
      path: "/spread-analysis/dynamic-view",
      name: "SpreadAnalysisDynamicView",
      component: () => import("@/modules/spread-analysis/views/dynamic-view.vue"),
      meta: {
        title: "持仓利差视图（动态）",
      },
    },
  ],
} as RouteConfigsTable
