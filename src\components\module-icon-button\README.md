# ModuleIconButton 组件

通用的图标按钮组件，支持下拉菜单和 tooltip 提示功能。

## 功能特性

- ✅ 支持下拉菜单配置
- ✅ 支持危险操作标识（红色文字）
- ✅ 支持两种提示类型：HTML title 和 xq-tooltip
- ✅ 完全可配置的 tooltip 选项
- ✅ TypeScript 类型支持

## Props 参数

| 参数            | 类型                       | 默认值    | 说明             |
|-----------------|----------------------------|-----------|------------------|
| `menuOptions`   | `ModuleIconButtonOption[]` | `[]`      | 下拉菜单配置项   |
| `width`         | `number`                   | `80`      | 弹出框宽度       |
| `title`         | `string`                   | -         | 按钮提示信息     |
| `tooltipType`   | `'title' \| 'tooltip'`     | `'title'` | 提示类型         |
| `tooltipConfig` | `TooltipConfig`            | `{}`      | tooltip 配置选项 |

## 插槽

| 名称      | 说明                                                              |
|-----------|-------------------------------------------------------------------|
| `default` | 自定义按钮图标，不传则默认使用 `<MoreFilled class="rotate-90" />` |

## 事件

| 事件名            | 参数            | 说明                 |
|-------------------|-----------------|----------------------|
| `menu-item-click` | `value: string` | 菜单项点击事件       |
| `click`           | `value: string` | 按钮点击事件（预留） |

## 类型定义

### ModuleIconButtonOption

```typescript
interface ModuleIconButtonOption {
  /** 菜单名称 */
  label: string
  /** 菜单 ID */
  value: string
  /** 是否为危险操作（红色文字） */
  danger?: boolean
}
```

### TooltipConfig

```typescript
interface TooltipConfig {
  /** tooltip 内容 */
  content?: string
  /** 出现位置 */
  placement?: 'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'
  /** 主题 */
  effect?: 'dark' | 'light'
  /** 延迟显示时间 */
  showAfter?: number
  /** 延迟隐藏时间 */
  hideAfter?: number
}
```

## 使用示例

### 基础用法

```vue
<template>
  <ModuleIconButton
    :menu-options="menuOptions"
    @menu-item-click="handleMenuClick"
  />
</template>

<script setup>
const menuOptions = [
  { label: '编辑', value: 'edit' },
  { label: '删除', value: 'delete', danger: true }
]

function handleMenuClick(value) {
  console.log('点击菜单项:', value)
}
</script>
```

### 使用 HTML title 提示（默认）

```vue
<template>
  <ModuleIconButton
    title="更多操作"
    :menu-options="menuOptions"
    @menu-item-click="handleMenuClick"
  />
</template>
```

### 使用 xq-tooltip 提示

```vue
<template>
  <ModuleIconButton
    title="更多操作"
    tooltip-type="tooltip"
    :menu-options="menuOptions"
    @menu-item-click="handleMenuClick"
  />
</template>
```

### 自定义 tooltip 配置

```vue
<template>
  <ModuleIconButton
    title="更多操作"
    tooltip-type="tooltip"
    :tooltip-config="{
      placement: 'bottom',
      effect: 'light',
      showAfter: 100,
      content: '自定义提示内容'
    }"
    :menu-options="menuOptions"
    @menu-item-click="handleMenuClick"
  />
</template>
```

### 在工具栏中使用

```vue
<template>
  <div class="toolbar">
    <span>视图管理</span>
    <ModuleIconButton
      title="更多视图操作"
      :menu-options="[
        { label: '添加新视图', value: 'add' },
        { label: '保存当前视图', value: 'save' },
        { label: '还原原始视图', value: 'reset' }
      ]"
      :width="120"
      @menu-item-click="handleToolbarAction"
    />
  </div>
</template>

<script setup>
function handleToolbarAction(action) {
  switch (action) {
    case 'add':
      // 添加新视图逻辑
      break
    case 'save':
      // 保存当前视图逻辑
      break
    case 'reset':
      // 还原原始视图逻辑
      break
  }
}
</script>
```

### 自定义图标

```vue
<template>
  <!-- 使用自定义图标 -->
  <ModuleIconButton
    title="设置"
    :menu-options="menuOptions"
    @menu-item-click="handleMenuClick"
  >
    <Setting />
  </ModuleIconButton>
  
  <!-- 使用默认图标（不传插槽内容） -->
  <ModuleIconButton
    title="更多操作"
    :menu-options="menuOptions"
    @menu-item-click="handleMenuClick"
  />
</template>

<script setup>
import { Setting } from "@xquant/x-ui-plus-icons-vue"
</script>
```

## 设计特点

1. **灵活的提示系统**：支持原生 HTML title 和富功能的 xq-tooltip 两种提示方式
2. **配置化菜单**：通过 `menuOptions` 数组轻松配置菜单项
3. **危险操作标识**：支持通过 `danger` 属性标识危险操作，自动显示红色文字
4. **响应式设计**：使用 CSS 变量支持主题切换
5. **类型安全**：完整的 TypeScript 类型定义

## 注意事项

- 当 `tooltipType` 为 `'tooltip'` 时，会使用 `xq-tooltip` 组件显示提示
- 当 `tooltipType` 为 `'title'` 时，会使用原生 HTML `title` 属性显示提示
- `tooltipConfig.content` 会优先使用，如果不提供则使用 `title` 属性值
- 组件基于 `xq-popover` 实现，兼容 element-plus 的 API 设计
