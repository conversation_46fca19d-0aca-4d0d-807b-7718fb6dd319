import prettier from "eslint-config-prettier"
import vue from "eslint-plugin-vue"
import { readFileSync } from "fs"
import {
  dirname,
  join,
} from "path"
import { fileURLToPath } from "url"
import vueParser from "vue-eslint-parser"

import js from "@eslint/js"
import typescript from "@typescript-eslint/eslint-plugin"
import typescriptParser from "@typescript-eslint/parser"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 读取自动导入配置
let autoImportGlobals = {}
try {
  const autoImportConfig = JSON.parse(
    readFileSync(join(__dirname, '.eslintrc-auto-import.json'), 'utf8')
  )
  autoImportGlobals = autoImportConfig.globals || {}
} catch (error) {
  console.warn('Could not load auto-import globals:', error.message)
}

export default [
  // 忽略的文件
  {
    ignores: [
      'public/**/*',
      'dist/**/*',
      '**/*.d.ts',
      'src/assets/**/*',
      'package.json',
      'commitlint.config.js',
      'postcss.config.js',
      'tailwind.config.js',
      'stylelint.config.js',
      'node_modules/**/*',
      '.eslintrc-auto-import.json'
    ]
  },

  // 基础 JavaScript 配置
  js.configs.recommended,

  // Vue 文件配置
  ...vue.configs['flat/recommended'],

  // TypeScript 配置
  {
    files: ['**/*.{ts,tsx,vue}'],
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        parser: typescriptParser,
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true
        },
        extraFileExtensions: ['.vue']
      },
      globals: {
        // Node.js 环境
        process: 'readonly',
        global: 'readonly',
        console: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        module: 'readonly',
        require: 'readonly',
        exports: 'readonly',

        // Vue 3 Composition API (从自动导入配置中读取)
        ...autoImportGlobals,

        // 项目全局类型
        Fn: 'readonly',
        PromiseFn: 'readonly',
        RefType: 'readonly',
        LabelValueOptions: 'readonly',
        EmitType: 'readonly',
        TargetContext: 'readonly',
        ComponentElRef: 'readonly',
        ComponentRef: 'readonly',
        ElRef: 'readonly',
        ForDataType: 'readonly',
        ComponentRoutes: 'readonly',

        // Vue 3 编译宏
        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly',

        // Vue Ref sugar (take 2)
        $: 'readonly',
        $$: 'readonly',
        $ref: 'readonly',
        $shallowRef: 'readonly',
        $computed: 'readonly'
      }
    },
    plugins: {
      '@typescript-eslint': typescript,
      vue
    },
    rules: {
      // TypeScript 规则
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/ban-types': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_'
        }
      ],

      // 命名约定规则
      '@typescript-eslint/naming-convention': [
        'error',
        // 变量使用 camelCase
        {
          selector: 'variable',
          format: ['camelCase', 'PascalCase', 'UPPER_CASE'],
          leadingUnderscore: 'allow',
          trailingUnderscore: 'allow'
        },
        // 函数使用 camelCase
        {
          selector: 'function',
          format: ['camelCase', 'PascalCase']
        },
        // 类使用 PascalCase
        {
          selector: 'class',
          format: ['PascalCase']
        },
        // 类方法使用 camelCase
        {
          selector: 'classMethod',
          format: ['camelCase']
        },
        // 类属性使用 camelCase
        {
          selector: 'classProperty',
          format: ['camelCase', 'UPPER_CASE']
        },
        // 接口使用 PascalCase
        {
          selector: 'interface',
          format: ['PascalCase']
        },
        // 类型别名使用 PascalCase
        {
          selector: 'typeAlias',
          format: ['PascalCase']
        },
        // 枚举使用 PascalCase
        {
          selector: 'enum',
          format: ['PascalCase']
        },
        // 枚举成员使用 PascalCase
        {
          selector: 'enumMember',
          format: ['PascalCase', 'UPPER_CASE']
        }
      ],

      // Vue 规则
      'vue/no-v-html': 'off',
      'vue/require-default-prop': 'off',
      'vue/require-explicit-emits': 'off',
      'vue/multi-word-component-names': 'off',
      'vue/require-prop-types': 'off', // 关闭 prop 类型要求，TypeScript 会处理
      'vue/prop-name-casing': 'off', // 关闭 prop 命名规则
      // Vue 组件命名规则
      'vue/component-definition-name-casing': ['error', 'PascalCase'],
      'vue/component-name-in-template-casing': ['error', 'PascalCase'],
      'vue/html-self-closing': [
        'error',
        {
          html: {
            void: 'always',
            normal: 'always',
            component: 'always'
          },
          svg: 'always',
          math: 'always'
        }
      ],

      // 通用规则
      'no-debugger': 'off',
      'no-undef': 'off', // TypeScript 会处理未定义的变量
      'no-unused-vars': 'off', // 使用 TypeScript 的版本
      'prefer-const': 'error',
      'no-var': 'error'
    }
  },

  // 仅 JavaScript 文件的配置
  {
    files: ['**/*.{js,jsx}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        process: 'readonly',
        global: 'readonly',
        console: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        module: 'readonly',
        require: 'readonly',
        exports: 'readonly'
      }
    },
    rules: {
      'no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_'
        }
      ]
    }
  },

  // Prettier 配置 - 必须放在最后
  prettier
]
