// Prettier 配置文件专门用于处理 Tailwind CSS 类名换行
module.exports = {
  // 基础配置继承主配置
  ...require("./.prettierrc.js"),

  // 专门针对长类名的配置
  overrides: [
    {
      files: ["*.vue"],
      options: {
        printWidth: 60,
        singleAttributePerLine: true,
        // Vue 特定的 HTML 换行配置
        htmlWhitespaceSensitivity: "ignore",
        // 强制属性换行
        vueIndentScriptAndStyle: false,
      },
    },
    {
      // 专门处理包含长 class 属性的文件
      files: ["**/*.vue"],
      options: {
        printWidth: 50, // 更短的行宽强制换行
        // 自定义 HTML 属性处理
        htmlWhitespaceSensitivity: "ignore",
        // 单行属性
        singleAttributePerLine: true,
      },
    },
  ],
}
