*,
::before,
::after {
  box-sizing: border-box;
}

html,
body,
#{$app} {
  height: 100%;
}

/* ----------------------------- x-ui-plus 样式覆盖 ----------------------------- */
.#{$ns}-dialog__body {
  padding: 16px 24px;
}

/* ---------------------------------- 公共样式 ---------------------------------- */
.check-tip {
  .#{$ns}-message-box__content,
  .#{$ns}-message-box__container,
  .#{$ns}-message-box__message {
    width: 100%;
  }
  .#{$ns}-message-box__header {
    padding-bottom: 12px!important;
  }
  .#{$ns}-message-box__headerbtn {
    height: 40px;
  }
  .#{$ns}-message-box__title {
    font-size: 14px;
  }
  &:has(.#{$ns}-message-box__status) .#{$ns}-message-box__title,
  &:has(.#{$ns}-message-box__status) .#{$ns}-message-box__message {
    padding-left: 20px!important;
  }
  .#{$ns}-message-box__message {
    padding-left: 0!important;
  }
  .#{$ns}-message-box__status {
    top: 18px;
    font-size: 16px;
  }
}

/* --------------------------------- 分组表格背景色 -------------------------------- */

/* 产品信息相关样式 - 蓝色系 */
.product-group-header {
  background-color: #6b7fd7 !important;
  color: white !important;
  font-weight: 500;
  &.#{$ns}-table__cell > .cell {
    text-align: center;
  }
}

.product-header {
  background-color: #6b7fd7 !important;
  color: white !important;
  font-weight: 500;
}

.product-cell {
  background-color: #c7d2fe !important;
  color: #374151;
}

.product-cell-striped {
  background-color: #a5b4fc !important;
  color: #374151;
}

/* 指标基础信息相关样式 - 橙色系 */
.indicator-group-header {
  background-color: #fb923c !important;
  color: white !important;
  font-weight: 500;
}

.indicator-header {
  background-color: #fb923c !important;
  color: white !important;
  font-weight: 500;
}

.indicator-cell {
  background-color: #fed7aa !important;
  color: #374151;
}

.indicator-cell-striped {
  background-color: #fdba74 !important;
  color: #374151;
}

/* 指标使用情况相关样式 - 绿色系 */
.usage-group-header {
  background-color: #4ade80 !important;
  color: white !important;
  font-weight: 500;
}

.usage-header {
  background-color: #4ade80 !important;
  color: white !important;
  font-weight: 500;
}

.usage-cell {
  background-color: #bbf7d0 !important;
  color: #374151;
}

.usage-cell-striped {
  background-color: #86efac !important;
  color: #374151;
}



/* 普通列的斑马纹 */
.normal-cell-striped {
  background-color: #f9fafb !important;
}

/* 悬停效果 */
:deep(.#{$ns}-table__row:hover td) {
  background-color: rgba(59, 130, 246, 0.05) !important;
}

/* 表头居中对齐 */
:deep(.#{$ns}-table__header .#{$ns}-table__cell > .cell) {
  text-align: center;
  justify-content: center;
}

/* 分组表头居中对齐 */
:deep(.#{$ns}-table__header-wrapper .#{$ns}-table__header .#{$ns}-table__cell) {
  text-align: center;
}

/* 确保表格占满容器宽度 */
:deep(.#{$ns}-table) {
  width: 100% !important;
}

