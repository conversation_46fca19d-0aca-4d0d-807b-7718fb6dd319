<script lang="ts" setup>
defineOptions({
  name: "ModuleLoading",
})

const isLoading = defineModel<boolean>()
const { height = 20 } = defineProps<{
  /** 加载区域高度 */
  height?: number
}>()
</script>

<template>
  <div
    class="relative flex w-full items-center justify-center px-[12px]"
    :style="{ height: `${height}px` }"
  >
    <loading
      v-model:active="isLoading"
      loader="dots"
      color="var(--xq-color-primary)"
      :is-full-page="false"
      :height="height"
    />
  </div>
</template>

<style scoped lang="scss">
:deep(.vl-overlay .vl-background) {
  background-color: transparent;
}
</style>
