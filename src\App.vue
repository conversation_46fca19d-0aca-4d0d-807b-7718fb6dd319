<!--
 * @Date         : 2024-07-10 11:11:42 星期3
 * <AUTHOR> xut
 * @Description  :
-->
<template>
  <xq-config-provider
    :locale="currentLocale"
    :namespace="namespace"
    :css-var-namespace="cssVarNS"
    :request="request"
    :table="{ showHeaderDivider: true, scrollbarLayout: 'footprint', border: false }"
  >
    <router-view />
  </xq-config-provider>
</template>

<script lang="ts">
import { XqConfigProvider } from "@xquant/x-ui-plus"
import zhCn from "@xquant/x-ui-plus/es/locale/lang/zh-cn"
import zhCnCubeBiz from "@cube-ui/biz/locale/lang/zh-cn.mjs"
import namespace, { cssVarNS } from "@/namespace"
import { http } from "@/utils/http"

export default defineComponent({
  name: "App",
  components: {
    [XqConfigProvider.name as string]: XqConfigProvider,
  },
  provide() {
    return {
      parentProps: this.parentProps,
    }
  },
  props: {
    parentProps: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      request: null as any,
    }
  },
  computed: {
    currentLocale() {
      return { ...zhCn, ...zhCnCubeBiz }
    },
    namespace() {
      return namespace
    },
    cssVarNS() {
      return cssVarNS
    },
  },
  created() {
    this.request = (url: string, options: any) => {
      options.url = url
      return http.request(null, null, null, options)
    }
  },
})
</script>
