# 统一风险管理平台 - 前端模块规划

根据需求文档目录，提取出以下前端功能模块、路由及目录命名建议。

## 1. 业务功能模块 (Functional Modules)

- **风险情况总览** (Risk Situation Overview)
- **风险指标情况总览** (Risk Indicator Overview)
    - 风险偏好指标明细 (Risk Appetite Indicator Details)
    - 监管合规风险指标明细 (Regulatory Compliance Risk Indicator Details)
    - 内部管理风险指标明细 (Internal Management Risk Indicator Details)
- **风险预警信息总览** (Risk Warning Information Overview)
- **流动性风险分析** (Liquidity Risk Analysis)
- **市场风险分析** (Market Risk Analysis)
- **大类资产配置** (Large-asset Allocation)
- **集中度分析** (Concentration Analysis)
- **归因分析** (Attribution Analysis)
- **自营端风险情况总览** (Proprietary Trading Risk Overview)
- **极限管理** (Limit Management)

## 2. 建议路由命名 (Route Naming)

采用 `kebab-case` 风格，层级清晰，并提供详细描述。

- **`/risk-overview`**:
    - **功能**: 风险情况总览
    - **描述**: 提供整个银行风险状况的宏观视图，作为风险工作台的首页或核心看板。

- **`/risk-indicators`**:
    - **功能**: 风险指标情况总览
    - **描述**: 展示关键风险指标 (KRI) 的概览信息。可作为父路由。
    - **子路由**:
        - **`/risk-indicators/preference`**:
            - **功能**: 风险偏好指标明细
            - **描述**: 详细展示与银行风险偏好相关的各项指标。
        - **`/risk-indicators/compliance`**:
            - **功能**: 监管合规风险指标明细
            - **描述**: 详细展示与监管要求相关的合规性风险指标。
        - **`/risk-indicators/internal`**:
            - **功能**: 内部管理风险指标明细
            - **描述**: 详细展示用于银行内部管理的风险指标。

- **`/risk-warnings`**:
    - **功能**: 风险预警信息总览
    - **描述**: 汇总和展示各类风险预警信号和信息。

- **`/liquidity-risk-analysis`**:
    - **功能**: 流动性风险分析
    - **描述**: 提供流动性风险的分析工具和视图。

- **`/market-risk-analysis`**:
    - **功能**: 市场风险分析
    - **描述**: 提供市场风险的分析工具和视图。

- **`/asset-allocation`**:
    - **功能**: 大类资产配置
    - **描述**: 展示和分析大类资产的配置情况，可作为父路由。
    - **子路由**:
        - **`/asset-allocation/overview`**:
            - **功能**: 大类资产配置(穿透前&穿透后)
            - **描述**: 宏观展示大类资产的配置情况。
        - **`/asset-allocation/credit-bond-rating`**:
            - **功能**: 信用债评级分布
            - **描述**: 分析信用债的评级分布情况。
        - **`/asset-allocation/credit-bond-risk`**:
            - **功能**: 信用债风险情况总览
            - **描述**: 按资产类别和行业分类，展示信用债的风险情况。
        - **`/asset-allocation/fund-risk`**:
            - **功能**: 基金风险情况总览
            - **描述**: 汇总展示基金产品的风险情况。
        - **`/asset-allocation/deposit-risk`**:
            - **功能**: 存款风险情况总览
            - **描述**: 汇总展示存款产品的风险情况。
        - **`/asset-allocation/entrusted-investment-risk`**:
            - **功能**: 委外风险情况总览
            - **描述**: 汇总展示委外投资的风险情况。

- **`/concentration-analysis`**:
    - **功能**: 集中度分析
    - **描述**: 分析各类资产和机构的集中度，可作为父路由。
    - **子路由**:
        - **`/concentration-analysis/entity`**:
            - **功能**: 主体集中度
            - **描述**: 分析交易对手或发行主体的集中度。
        - **`/concentration-analysis/asset`**:
            - **功能**: 资产集中度
            - **描述**: 分析不同资产类别的集中度。
        - **`/concentration-analysis/single-bond`**:
            - **功能**: 单券集中度
            - **描述**: 分析单一债券的持仓集中度。
        - **`/concentration-analysis/partner-institution`**:
            - **功能**: 合作机构集中度
            - **描述**: 分析与合作机构业务往来的集中度。

- **`/attribution-analysis`**:
    - **功能**: 归因分析
    - **描述**: 对风险或收益进行归因分析。

- **`/proprietary-risk-overview`**:
    - **功能**: 自营端风险情况总览
    - **描述**: 专门针对自营业务的风险情况概览。

- **`/limit-management`**:
    - **功能**: 极限管理
    - **描述**: 管理和监控各类风险限额。

## 3. 建议目录命名 (Directory Naming)

在 `src/views/` 或 `src/modules/` 下，建议创建与功能模块对应的目录，用于存放视图组件、hooks、services 等。

```
src/views/
├── risk-overview/
├── risk-indicators/
├── risk-warnings/
├── liquidity-risk-analysis/
├── market-risk-analysis/
├── asset-allocation/
├── concentration-analysis/
├── attribution-analysis/
├── proprietary-risk-overview/
└── limit-management/
```


## 模板目录规范

- `components` 公共组件
- `views` 包含的视图
- `hooks` 钩子函数
- `services` 放置后端接口服务
- `stores` Pinia 模块
- `utils` 辅助函数
- `index.ts` 入口函数
- `consts.ts` 放置常量
- `types.ts` 放置类型声明

## 功能需求

- 查询支持模板化功能（保存查询条件下次快速查询）