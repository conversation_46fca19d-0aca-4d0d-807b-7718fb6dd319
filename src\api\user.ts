import { http } from "@/utils/http"

export type UserResult = {
  success: boolean
  data: {
    /** 用户名 */
    username: string
    /** 当前登陆用户的角色 */
    roles: Array<string>
    /** `token` */
    accessToken: string
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date
  }
}

export type RefreshTokenResult = {
  success: boolean
  data: {
    /** `token` */
    accessToken: string
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date
  }
}

export type PermissionResult = {
  success: boolean
  data: {
    appCode: string
    menuPermissions: {
      menuUrl: string
      permissionCacheDto: {
        buttons: string[]
        apis: string[]
      }
    }[]
  }
}

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<UserResult>("post", "/login", { data })
}

/** SSO登录 */
export const getSSOLogin = (data: object) => {
  return http.request<UserResult>("post", "/sso/login/local", { data })
}

/** 刷新token */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refreshToken", { data })
}

// 根据appCode查询出用户的菜单权限
export const getUserPermissionsByAppcode = (appCode: string) => {
  return http.get<{ appCode: string }, PermissionResult>("/user/permissions", {
    params: {
      appCode,
    },
  })
}
