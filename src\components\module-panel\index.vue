<script setup lang="ts">
import {
  useFullscreen,
  useResizeObserver,
} from "@vueuse/core"
import { XqMessage } from "@xquant/x-ui-plus"
import {
  Download,
  FullScreen,
  FullScreenExit,
  History,
  Refresh,
} from "@xquant/x-ui-plus-icons-vue"

type ContentType = "table" | "chart"

defineOptions({
  name: "ModulePanel",
})

const { title, contentType = "chart" } = defineProps<{
  /** 面板标题 */
  title: string
  /** 内容类型, `table`: 表格，`chart`: 图表 */
  contentType?: ContentType
}>()

const emit = defineEmits<{
  /** 刷新事件 */
  (e: "refresh", value: boolean): void
  /** 导出事件 */
  (e: "export", type: ContentType): void
  /** 全屏切换事件 */
  (e: "fullscreen-toggle", value: boolean): void
  /** 容器尺寸变化事件 */
  (e: "resize", width: number, height: number): void
}>()

/** 容器引用 */
const containerRef = useTemplateRef<HTMLElement | null>("containerRef")

/** 全屏功能 */
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen(containerRef)

/** 【导出】按钮标签 */
const exportButtonLabel = computed(() => {
  return contentType === "table" ? "导出表格" : "导出图表"
})

// 考虑到筛选组件有【展开/折叠】功能，会导致图表高度改变，进而影响内容的完整展示
// 这里需要在外部容器高度改变时同步更新图表的尺寸
useResizeObserver(containerRef, entries => {
  const entry = entries[0]
  const { width, height } = entry.contentRect
  nextTick(() => {
    // 触发图表重新计算尺寸
    emit("resize", width, height)
  })
})

// 监听全屏状态变化，确保图表尺寸正确调整
watch(
  isFullscreen,
  newVal => {
    console.log(`全屏状态变化: ${newVal ? "进入全屏" : "退出全屏"}`)

    // 延迟调整图表尺寸，等待DOM更新完成
    setTimeout(() => {
      emit("fullscreen-toggle", newVal)
    }, 100)
  },
  { flush: "post" }
)

/**
 * 处理刷新操作
 */
function handleRefresh() {
  emit("refresh", true)
}

/**
 * 处理导出菜单项点击
 * @param value 导出类型
 */
function handleExportMenu() {
  emit("export", contentType)
}

/**
 * 处理全屏操作
 */
async function handleFullscreen() {
  try {
    await toggleFullscreen()

    // 全屏状态变化后，需要重新调整图表尺寸
    await nextTick()

    // 抛出全屏事件
    emit("fullscreen-toggle", isFullscreen.value)

    // 用户提示
    if (isFullscreen.value) {
      XqMessage.success("已进入全屏模式，按 ESC 键可退出全屏")
    } else {
      XqMessage.info("已退出全屏模式")
    }
  } catch (error) {
    console.error("切换全屏失败:", error)
    XqMessage.error("全屏功能不被支持或已被阻止")
  }
}
</script>

<template>
  <div ref="containerRef" class="flex h-full w-full flex-col rounded-[12px] bg-[#f2f6ff]">
    <div
      class="flex h-[32px] justify-between rounded-t-[12px] rounded-tr-[12px] px-[16px]"
      style="background: linear-gradient(to bottom, #eff6ff, #dbeaff)"
    >
      <div
        class="flex h-[32px] items-center text-[12px] leading-[30px] font-bold text-[var(--xq-text-color-primary)]"
      >
        {{ title }}
      </div>
      <div class="flex items-center space-x-3">
        <ModuleIconButton title="刷新" @click="handleRefresh">
          <Refresh class="text-[#407FFF]" />
        </ModuleIconButton>
        <ModuleIconButton
          :title="exportButtonLabel"
          :width="100"
          @menu-item-click="handleExportMenu"
        >
          <Download class="text-[#407FFF]" />
        </ModuleIconButton>
        <ModuleIconButton :title="isFullscreen ? '退出全屏' : '全屏'" @click="handleFullscreen">
          <FullScreenExit v-if="isFullscreen" class="text-[#407FFF]" />
          <FullScreen v-else class="text-[#407FFF]" />
        </ModuleIconButton>
      </div>
    </div>
    <!-- 自定义滚动容器 -->
    <div class="content-scroll-wrapper">
      <slot />
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 滚动容器包装器
.content-scroll-wrapper {
  flex: 1;
  position: relative;
  overflow: auto;
  padding: 16px 20px;

  // 左下角装饰
  &::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 24px;
    height: 24px;
    background: url("@/assets/corner.png") no-repeat left bottom;
    background-size: contain;
    pointer-events: none;
    z-index: 1;
  }

  // 右下角装饰（水平翻转）
  &::after {
    content: "";
    position: absolute;
    right: 0;
    bottom: 0;
    width: 24px;
    height: 24px;
    background: url("@/assets/corner.png") no-repeat right bottom;
    background-size: contain;
    transform: scaleX(-1);
    pointer-events: none;
    z-index: 1;
  }

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--xq-fill-color-lighter);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--xq-border-color-darker);
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--xq-text-color-placeholder);
    }
  }

  &::-webkit-scrollbar-corner {
    background: var(--xq-fill-color-lighter);
  }

  // Firefox 滚动条样式
  scrollbar-width: thin;
  scrollbar-color: var(--xq-border-color-darker) var(--xq-fill-color-lighter);
}
</style>
