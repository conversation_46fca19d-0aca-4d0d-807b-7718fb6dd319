import Layout from "@/layout/LayoutProxy"

export default {
  path: "/risk-overview",
  name: "RiskOverview",
  component: Layout,
  redirect: "/risk-overview/index",
  meta: {
    icon: "homeFilled",
    title: "风险情况总览",
  },
  children: [
    {
      path: "/risk-overview/index",
      name: "RiskOverviewIndex",
      component: () => import("@/modules/risk-overview/views/index.vue"),
      meta: {
        title: "风险情况总览",
      },
    },
  ],
} as RouteConfigsTable
