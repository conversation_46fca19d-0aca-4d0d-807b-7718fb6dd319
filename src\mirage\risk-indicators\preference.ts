import type { Request, Response } from "miragejs"

import type {
  PreferenceListParams,
  PreferenceListResponse,
} from "@/modules/risk-indicators/services/preference"
import type { PreferenceTableData } from "@/modules/risk-indicators/types"
import { faker } from "@faker-js/faker/locale/zh_CN"

// 指标状态选项
const vcItStatusOptions = ["合规", "预警", "违规", "手动合规"]

// 指标类型选项
const vcLtTypeNameOptions = [
  "风险测度",
  "投资限制",
  "流动性管理",
  "信用风险",
  "市场风险",
]

// 指标管理部门选项
const indexManageDeptOptions = [
  "风险管理部",
  "投资管理部",
  "产品管理部",
  "合规管理部",
  "运营管理部",
]

/**
 * 生成偏好类指标明细模拟数据
 */
function generatePreferenceData(): PreferenceTableData {
  return {
    vcLtTypeName: faker.helpers.arrayElement(vcLtTypeNameOptions),
    indexName: faker.helpers.arrayElement([
      "股票投资比例",
      "债券投资比例",
      "现金类资产比例",
      "单一股票投资比例",
      "流动性覆盖率",
      "净稳定资金比例",
      "信用风险敞口",
      "市场风险价值",
      "操作风险资本",
      "集中度风险指标",
    ]),
    tolVal: Number(faker.finance.amount({ min: 50, max: 100, dec: 2 })),
    earlyWarnVal: Number(faker.finance.amount({ min: 80, max: 95, dec: 2 })),
    indexManageDept: faker.helpers.arrayElement(indexManageDeptOptions),
    actualVal: Number(faker.finance.amount({ min: 60, max: 105, dec: 2 })),
    vcItStatus: faker.helpers.arrayElement(vcItStatusOptions),
  }
}

/**
 * 偏好类指标明细列表接口
 */
export function getPreferenceList(schema: any, request: Request): Response {
  const params = request.queryParams as unknown as PreferenceListParams
  const { page = 1, pageSize = 10 } = params

  // 生成模拟数据
  const totalCount = faker.number.int({ min: 50, max: 200 })
  const startIndex = (Number(page) - 1) * Number(pageSize)
  const endIndex = Math.min(startIndex + Number(pageSize), totalCount)

  const list: PreferenceTableData[] = []
  for (let i = startIndex; i < endIndex; i++) {
    list.push(generatePreferenceData())
  }

  // 模拟筛选逻辑
  let filteredList = list

  if (params.indexCode) {
    filteredList = filteredList.filter(item =>
      item.indexName.includes(params.indexCode)
    )
  }

  if (params.indexManageDept) {
    filteredList = filteredList.filter(item =>
      item.indexManageDept.includes(params.indexManageDept)
    )
  }

  if (params.vcItStatus) {
    filteredList = filteredList.filter(item =>
      item.vcItStatus.includes(params.vcItStatus)
    )
  }

  const response: PreferenceListResponse = {
    list: filteredList,
    total: totalCount,
    page: Number(page),
    pageSize: Number(pageSize),
  }

  return new Response(200, {}, { data: response })
}

/**
 * 偏好类指标明细导出接口
 */
export function exportPreferenceList(schema: any, request: Request): Response {
  // 模拟导出文件
  const blob = new Blob(["偏好类指标明细导出数据"], {
    type: "application/vnd.ms-excel",
  })

  return new Response(
    200,
    {
      "Content-Type": "application/vnd.ms-excel",
      "Content-Disposition": "attachment; filename=preference-indicators.xlsx",
    },
    blob
  )
}

/**
 * 创建偏好类指标相关路由
 */
export function createPreferenceRoutes(server: any) {
  // 偏好类指标明细列表
  server.get("/api/risk-indicators/preference/list", getPreferenceList)

  // 偏好类指标明细导出
  server.post("/api/risk-indicators/preference/export", exportPreferenceList)
}
