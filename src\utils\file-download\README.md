# 大文件下载工具

基于 File System Access API + Web Worker + Streaming 的现代化大文件下载解决方案。

## 🌟 特性

- ✅ **现代化体验**：使用 File System Access API，用户可直接选择保存位置
- ✅ **高性能**：Web Worker 处理，不阻塞主线程
- ✅ **流式下载**：支持大文件，不占用过多内存
- ✅ **进度监控**：实时显示下载进度、速度和剩余时间
- ✅ **自动重试**：网络异常时自动重试
- ✅ **兼容性**：自动降级到传统下载方式
- ✅ **类型安全**：完整的 TypeScript 支持

## 📦 安装

文件已集成在项目中，位于 `src/utils/file-download/` 目录。

## 🚀 快速开始

### 基础用法

```typescript
import { downloadFile } from '@/utils/file-download'

// 简单下载
const result = await downloadFile('https://example.com/large-file.zip')

// 指定文件名
const result = await downloadFile(
  'https://example.com/data.csv', 
  'my-data.csv'
)
```

### 高级用法

```typescript
import { downloadFile, type DownloadProgress } from '@/utils/file-download'

const result = await downloadFile(url, filename, {
  // 文件类型过滤器
  types: [{
    description: 'CSV files',
    accept: { 'text/csv': ['.csv'] }
  }],
  
  // 进度回调
  onProgress: (progress: DownloadProgress) => {
    console.log(`进度: ${progress.percentage.toFixed(1)}%`)
    console.log(`速度: ${formatSpeed(progress.speed)}`)
  },
  
  // 错误回调
  onError: (error: Error) => {
    console.error('下载失败:', error.message)
  },
  
  // 请求头
  headers: {
    'Authorization': 'Bearer token'
  },
  
  // 最大重试次数
  maxRetries: 3
})

if (result.success) {
  console.log('下载成功:', result.filename)
} else {
  console.error('下载失败:', result.error)
}
```

### Vue 组合式函数

```vue
<script setup>
import { useFileDownload } from '@/utils/file-download/examples'

const { 
  download, 
  isDownloading, 
  downloadProgress, 
  formatProgress,
  isSupported 
} = useFileDownload()

async function handleDownload() {
  if (!isSupported) {
    console.warn('浏览器不支持高级下载功能')
    return
  }
  
  await download('https://example.com/file.zip', 'my-file.zip')
}
</script>

<template>
  <div>
    <button @click="handleDownload" :disabled="isDownloading">
      {{ isDownloading ? '下载中...' : '开始下载' }}
    </button>
    
    <div v-if="downloadProgress">
      <progress :value="downloadProgress.percentage" max="100" />
      <p>{{ formatProgress?.percentage }}% - {{ formatProgress?.speed }}</p>
    </div>
  </div>
</template>
```

## 🔧 API 参考

### downloadFile(url, filename?, options?)

主要的下载函数。

**参数:**
- `url: string` - 下载链接
- `filename?: string` - 可选的文件名
- `options?: DownloadOptions` - 下载选项

**返回:** `Promise<DownloadResult>`

### DownloadOptions

```typescript
interface DownloadOptions {
  suggestedName?: string          // 建议文件名
  types?: FilePickerAcceptType[]  // 文件类型过滤器
  chunkSize?: number             // 分块大小（默认 1MB）
  maxRetries?: number            // 最大重试次数（默认 3）
  autoDetectFilename?: boolean   // 自动检测文件名（默认 true）
  headers?: Record<string, string> // 请求头
  onProgress?: (progress: DownloadProgress) => void // 进度回调
  onError?: (error: Error) => void // 错误回调
}
```

### DownloadProgress

```typescript
interface DownloadProgress {
  downloaded: number     // 已下载字节数
  total?: number        // 总文件大小
  percentage: number    // 进度百分比
  speed: number         // 下载速度（字节/秒）
  remainingTime?: number // 预计剩余时间（秒）
}
```

## 🎯 在项目中的使用

### 图表数据导出

已集成在 `spread-analysis/components/chart.vue` 中：

```typescript
// 导出图表数据
async function handleExportData() {
  const csvData = convertToCSV(chartData)
  const blob = new Blob([csvData], { type: 'text/csv' })
  const url = URL.createObjectURL(blob)
  
  const result = await downloadFile(url, 'chart-data.csv', {
    types: [{ description: 'CSV files', accept: { 'text/csv': ['.csv'] } }],
    onProgress: (progress) => {
      console.log(`导出进度: ${progress.percentage}%`)
    }
  })
  
  URL.revokeObjectURL(url)
}
```

### 大型数据集导出

```typescript
import { exportSpreadAnalysisData } from '@/utils/file-download/examples'

// 导出大型数据集
const result = await exportSpreadAnalysisData(
  largeDataArray,
  'spread-analysis-full.csv',
  (progress) => {
    updateProgressUI(progress)
  }
)
```

## 🔄 异步文件下载

专门处理后端需要时间生成文件的下载场景，如从云服务获取数据、数据处理、文件压缩等。

### 🎯 使用场景

- ✅ 后端从云服务（OSS、COS等）获取数据
- ✅ 大数据量需要处理和压缩
- ✅ 复杂报表生成需要时间
- ✅ 多数据源合并导出
- ✅ 实时数据采集和整理

### 🚀 基础用法

```typescript
import { downloadAsyncFile } from '@/utils/file-download/async-file-downloader'

// 简单的异步下载
const result = await downloadAsyncFile(
  '/api/export/large-dataset',  // 后端文件生成接口
  'export-data.zip'
)

if (result.success) {
  console.log(`下载完成，生成耗时: ${result.generationTime}ms`)
} else {
  console.error('下载失败:', result.error)
}
```

### 🎛️ 高级用法

```typescript
import { downloadAsyncFile, type AsyncFileStatus } from '@/utils/file-download/async-file-downloader'

const result = await downloadAsyncFile(
  '/api/export/large-dataset',
  'big-data-export.zip',
  {
    // 轮询配置
    pollInterval: 2000,              // 每2秒查询一次状态
    maxWaitTime: 10 * 60 * 1000,     // 最大等待10分钟
    
    // 状态变化回调
    onStatusChange: (status: AsyncFileStatus) => {
      console.log('状态更新:', status.message)
      
      switch (status.status) {
        case 'pending':
          showMessage('正在准备文件...')
          break
        case 'processing':
          showMessage(`处理中: ${status.progress}%`)
          break
        case 'ready':
          showMessage('文件准备就绪，开始下载')
          break
        case 'failed':
          showError(status.error)
          break
      }
    },
    
    // 文件生成进度回调
    onGenerationProgress: (progress: number) => {
      updateProgressBar(progress)
      console.log(`文件生成进度: ${progress}%`)
    },
    
    // 下载进度回调（文件生成完成后）
    onProgress: (progress: DownloadProgress) => {
      console.log(`下载进度: ${progress.percentage.toFixed(1)}%`)
    },
    
    // 请求配置
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'X-User-ID': userId
    }
  }
)
```

### 🎨 Vue 组合式函数

```vue
<script setup>
import { useAsyncFileDownload } from '@/utils/file-download/async-file-downloader'

const {
  isDownloading,        // 是否正在下载
  downloadStatus,       // 当前状态信息
  downloadProgress,     // 下载进度
  generationProgress,   // 文件生成进度
  download,             // 下载函数
  cancel,              // 取消函数
  formatGenerationProgress,  // 格式化生成进度
  formatWaitTime       // 格式化等待时间
} = useAsyncFileDownload()

async function handleExport() {
  try {
    const result = await download('/api/export/data', 'export.zip', {
      maxWaitTime: 15 * 60 * 1000, // 15分钟超时
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    })
    
    if (result.success) {
      XqMessage.success('导出成功')
    }
  } catch (error) {
    XqMessage.error('导出失败: ' + error.message)
  }
}

function handleCancel() {
  cancel()
  XqMessage.info('已取消导出')
}
</script>

<template>
  <div>
    <!-- 操作按钮 -->
    <XqButton 
      type="primary"
      :loading="isDownloading"
      :disabled="isDownloading"
      @click="handleExport"
    >
      {{ isDownloading ? '导出中...' : '导出数据' }}
    </XqButton>
    
    <XqButton 
      v-if="isDownloading"
      type="danger"
      plain
      @click="handleCancel"
    >
      取消
    </XqButton>

    <!-- 状态显示 -->
    <div v-if="downloadStatus" class="status-display">
      <div class="status-info">
        <h4>{{ getStatusTitle(downloadStatus.status) }}</h4>
        <p>{{ downloadStatus.message }}</p>
        <p v-if="formatWaitTime">{{ formatWaitTime }}</p>
      </div>

      <!-- 文件生成进度 -->
      <div v-if="downloadStatus.status === 'processing'">
        <div class="progress-header">
          <span>文件生成进度</span>
          <span>{{ formatGenerationProgress }}</span>
        </div>
        <el-progress 
          :percentage="generationProgress"
          status="warning"
        />
      </div>

      <!-- 下载进度 -->
      <div v-if="downloadProgress">
        <div class="progress-header">
          <span>下载进度</span>
          <span>{{ downloadProgress.percentage.toFixed(1) }}%</span>
        </div>
        <el-progress 
          :percentage="downloadProgress.percentage"
          status="success"
        />
      </div>
    </div>
  </div>
</template>
```

### 🔧 可控下载器

对于需要更精细控制的场景：

```typescript
import { createAsyncDownloader } from '@/utils/file-download/async-file-downloader'

class DataExportManager {
  private downloader = createAsyncDownloader()
  private currentTaskId: string | null = null

  async exportLargeDataset(filters: any) {
    // 开始下载
    const downloadPromise = this.downloader.download(
      '/api/export/dataset',
      `dataset_${Date.now()}.zip`,
      {
        onStatusChange: (status) => {
          this.handleStatusChange(status)
        },
        onGenerationProgress: (progress) => {
          this.updateUI('generation', progress)
        },
        onProgress: (progress) => {
          this.updateUI('download', progress)
        }
      }
    )

    // 保存任务引用，以便取消
    this.currentTaskId = Date.now().toString()

    try {
      const result = await downloadPromise
      return result
    } finally {
      this.currentTaskId = null
    }
  }

  cancelCurrentDownload() {
    if (this.currentTaskId) {
      this.downloader.cancel()
      this.currentTaskId = null
    }
  }

  private handleStatusChange(status: AsyncFileStatus) {
    // 更新用户界面状态
    this.updateStatusDisplay(status)
    
    // 记录日志
    console.log(`[${new Date().toISOString()}] ${status.message}`)
  }

  private updateUI(type: 'generation' | 'download', progress: number) {
    // 更新对应的进度条
    if (type === 'generation') {
      this.updateGenerationProgress(progress)
    } else {
      this.updateDownloadProgress(progress)
    }
  }
}
```

### 📋 后端API接口规范

为了配合异步下载，后端需要提供以下接口：

#### 1. 发起文件生成请求

```http
POST /api/export/large-dataset
Content-Type: application/json
Authorization: Bearer <token>

{
  "dataType": "user_data",
  "filters": {
    "dateRange": "2024-01-01,2024-12-31",
    "categories": ["A", "B"]
  },
  "format": "zip"
}
```

**响应：**
```json
{
  "success": true,
  "taskId": "task_1640995200000_abc123",
  "message": "文件生成任务已创建",
  "statusUrl": "/api/export/status/task_1640995200000_abc123"
}
```

#### 2. 查询任务状态

```http
GET /api/export/status/:taskId
Authorization: Bearer <token>
```

**响应示例：**
```json
{
  "status": "processing",
  "progress": 45,
  "message": "正在处理数据...",
  "estimatedTime": 120,
  "downloadUrl": null
}
```

```json
{
  "status": "ready",
  "progress": 100,
  "message": "文件生成完成",
  "estimatedTime": 0,
  "downloadUrl": "/api/download/task_1640995200000_abc123/export.zip"
}
```

#### 3. 下载文件

```http
GET /api/download/:taskId/:filename
Authorization: Bearer <token>
Range: bytes=0-1023 (可选，支持断点续传)
```

**完整的后端实现示例请参考 `backend-api-example.ts`**

### 🎭 用户体验最佳实践

#### 1. 智能等待提示

```typescript
const getWaitingMessage = (status: AsyncFileStatus) => {
  const messages = {
    'pending': '正在准备数据导出...',
    'processing': (() => {
      const step = Math.floor(status.progress / 33)
      const steps = [
        '正在从云端获取数据...',
        '正在处理和分析数据...',
        '正在生成导出文件...'
      ]
      return steps[step] || '正在处理...'
    })()
  }
  return messages[status.status] || status.message
}
```

#### 2. 预估时间显示

```typescript
const formatEstimatedTime = (seconds: number) => {
  if (seconds < 60) {
    return `预计还需 ${seconds} 秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    return `预计还需 ${minutes} 分钟`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `预计还需 ${hours} 小时 ${minutes} 分钟`
  }
}
```

#### 3. 分步骤进度展示

```typescript
const getProgressSteps = (progress: number) => {
  return [
    { name: '获取数据', active: progress >= 0, completed: progress >= 30 },
    { name: '数据处理', active: progress >= 30, completed: progress >= 70 },
    { name: '文件生成', active: progress >= 70, completed: progress >= 100 }
  ]
}
```

### ⚠️ 注意事项

1. **超时处理**：设置合理的 `maxWaitTime`，避免无限等待
2. **轮询频率**：`pollInterval` 不要设置过短，避免给服务器造成压力
3. **用户反馈**：及时更新状态和进度，让用户了解处理进展
4. **错误处理**：妥善处理网络错误、超时等异常情况
5. **资源清理**：确保及时清理临时文件和任务记录
6. **权限验证**：每次状态查询都应验证用户权限

## 🌐 浏览器版本兼容性详解

### 📊 版本支持对照表

| 浏览器      | 现代下载支持版本 | 发布时间   | 传统下载兜底 | 状态         |
|-------------|------------------|------------|--------------|--------------|
| **Chrome**  | 86+              | 2020年10月 | ✅ 支持       | ✅ 推荐       |
| **Edge**    | 86+              | 2020年10月 | ✅ 支持       | ✅ 推荐       |
| **Opera**   | 72+              | 2020年11月 | ✅ 支持       | ✅ 支持       |
| **Firefox** | 不支持           | -          | ✅ 支持       | ⚠️ 仅传统下载 |
| **Safari**  | 不支持           | -          | ✅ 支持       | ⚠️ 仅传统下载 |

### 🔍 自动兼容性检测

```typescript
import { 
  getDownloadCapabilities, 
  getBrowserCompatibilityInfo 
} from '@/utils/file-download'

// 获取详细的兼容性信息
const capabilities = getDownloadCapabilities()
console.log('浏览器能力:', capabilities)

// 输出示例：
// {
//   browserName: "Chrome",
//   browserVersion: "91",
//   canUseModernDownload: true,
//   canUseWebWorker: true,
//   canUseStreaming: true,
//   recommendedMethod: "modern",
//   limitations: [],
//   recommendations: []
// }

// 获取基础浏览器信息
const browserInfo = getBrowserCompatibilityInfo()
console.log('浏览器信息:', browserInfo)
```

### 🎯 按浏览器的功能差异

#### Chrome 86+ / Edge 86+
- ✅ **完整支持**：所有现代下载功能
- ✅ **File System Access API**：用户可选择保存位置
- ✅ **Web Worker + Streaming**：后台处理，不阻塞界面
- ✅ **断点续传**：支持大文件分块下载
- ✅ **进度监控**：实时显示下载进度

#### Chrome < 86 / Edge < 86
- ❌ **File System Access API**：不支持选择保存位置
- ✅ **Web Worker + Streaming**：支持后台处理
- ⚠️ **自动降级**：使用传统 Blob 下载
- ⚠️ **文件命名**：浏览器自动命名，用户无法选择位置

#### Firefox (所有版本)
- ❌ **File System Access API**：Mozilla 暂无支持计划
- ✅ **Web Worker + Streaming**：支持后台处理
- ✅ **传统下载**：完全支持 Blob 下载
- ⚠️ **用户体验**：无法选择保存位置，但功能完整

#### Safari (所有版本)
- ❌ **File System Access API**：Apple 暂无支持计划
- ✅ **Web Worker**：支持后台处理
- ⚠️ **Streaming 限制**：部分版本可能有兼容性问题
- ✅ **传统下载**：支持 Blob 下载

### 🔧 智能降级策略

系统会自动检测浏览器能力并选择最佳下载方式：

```typescript
// 自动选择最佳下载方式
async function smartDownload(url: string, filename: string) {
  const capabilities = getDownloadCapabilities()
  
  if (capabilities.canUseModernDownload) {
    // 使用现代下载
    console.log('使用 File System Access API')
    return await downloadFile(url, filename, {
      types: [{ 
        description: 'All files', 
        accept: { '*/*': ['*'] } 
      }]
    })
  } else {
    // 降级到传统下载
    console.log('降级到传统下载方式')
    return await downloadFileFallback(url, filename)
  }
}

// 传统下载实现
async function downloadFileFallback(url: string, filename: string) {
  const response = await fetch(url)
  const blob = await response.blob()
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = filename
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(link.href)
  return { success: true, filename }
}
```

### 💡 版本检测和用户提示

```typescript
// 检测并提示用户升级
function checkBrowserAndNotify() {
  const capabilities = getDownloadCapabilities()
  
  if (capabilities.recommendations.length > 0) {
    // 显示升级建议
    const message = capabilities.recommendations.join('\n')
    
    XqMessage.warning({
      title: '浏览器兼容性提示',
      message: message,
      duration: 10000,
      showClose: true
    })
  }
  
  if (capabilities.limitations.length > 0) {
    // 显示功能限制提示
    console.warn('当前浏览器功能限制:', capabilities.limitations)
  }
}

// 在应用启动时检测
onMounted(() => {
  checkBrowserAndNotify()
})
```

### 🎨 用户界面适配

根据浏览器能力调整UI显示：

```vue
<template>
  <div class="download-section">
    <!-- 现代浏览器显示高级功能 -->
    <div v-if="capabilities.canUseModernDownload" class="modern-download">
      <XqButton type="primary" @click="handleModernDownload">
        选择位置并下载
      </XqButton>
      <p class="feature-tip">✅ 您可以选择文件保存位置</p>
    </div>
    
    <!-- 传统浏览器显示基础功能 -->
    <div v-else class="fallback-download">
      <XqButton @click="handleFallbackDownload">
        下载文件
      </XqButton>
      <p class="limitation-tip">
        ⚠️ 文件将保存到默认下载目录
        <span v-if="capabilities.recommendations.length">
          - <a href="javascript:void(0)" @click="showUpgradeInfo">升级浏览器获得更好体验</a>
        </span>
      </p>
    </div>
    
    <!-- 浏览器信息显示 -->
    <div class="browser-info">
      <small>
        当前浏览器: {{ capabilities.browserName }} {{ capabilities.browserVersion }}
        ({{ capabilities.recommendedMethod === 'modern' ? '现代下载' : '传统下载' }})
      </small>
    </div>
  </div>
</template>

<script setup>
const capabilities = getDownloadCapabilities()

function showUpgradeInfo() {
  XqDialog.alert({
    title: '浏览器升级建议',
    message: capabilities.recommendations.join('\n'),
    confirmButtonText: '我知道了'
  })
}
</script>
```

### 📈 兼容性测试

为了确保在不同浏览器中的表现，建议进行以下测试：

```typescript
// 兼容性测试套件
async function runCompatibilityTests() {
  const results = []
  
  // 测试1: 基础下载功能
  try {
    await testBasicDownload()
    results.push({ test: 'basic', status: 'pass' })
  } catch (error) {
    results.push({ test: 'basic', status: 'fail', error: error.message })
  }
  
  // 测试2: 大文件下载
  try {
    await testLargeFileDownload()
    results.push({ test: 'large', status: 'pass' })
  } catch (error) {
    results.push({ test: 'large', status: 'fail', error: error.message })
  }
  
  // 测试3: 并发下载
  try {
    await testConcurrentDownload()
    results.push({ test: 'concurrent', status: 'pass' })
  } catch (error) {
    results.push({ test: 'concurrent', status: 'fail', error: error.message })
  }
  
  console.table(results)
  return results
}
```

### ⚠️ 已知问题和解决方案

#### 问题1: Firefox 中大文件下载可能卡顿
```typescript
// 解决方案：使用更小的分块大小
const firefoxOptions = {
  chunkSize: 512 * 1024, // Firefox 使用 512KB 分块
  maxRetries: 5
}
```

#### 问题2: Safari 中流式处理可能有问题
```typescript
// 解决方案：检测 Safari 并使用传统方式
if (capabilities.browserName === 'Safari') {
  // 强制使用传统下载
  await downloadFileFallback(url, filename)
}
```

#### 问题3: 老版本 Edge 中的兼容性问题
```typescript
// 解决方案：版本检测和功能降级
if (capabilities.browserName === 'Edge' && 
    parseInt(capabilities.browserVersion) < 86) {
  // 使用兼容性更好的配置
  options.useWebWorker = false
}
```
