import Layout from "@/layout/LayoutProxy"

export default {
  path: "/asset-allocation",
  name: "AssetAllocation",
  component: Layout,
  redirect: "/asset-allocation/overview",
  meta: {
    icon: "homeFilled",
    title: "大类资产配置",
  },
  children: [
    {
      path: "/asset-allocation/overview",
      name: "AssetAllocationOverview",
      component: () => import("@/modules/asset-allocation/views/overview.vue"),
      meta: {
        title: "大类资产配置(穿透前&穿透后)",
      },
    },
    {
      path: "/asset-allocation/credit-bond-rating",
      name: "AssetAllocationCreditBondRating",
      component: () =>
        import("@/modules/asset-allocation/views/credit-bond-rating.vue"),
      meta: {
        title: "信用债评级分布",
      },
    },
    {
      path: "/asset-allocation/credit-bond-risk",
      name: "AssetAllocationCreditBondRisk",
      component: () =>
        import("@/modules/asset-allocation/views/credit-bond-risk.vue"),
      meta: {
        title: "信用债风险情况总览",
      },
    },
    {
      path: "/asset-allocation/fund-risk",
      name: "AssetAllocationFundRisk",
      component: () => import("@/modules/asset-allocation/views/fund-risk.vue"),
      meta: {
        title: "基金风险情况总览",
      },
    },
    {
      path: "/asset-allocation/deposit-risk",
      name: "AssetAllocationDepositRisk",
      component: () =>
        import("@/modules/asset-allocation/views/deposit-risk.vue"),
      meta: {
        title: "存款风险情况总览",
      },
    },
    {
      path: "/asset-allocation/entrusted-investment-risk",
      name: "AssetAllocationEntrustedInvestmentRisk",
      component: () =>
        import(
          "@/modules/asset-allocation/views/entrusted-investment-risk.vue"
        ),
      meta: {
        title: "委外风险情况总览",
      },
    },
  ],
} as RouteConfigsTable
