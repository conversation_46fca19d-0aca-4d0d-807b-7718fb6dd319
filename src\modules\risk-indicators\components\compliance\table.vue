<script setup lang="ts">
import { useQueryForm } from "../../hooks/compliance/useQueryForm"

defineOptions({
  name: "ComplianceTable",
})

const { tableData, total, pagination, listLoading, handlePageChange, handlePageSizeChange } =
  useQueryForm()

// 表头单元格样式配置
const getHeaderCellClassName = ({ column }: { column: any; columnIndex: number }) => {
  // 根据分组表头设置样式
  if (column.level === 1) {
    const label = column.label
    if (label === "产品信息") {
      return "product-group-header"
    } else if (label === "指标基础信息") {
      return "indicator-group-header"
    } else if (label === "指标使用情况") {
      return "usage-group-header"
    }
  }

  // 子表头样式
  const parentLabel = column.parent?.label
  if (parentLabel === "产品信息") {
    return "product-header"
  } else if (parentLabel === "指标基础信息") {
    return "indicator-header"
  } else if (parentLabel === "指标使用情况") {
    return "usage-header"
  }

  return ""
}

// 单元格样式配置
const getCellClassName = ({
  column,
  row,
  rowIndex,
}: {
  column: any
  row: any
  rowIndex: number
}) => {
  const isOddRow = rowIndex % 2 === 1
  let className = ""

  // 根据列的分组设置背景色
  const parentLabel = column.parent?.label
  if (parentLabel === "产品信息") {
    className = isOddRow ? "product-cell-striped" : "product-cell"
  } else if (parentLabel === "指标基础信息") {
    className = isOddRow ? "indicator-cell-striped" : "indicator-cell"
  } else if (parentLabel === "指标使用情况") {
    className = isOddRow ? "usage-cell-striped" : "usage-cell"
  } else {
    className = isOddRow ? "normal-cell-striped" : ""
  }

  // 指标状态列特殊处理 - 违规和预警显示红色文字
  if (
    column.property === "vcItStatus" &&
    (row.vcItStatus === "违规" || row.vcItStatus === "预警")
  ) {
    className += " status-error"
  }

  return className
}
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- 表格 -->
    <xq-table
      :data="tableData"
      :loading="listLoading"
      class="flex-1"
      :stripe="false"
      border
      :header-cell-class-name="getHeaderCellClassName"
      :cell-class-name="getCellClassName"
      style="width: 100%"
    >
      <!-- 产品信息分组 -->
      <xq-table-column label="产品信息" align="center">
        <xq-table-column prop="vcFundCode" label="产品代码" min-width="120" align="center" />
        <xq-table-column prop="vcFundName" label="产品名称" min-width="150" align="left" />
        <xq-table-column prop="vcFundCode" label="专户代码" min-width="100" align="center" />
        <xq-table-column prop="vcFundName" label="专户名称" min-width="100" align="center" />
        <xq-table-column prop="vcManagerName" label="投资部门" min-width="100" align="center" />
      </xq-table-column>

      <!-- 指标基础信息分组 -->
      <xq-table-column label="指标基础信息" align="center">
        <xq-table-column prop="vcItCode" label="日期" min-width="100" align="center" />
        <xq-table-column prop="vcItCode" label="指标编号" min-width="120" align="center" />
        <xq-table-column prop="vcItName" label="指标名称" min-width="200" align="left" />
        <xq-table-column prop="vcItCode" label="资产代码" min-width="100" align="center" />
        <xq-table-column prop="vcItName" label="资产名称" min-width="100" align="center" />
        <xq-table-column prop="vcOutlineNum" label="限额值" min-width="100" align="right" />
      </xq-table-column>

      <!-- 指标使用情况分组 -->
      <xq-table-column label="指标使用情况" align="center">
        <xq-table-column prop="fDactualMoney" label="监控数据" min-width="100" align="right" />
        <xq-table-column prop="vcItStatus" label="指标状态" min-width="100" align="center" />
      </xq-table-column>
    </xq-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <xq-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 产品信息相关样式 - 蓝色系 */
:deep(.product-group-header) {
  background-color: #6b7fd7 !important;
  color: white !important;
  font-weight: 500;
}

:deep(.product-header) {
  background-color: #6b7fd7 !important;
  color: white !important;
  font-weight: 500;
}

:deep(.product-cell) {
  background-color: #c7d2fe !important;
  color: #374151;
}

:deep(.product-cell-striped) {
  background-color: #a5b4fc !important;
  color: #374151;
}

/* 指标基础信息相关样式 - 橙色系 */
:deep(.indicator-group-header) {
  background-color: #fb923c !important;
  color: white !important;
  font-weight: 500;
}

:deep(.indicator-header) {
  background-color: #fb923c !important;
  color: white !important;
  font-weight: 500;
}

:deep(.indicator-cell) {
  background-color: #fed7aa !important;
  color: #374151;
}

:deep(.indicator-cell-striped) {
  background-color: #fdba74 !important;
  color: #374151;
}

/* 指标使用情况相关样式 - 绿色系 */
:deep(.usage-group-header) {
  background-color: #4ade80 !important;
  color: white !important;
  font-weight: 500;
}

:deep(.usage-header) {
  background-color: #4ade80 !important;
  color: white !important;
  font-weight: 500;
}

:deep(.usage-cell) {
  background-color: #bbf7d0 !important;
  color: #374151;
}

:deep(.usage-cell-striped) {
  background-color: #86efac !important;
  color: #374151;
}

/* 指标状态错误文字样式 */
:deep(.status-error) {
  color: #dc2626 !important;
  font-weight: 500;
}

/* 普通列的斑马纹 */
:deep(.normal-cell-striped) {
  background-color: #f9fafb !important;
}

/* 悬停效果 */
:deep(.xq-table__row:hover td) {
  background-color: rgba(59, 130, 246, 0.05) !important;
}

/* 表头居中对齐 */
:deep(.#{$ns}-table__header .#{$ns}-table__cell > .cell) {
  text-align: center;
  justify-content: center;
}

/* 分组表头居中对齐 */
:deep(.#{$ns}-table__header-wrapper .#{$ns}-table__header .#{$ns}-table__cell) {
  text-align: center;
}

/* 确保表格占满容器宽度 */
:deep(.#{$ns}-table) {
  width: 100% !important;
}

/* 表格列自适应 */
:deep(.#{$ns}-table__body-wrapper) {
  width: 100%;
}
</style>
