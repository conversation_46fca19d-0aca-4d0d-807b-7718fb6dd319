/** 处理环境变量 */
const _wrapperEnv = (envConf: Recordable): ViteEnv => {
  /** 此处为默认值 */
  const ret: ViteEnv = {
    VITE_PORT: 8848,
    VITE_PUBLIC_PATH: "",
    VITE_CDN: false,
    VITE_COMPRESSION: "none",
    VITE_AUTH: "off",
    VITE_APP_NAME: "vue3-vite-template",
    VITE_USE_MOCK: "off",
    VITE_APP_BASE_API: "/",
  }

  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, "\n")
    realName = realName === "true" ? true : realName === "false" ? false : realName

    if (envName === "VITE_PORT") {
      realName = Number(realName)
    }
    ret[envName] = realName
    if (typeof realName === "string") {
      process.env[envName] = realName
    } else if (typeof realName === "object") {
      process.env[envName] = JSON.stringify(realName)
    }
  }
  return ret
}

export { _wrapperEnv as wrapperEnv }
