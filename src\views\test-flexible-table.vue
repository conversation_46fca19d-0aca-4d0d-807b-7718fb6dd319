<script setup lang="ts">
import { ref } from "vue"
import { createGroupedTableStyleFunctions } from "@/utils/tableStyleUtils"

defineOptions({
  name: "TestFlexibleTable",
})

// 模拟数据
const tableData = ref([
  {
    name: "张三",
    age: 25,
    department: "技术部",
    salary: 8000,
    bonus: 2000,
    performance: "优秀",
    status: "在职"
  },
  {
    name: "李四", 
    age: 30,
    department: "产品部",
    salary: 12000,
    bonus: 3000,
    performance: "良好",
    status: "在职"
  },
  {
    name: "王五",
    age: 28,
    department: "设计部", 
    salary: 10000,
    bonus: 1500,
    performance: "一般",
    status: "离职"
  }
])

// 场景配置
const scenarios = ref([
  {
    name: "员工管理",
    config: {
      groupOneLabel: "基本信息",
      groupTwoLabel: "薪资数据",
      groupThreeLabel: "评估结果"
    }
  },
  {
    name: "人事档案", 
    config: {
      groupOneLabel: "个人资料",
      groupTwoLabel: "工作信息",
      groupThreeLabel: "状态管理"
    }
  },
  {
    name: "财务报表",
    config: {
      groupOneLabel: "员工信息",
      groupTwoLabel: "收入明细", 
      groupThreeLabel: "绩效评价"
    }
  }
])

// 当前选中的场景
const currentScenario = ref(0)

// 根据当前场景创建样式函数
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions(
  scenarios.value[currentScenario.value].config
)

// 切换场景时重新创建样式函数
function handleScenarioChange() {
  // 注意：在实际应用中，这里需要重新创建组件或使用响应式的方式
  // 这个示例主要展示配置的灵活性
  location.reload()
}
</script>

<template>
  <div class="p-6 h-screen">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">灵活分组表格配置示例</h1>
      
      <!-- 场景选择器 -->
      <div class="mb-4">
        <label class="mr-2">选择场景：</label>
        <select 
          v-model="currentScenario" 
          @change="handleScenarioChange"
          class="border rounded px-3 py-1"
        >
          <option 
            v-for="(scenario, index) in scenarios" 
            :key="index" 
            :value="index"
          >
            {{ scenario.name }}
          </option>
        </select>
      </div>

      <!-- 当前配置显示 -->
      <div class="bg-gray-100 p-3 rounded mb-4">
        <h3 class="font-semibold mb-2">当前配置：</h3>
        <div class="text-sm">
          <div>第一组：{{ scenarios[currentScenario].config.groupOneLabel }}</div>
          <div>第二组：{{ scenarios[currentScenario].config.groupTwoLabel }}</div>
          <div>第三组：{{ scenarios[currentScenario].config.groupThreeLabel }}</div>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <div class="h-96">
      <xq-table
        :data="tableData"
        :stripe="false"
        border
        :header-cell-class-name="getHeaderCellClassName"
        :cell-class-name="getCellClassName"
        style="width: 100%"
      >
        <!-- 第一个分组 -->
        <xq-table-column :label="scenarios[currentScenario].config.groupOneLabel" align="center">
          <xq-table-column prop="name" label="姓名" min-width="100" align="center" />
          <xq-table-column prop="age" label="年龄" min-width="80" align="center" />
          <xq-table-column prop="department" label="部门" min-width="100" align="center" />
        </xq-table-column>

        <!-- 第二个分组 -->
        <xq-table-column :label="scenarios[currentScenario].config.groupTwoLabel" align="center">
          <xq-table-column prop="salary" label="基本工资" min-width="100" align="right" />
          <xq-table-column prop="bonus" label="奖金" min-width="100" align="right" />
        </xq-table-column>

        <!-- 第三个分组 -->
        <xq-table-column :label="scenarios[currentScenario].config.groupThreeLabel" align="center">
          <xq-table-column prop="performance" label="绩效评级" min-width="100" align="center" />
          <xq-table-column prop="status" label="状态" min-width="80" align="center" />
        </xq-table-column>
      </xq-table>
    </div>

    <div class="mt-6 text-sm text-gray-600">
      <p><strong>说明：</strong></p>
      <p>• 这个示例展示了如何使用不同的分组标签配置</p>
      <p>• 每个场景使用相同的数据，但分组名称不同</p>
      <p>• 样式会根据分组位置自动应用（第一组蓝色，第二组橙色，第三组绿色）</p>
      <p>• 在实际应用中，可以根据业务需求动态配置分组标签</p>
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
