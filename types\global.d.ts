import type {
  ComponentPublicInstance,
  FunctionalComponent,
  PropType as VuePropType,
  VNode,
} from "vue"

import type { ECharts } from "echarts"

import type { IconifyIcon } from "@iconify/vue"

/**
 * 全局类型声明，无需引入直接在 `.vue` 、`.ts` 、`.tsx` 文件使用即可获得类型提示
 */
declare global {
  /**
   * 平台的名称、版本、依赖、最后构建时间的类型提示
   */
  const __APP_INFO__: {
    pkg: {
      name: string
      version: string
      dependencies: Recordable<string>
      devDependencies: Recordable<string>
    }
    lastBuildTime: string
  }
  /**
   * Window 的类型提示
   */
  interface Window {
    // Global vue app instance
    __APP__: App<Element>
    __POWERED_BY_QIANKUN__: boolean
    webkitCancelAnimationFrame: (handle: number) => void
    mozCancelAnimationFrame: (handle: number) => void
    oCancelAnimationFrame: (handle: number) => void
    msCancelAnimationFrame: (handle: number) => void
    webkitRequestAnimationFrame: (callback: FrameRequestCallback) => number
    mozRequestAnimationFrame: (callback: FrameRequestCallback) => number
    oRequestAnimationFrame: (callback: FrameRequestCallback) => number
    msRequestAnimationFrame: (callback: FrameRequestCallback) => number
  }

  /**
   * 打包压缩格式的类型声明
   */
  type ViteCompression =
    | "none"
    | "gzip"
    | "brotli"
    | "both"
    | "gzip-clear"
    | "brotli-clear"
    | "both-clear"

  /**
   * 全局自定义环境变量的类型声明
   * @see {@link https://yiming_chang.gitee.io/pure-admin-doc/pages/config/#%E5%85%B7%E4%BD%93%E9%85%8D%E7%BD%AE}
   */
  interface ViteEnv {
    VITE_PORT: number
    VITE_PUBLIC_PATH: string
    VITE_CDN: boolean
    VITE_COMPRESSION: ViteCompression
    VITE_AUTH: string
    VITE_APP_NAME: string
    VITE_USE_MOCK: string
    VITE_APP_BASE_API: string
  }

  // File System Access API 类型声明
  interface FilePickerAcceptType {
    description?: string
    accept: Record<string, string[]>
  }

  interface FilePickerOptions {
    suggestedName?: string
    types?: FilePickerAcceptType[]
    excludeAcceptAllOption?: boolean
  }

  interface FileSystemFileHandle {
    readonly kind: "file"
    readonly name: string
    createWritable(): Promise<FileSystemWritableFileStream>
    createWritableFileStream(): Promise<FileSystemWritableFileStream>
    getFile(): Promise<File>
  }

  interface FileSystemWritableFileStream extends WritableStream {
    write(data: BufferSource | Blob | string): Promise<void>
    seek(position: number): Promise<void>
    truncate(size: number): Promise<void>
    close(): Promise<void>
    abort(): Promise<void>
  }

  interface Window {
    showSaveFilePicker(
      options?: FilePickerOptions
    ): Promise<FileSystemFileHandle>
    showOpenFilePicker(
      options?: FilePickerOptions
    ): Promise<FileSystemFileHandle[]>
  }

  type GlobalStateChangeCallback = (state: any, prev: any) => void
  interface QianKunProps {
    container?: HTMLElement
    baseroute?: string
    onGlobalStateChange?: (callback: GlobalStateChangeCallback) => void
    on?: (event: string, callback: (...args: any[]) => void) => void
    overrideOriginHistory?: (history: RouterHistory) => RouterHistory
  }
}
