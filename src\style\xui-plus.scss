@forward "@xquant/x-ui-plus/theme-chalk/src/common/var.scss" with (
  $font-size: (
    "base": 12px,
    "small": 12px,
    "extra-small": 12px,
  ),
  $text-color: (
    "primary": #262626,
    "regular": #262626,
    "secondary": #595959,
    "placeholder": #a6a6a6,
    "disabled": #8c8c8c,
  ),
  $colors: (
    "primary": (
      "base": #407fff,
      "light-3": #6698ff,
      "light-5": #8cb2ff,
      "dark-2": "#3365CC",
    ),
    "success": (
      "base": #52c41a,
    ),
    "warning": (
      "base": #faad14,
    ),
    "danger": (
      "base": #f5222d,
    ),
    "error": (
      "base": #f5222d,
    ),
  ),
  $common-component-size: (
    "large": 20px,
    "default": 20px,
    "small": 20px,
  ),
  $border-color: (
    "": #d9d9d9,
    "light": #d9d9d9,
    "lighter": #e5e5e5,
    "extra-light": #f0f0f0,
  ),
  $border-radius: (
    "base": 2px,
  ),
  $pagination: (
    "font-size": 12px,
    "button-width": 20px,
    "button-height": 20px,
    "button-width-small": 20px,
    "button-height-small": 20px,
    "item-gap": 8px,
  ),
  $checkbox: (
    "font-size": 12px,
    "border-radius": 2px,
    "input-height": 12px,
    "input-width": 12px,
  ),
  $radio: (
    "input-height": 12px,
    "input-width": 12px,
  ),
  $slider: (
    "height": 4px,
    "button-size": 14px,
    "button-wrapper-size": 30px,
    "button-wrapper-offset": -13px,
  ),
  $transfer: (
    "filter-height": 26px,
  ),
  $avatar: (
    "bg-color": #407fff,
  ),
  $table: (
    "border-color": #e8eaed,
    "header-bg-color": #d8e5ff,
    "header-text-color": #262626,
    "row-hover-bg-color": #ecf2ff,
    "current-row-bg-color": #c6d8ff,
  ),
  $table-font-size: (
    "default": 12px,
    "small": 12px,
  ),
  $table-padding: (
    "default": 2px 0,
    "small": 2px 0,
  ),
  $edit-table-size: (
    "large": 20px,
    "default": 20px,
    "small": 20px,
  ),
  $dialog: (
    "title-font-size": 14px,
  ),
  $notification: (
    "radius": 4px,
  ),
  $popover: (
    "title-font-size": 14px,
  ),
  $messagebox: (
    "font-size": 16px,
    "padding-primary": 16px,
  ),
  $tree: (
    "node-content-height": 24px,
  ),
  $overlay-color: (
    "lighter": rgba(51, 51, 51, 0.6),
  ),
  $fill-color: (
    "light": #f2f3f5,
  ),
  $button: (
    "text-color": #407fff,
    "border-color": #407fff,
    "hover-text-color": #6698ff,
    "hover-border-color": #6698ff,
    "hover-bg-color": #fff,
    "active-border-color": #3365cc,
    "disabled-text-color": #8c8c8c,
    "disabled-border-color": #e5e5e5,
  ),
  $tabs: (
    "header-height": 24px,
  ),
  $box-shadow: (
    "light": 0 4px 10px 0 rgba(0, 0, 0, 0.15),
  ),
  $border-color-hover: "#407fff"
);
@use "@xquant/x-ui-plus/theme-chalk/src/index.scss" as *;
@use "@xquant/x-ui-plus/theme-chalk/src/mixins/config.scss" as *;

:root {
  --#{$var-namespace}-font-line-height-primary: 20px;
}

/****************************************************************************
* xq-tabs 重置样式
****************************************************************************/
.#{$namespace}-tabs__item.is-active {
  font-weight: bold;
}

.#{$namespace}-tabs--card > .#{$namespace}-tabs__header {
  border-bottom: none;

  &::before {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 1px;
    content: "";
    background-color: var(--#{$var-namespace}-border-color-light);
  }

  .#{$namespace}-tabs__nav {
    border: none;
  }

  .#{$namespace}-tabs__item {
    background-color: rgb(0 0 0 / 2%);
    border: 1px solid var(--#{$var-namespace}-border-color-light);
    border-bottom-color: transparent;
    border-radius: 2px 2px 0 0;

    &:first-child {
      border-left: 1px solid var(--#{$var-namespace}-border-color-light);
    }

    & + .#{$namespace}-tabs__item {
      margin-left: 2px;
    }

    &.is-active {
      background-color: var(--#{$var-namespace}-fill-color-blank);

      &::after {
        position: absolute;
        bottom: -2px;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 2px;
        content: "";
        background-color: var(--#{$var-namespace}-fill-color-blank);
      }

      &.is-closable {
        padding-right: 12px;
        padding-left: 12px;
      }
    }
  }
}

.#{$namespace}-tabs {
  &__header {
    margin: 0 0 12px;
  }

  &__new-tab {
    margin: 1px 0 1px 10px;
  }

  &__nav-next,
  &__nav-prev {
    line-height: 24px;
  }

  &__item {
    padding: 0 12px;
  }

  &__nav-wrap::after {
    height: 1px;
  }
}

.#{$namespace}-tabs--top.#{$namespace}-tabs--border-card
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:last-child,
.#{$namespace}-tabs--top.#{$namespace}-tabs--card
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:last-child,
.#{$namespace}-tabs--top
  .#{$namespace}-tabs--left
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:last-child,
.#{$namespace}-tabs--top
  .#{$namespace}-tabs--right
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:last-child,
.#{$namespace}-tabs--bottom.#{$namespace}-tabs--border-card
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:last-child,
.#{$namespace}-tabs--bottom.#{$namespace}-tabs--card
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:last-child,
.#{$namespace}-tabs--bottom
  .#{$namespace}-tabs--left
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:last-child,
.#{$namespace}-tabs--bottom
  .#{$namespace}-tabs--right
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:last-child {
  padding-right: 12px;
}
.#{$namespace}-tabs--top.#{$namespace}-tabs--border-card
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:nth-child(2),
.#{$namespace}-tabs--top.#{$namespace}-tabs--card
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:nth-child(2),
.#{$namespace}-tabs--top
  .#{$namespace}-tabs--left
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:nth-child(2),
.#{$namespace}-tabs--top
  .#{$namespace}-tabs--right
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:nth-child(2),
.#{$namespace}-tabs--bottom.#{$namespace}-tabs--border-card
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:nth-child(2),
.#{$namespace}-tabs--bottom.#{$namespace}-tabs--card
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:nth-child(2),
.#{$namespace}-tabs--bottom
  .#{$namespace}-tabs--left
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:nth-child(2),
.#{$namespace}-tabs--bottom
  .#{$namespace}-tabs--right
  > .#{$namespace}-tabs__header
  .#{$namespace}-tabs__item:nth-child(2) {
  padding-left: 12px;
}

/****************************************************************************
* 数据录入表单项 重置样式
****************************************************************************/
.#{$namespace}-input {
  --#{$var-namespace}-input-height: var(--#{$var-namespace}-component-size-small);

  .#{$namespace}-button {
    height: 20px;
    line-height: 20px;

    &:has([class*="#{$namespace}-icon"]) {
      padding: 0;
    }
  }
}

// .#{$namespace}-input-number.is-controls-right {
//   --#{$namespace}-input-number-controls-height: 9px;
// }

.#{$namespace}-checkbox .#{$namespace}-checkbox__inner::after {
  width: 2px;
  height: 6px;
}

.#{$namespace}-checkbox-button__inner,
.#{$namespace}-radio-button__inner {
  padding: 4px 8px;
}

.#{$namespace}-radio {
  height: 20px;
  margin-right: 20px;

  .#{$namespace}-radio__inner {
    width: 12px;
    height: 12px;

    &::after {
      width: 5px;
      height: 5px;
    }
  }

  .#{$namespace}-radio__input.is-checked .#{$namespace}-radio__inner {
    background-color: var(--#{$var-namespace}-radio-input-bg-color);

    &::after {
      --#{$var-namespace}-color-white: var(--#{$var-namespace}-color-primary);
    }
  }

  &--small {
    .#{$namespace}-radio__inner::after {
      width: 5px;
      height: 5px;
    }
  }
}

.#{$namespace}-select {
  &__wrapper {
    min-height: 20px;
    padding: 0 12px;
    font-size: 12px;
    line-height: 20px;

    &.is-filterable {
      .#{$namespace}-select__selection {
        flex-wrap: nowrap;
      }
      .#{$namespace}-select__input-wrapper {
        min-width: 12px;

        .#{$namespace}-select__input {
          height: 100%;
        }
      }
    }
  }
  &__selection .#{$namespace}-tag {
    height: 14px;
    border-radius: 2px;
  }

  &-dropdown__item {
    height: 24px;
    line-height: 24px;
  }

  &-group__split-dash,
  &-group__wrap:not(:last-of-type)::after {
    background: var(--#{$var-namespace}-border-color-lighter);
  }
}

.#{$namespace}-date-table td {
  & .#{$namespace}-date-table-cell .#{$namespace}-date-table-cell__text {
    border-radius: 4px;
  }

  &.end-date .#{$namespace}-date-table-cell {
    border-radius: 0;
  }
}

.#{$namespace}-date-table.is-week-mode .#{$namespace}-date-table__row:hover {
  td:first-child,
  td:last-child {
    .#{$namespace}-date-table-cell {
      border-radius: 0;
    }
  }
}

.#{$namespace}-year-table,
.#{$namespace}-month-table {
  td .cell {
    border-radius: 4px;
  }
}

.#{$namespace}-treeselect {
  &__control {
    height: var(--#{$var-namespace}-component-size-small);
    line-height: 1;
    border-radius: 2px;
  }

  &__multi-value {
    line-height: 14px !important;
  }

  &__placeholder,
  &__single-value {
    line-height: 1.4 !important;
  }
}

.#{$namespace}-slider {
  height: 22px;
}

.#{$namespace}-rate {
  height: var(--xq-rate-height);
}

.#{$namespace}-form-item,
.#{$namespace}-form-item--default,
.#{$namespace}-form-item--small {
  --font-size: 12px;

  margin-bottom: 4px;

  .#{$namespace}-form-item__label {
    height: 20px;
    padding-right: 4px;
    line-height: 20px;
  }

  .#{$namespace}-form-item__content {
    line-height: 20px;
  }
}

.#{$namespace}-form-item .#{$namespace}-form-item {
  margin-bottom: 4px;
}

// treeselect 在表单中报错的样式
.#{$namespace}-form {
  .#{$namespace}-button {
    height: 20px;
    padding: 0 8px;
  }

  &-item.is-error {
    .#{$namespace}-treeselect__control {
      border: 1px solid var(--#{$var-namespace}-color-danger) !important;
    }
  }
}

/****************************************************************************
* xq-table xq-pro-table xp-edit-table-column 重置样式
****************************************************************************/
.#{$namespace}-table {
  --#{$var-namespace}-table-row-height-small: 20px;
  --#{$var-namespace}-table-row-height-default: 24px;
  --#{$var-namespace}-table-row-height-large: 28px;
  --#{$var-namespace}-table-row-height: var(--#{$var-namespace}-table-row-height-small);

  font-size: 12px;

  & .#{$namespace}-table__cell {
    padding: 0;
  }
  &--striped
    .#{$namespace}-table__body
    tr.#{$namespace}-table__row--striped
    td.#{$namespace}-table__cell {
    background: #f4f8ff;
  }
  &--enable-row-hover .#{$namespace}-table__body tr:hover > td.#{$namespace}-table__cell {
    background: var(--#{$var-namespace}-table-row-hover-bg-color);
  }
  &--striped
    .#{$namespace}-table__body
    tr.#{$namespace}-table__row--striped.hover-row
    td.#{$namespace}-table__cell {
    background: var(--#{$var-namespace}-table-row-hover-bg-color);
  }

  & thead.is-group th.#{$namespace}-table__cell {
    background: var(--#{$var-namespace}-table-row-hover-bg-color);
  }

  // 可编辑表格，覆盖单元格内部可编辑表单组件样式
  // &:has(.#{$namespace}-table__edit-column) {
  // }

  .#{$namespace}-table__cell {
    > .cell {
      line-height: var(--#{$var-namespace}-table-row-height);
      .#{$namespace}-tag {
        --xp-tag-border-radius: 2px;

        position: relative;
        top: -1px;
        height: calc(var(--#{$var-namespace}-table-row-height) - 4px);
        padding: 0 4px;
      }
      .#{$namespace}-tag + .#{$namespace}-tag {
        margin-left: 4px;
      }

      .#{$namespace}-button + .#{$namespace}-dropdown {
        margin-left: 12px;
        vertical-align: middle;
      }

      .#{$namespace}-table__expand-icon {
        height: 18px;
      }
    }
  }

  &__header {
    .#{$namespace}-table__cell {
      & > .cell {
        line-height: 1em;
        text-align: justify;
      }

      // 筛选器样式
      .#{$namespace}-table__column-filter-trigger {
        height: 12px;
        margin-left: 4px;
        overflow: hidden;
        vertical-align: top;

        > i {
          display: none;
        }

        &::after {
          font-family: xui-icons, sans-serif !important;
          font-size: 12px;
          font-style: normal;
          font-weight: normal;
          content: "\e672";
        }
      }
    }
  }

  .#{$namespace}-table__header-wrapper,
  .#{$namespace}-table__body-wrapper {
    .#{$namespace}-table-column--selection > .cell {
      height: 20px;
    }
  }
  .#{$namespace}-table__header-wrapper {
    .#{$namespace}-table-column--selection {
      padding: 0 !important;
    }
  }

  // 无边框表头，增加上下内边距，以便分隔线上下有间隔，不用跟水平橫线相交
  &:not(.#{$namespace}-table__border) {
    .#{$namespace}-table__header .#{$namespace}-table__cell,
    .#{$namespace}-table__body-header .#{$namespace}-table__cell {
      height: var(--#{$var-namespace}-table-row-height);
      padding: 2px 0;
    }

    // 表头分割线
    &.#{$namespace}-table--header-divider
      thead
      th.#{$namespace}-table__cell:not(:last-of-type)
      > .cell {
      border-right: 0;

      &::after {
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 12px;
        margin-top: -6px;
        content: "";
        background: var(--#{$var-namespace}-border-color);
      }
    }
  }

  // 表格无数据时默认样式
  .#{$namespace}-table__empty-block {
    .#{$namespace}-table__empty-text {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      line-height: 2em;

      &::before {
        display: block;
        width: 80px;
        height: 80px;
        content: "";
        background: url("@/assets/table_no_data.png");
        background-size: cover;
      }
    }
  }

  // 表格中嵌套子表格
  &__expanded-cell {
    background-color: #f2f3f5;

    &:hover {
      background-color: #f2f3f5 !important;
    }

    // &.#{$namespace}-table__cell{
    //   padding: 8px 0 8px 36px;
    // }
    .#{$namespace}-table th.#{$namespace}-table__cell {
      background-color: #e5e8ed;
    }
    .#{$namespace}-table .#{$namespace}-scrollbar__bar.is-horizontal {
      box-sizing: content-box;
    }
  }
}

.#{$namespace}-table,
.#{$namespace}-table-v2 {
  // 表格下的开关样式
  .#{$namespace}-switch {
    --#{$var-namespace}-switch-height: 16px;

    vertical-align: initial;

    &.is-checked {
      .#{$namespace}-switch__inner {
        padding-right: 14px;
      }
    }

    &:not(.is-checked) {
      .#{$namespace}-switch__core {
        .#{$namespace}-switch__inner {
          padding-left: 14px;
        }
      }
    }
  }
}

.#{$namespace}-pro-table-pagination {
  padding-top: 12px;
  overflow-x: auto;

  // border-top: 1px solid var(--#{$var-namespace}-border-color-lighter);
  & > div:first-child {
    font-size: 12px !important;
  }
  .#{$namespace}-select__input {
    height: auto;
  }
}

.#{$namespace}-pagination {
  .#{$namespace}-select {
    width: 95px;
  }

  &.is-border {
    & .btn-prev,
    & .btn-next,
    & .#{$namespace}-pager li {
      min-width: 20px;
      margin: 0 4px;
    }
  }
}

/****************************************************************************
* xq-dialog 重置样式
****************************************************************************/
.#{$namespace}-dialog {
  &__title {
    font-weight: bold;
  }

  &__header,
  &__footer {
    padding: 10px 24px;
  }

  &__headerbtn {
    right: 8px;
    width: 41px;
    height: 41px;
    font-size: var(--#{$var-namespace}-dialog-title-font-size);
  }

  &__body {
    max-height: calc(70vh - 41px - 41px);
    padding: 20px 40px;
    overflow: auto;

    .#{$namespace}-form:last-child {
      margin-bottom: -4px;
    }
  }

  &.is-fullscreen {
    .#{$namespace}-dialog__body {
      height: calc(100vh - 41px - 41px);
      max-height: none;
    }
  }
}

/****************************************************************************
* xq-drawer 重置样式
****************************************************************************/
.#{$namespace}-drawer {
  &__header {
    padding: 10px 24px;
    margin-bottom: 0;
    border-bottom: 1px solid var(--#{$var-namespace}-border-color-lighter);
  }

  &__title {
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
    color: var(--#{$var-namespace}-text-color-primary);
  }

  &__close-btn {
    margin-right: -8px;
    font-size: 14px;
  }

  &__body {
    padding: 20px;
    font-size: 12px;
  }

  &__footer {
    padding: 10px 24px;
    border-top: 1px solid var(--#{$var-namespace}-border-color-lighter);
  }
}

/****************************************************************************
* xq-message-box xq-message xq-alert xq-notification 消息类组件 重置样式
****************************************************************************/
.#{$namespace}-message-box {
  &__headerbtn {
    top: 6px;
    height: 48px;
  }

  &__title {
    font-weight: bold;
  }

  &__status {
    position: absolute;
    top: 15px;
    left: 16px;
    font-size: 20px;
  }

  &:has(&__status) &__title,
  &:has(&__status) &__message {
    padding-left: 32px;
  }
}

.#{$namespace}-message {
  --#{$var-namespace}-message-bg-color: var(--#{$var-namespace}-bg-color-overlay);
  --#{$var-namespace}-message-border-color: transparent;
  --#{$var-namespace}-border-radius-base: 2px;

  box-shadow: 0 4px 5px 0 rgb(0 0 0 / 10%);

  .#{$namespace}-message__content {
    --#{$var-namespace}-message-text-color: var(--#{$var-namespace}-text-color-regular);
  }

  &--info {
    --#{$var-namespace}-message-text-color: var(--#{$var-namespace}-color-primary);
  }
}

.is-light.#{$namespace}-alert {
  .#{$namespace}-alert__title {
    font-size: 12px;
    color: var(--#{$var-namespace}-text-color-regular);

    &.with-description {
      font-size: 14px;
      font-weight: bold;
      color: #262626;
    }
  }

  .#{$namespace}-alert__description {
    font-size: 12px;
    color: var(--#{$var-namespace}-text-color-regular);
  }

  .#{$namespace}-alert__icon.is-big {
    align-self: flex-start;
  }

  &--info {
    --#{$var-namespace}-alert-bg-color: var(--#{$var-namespace}-color-primary-light-9);

    color: var(--#{$var-namespace}-color-primary);
  }

  .#{$namespace}-alert__close-btn {
    color: var(--#{$var-namespace}-text-color-secondary);
  }

  .#{$namespace}-alert__close-btn.is-customed {
    font-size: 12px;
    color: var(--#{$var-namespace}-color-primary);
  }

  .#{$namespace}-alert__icon.is-big {
    font-size: 24px;
  }
}

.#{$namespace}-notification--info.#{$namespace}-notification__icon {
  color: var(--#{$var-namespace}-color-primary);
}

.#{$namespace}-notification__content {
  margin-right: -20px;
}

/****************************************************************************
* xq-page-header 重置样式
****************************************************************************/
.#{$namespace}-page-header {
  border-bottom: 1px solid var(--#{$var-namespace}-border-color-lighter);

  &__header {
    padding: 10px 16px;
  }

  &__title {
    font-weight: bold;
  }

  &__content {
    font-size: 14px;
    font-weight: bold;
  }

  &__icon {
    margin-right: 8px;
    font-size: 14px;
  }

  &__left {
    .#{$namespace}-divider--vertical {
      margin: 0 12px;
    }
  }

  &.is-contentful .#{$namespace}-page-header__main {
    padding: 20px 20px 0;
    margin-top: 0;
  }
}

.#{$namespace}-header-title__content {
  background: none;

  &::before {
    display: inline-block;
    width: 4px;
    padding-top: 1em;
    margin-top: -2px;
    margin-right: 4px;
    line-height: inherit;
    vertical-align: middle;
    content: "";
    background: var(--#{$var-namespace}-color-primary);
  }
}

/****************************************************************************
* xq-transfer 传输器 重置样式
****************************************************************************/
.#{$namespace}-transfer-panel .#{$namespace}-checkbox__inner {
  width: 12px;
  height: 12px;
  border-radius: 4px;
}

.#{$namespace}-transfer-panel__item .#{$namespace}-checkbox__input {
  top: 9px;
}

.#{$namespace}-transfer-panel
  .#{$namespace}-transfer-panel__header
  .#{$namespace}-checkbox
  .#{$namespace}-checkbox__label {
  font-size: 12px;
}

/****************************************************************************
* xq-step 重置样式
****************************************************************************/
.#{$namespace}-steps {
  .#{$namespace}-step__head {
    &.is-process {
      --#{$var-namespace}-bg-color: var(--#{$var-namespace}-color-primary);

      color: var(--#{$var-namespace}-color-white);

      .#{$namespace}-step__icon.is-text {
        border-color: var(--#{$var-namespace}-bg-color);
      }
    }

    &.is-success {
      --#{$var-namespace}-bg-color: var(--#{$var-namespace}-color-success);

      color: var(--#{$var-namespace}-color-white);

      .#{$namespace}-step__icon.is-text {
        border-color: var(--#{$var-namespace}-bg-color);
      }
    }

    &.is-error {
      --#{$var-namespace}-bg-color: var(--#{$var-namespace}-color-error);

      color: var(--#{$var-namespace}-color-white);

      .#{$namespace}-step__icon.is-text {
        border-color: var(--#{$var-namespace}-bg-color);
      }
    }

    .#{$namespace}-step__icon.is-text {
      border-width: 1px;
      border-radius: 4px;
    }
  }

  &--horizontal {
    .#{$namespace}-step__head {
      .#{$namespace}-step__line {
        --#{$var-namespace}-text-color-placeholder: #d9d9d9;

        right: 12px;
        left: 36px;
        height: 1px;

        .#{$namespace}-step__line-inner {
          border-bottom: none;
        }
      }
    }

    .#{$namespace}-step__main {
      .#{$namespace}-step__description {
        max-width: 152px;
      }
    }
  }

  &--vertical {
    .#{$namespace}-step__head {
      .#{$namespace}-step__line {
        --#{$var-namespace}-text-color-placeholder: #d9d9d9;

        top: 36px;
        bottom: 12px;
        width: 1px;

        .#{$namespace}-step__line-inner {
          border-left: none;
        }
      }
    }
  }
}

/****************************************************************************
* xq-collapse 折叠区块 重置样式
****************************************************************************/
.#{$namespace}-collapse {
  border-top: 0;
  border-bottom: 0;

  &-item {
    &:not(:first-of-type) {
      margin-top: 16px;
    }

    &__wrap {
      border-bottom: 0;
    }

    &__header {
      flex-direction: row-reverse;
      justify-content: flex-end;
      height: 32px;
      padding-left: 8px;
      font-size: 14px;
      font-weight: 700;
      line-height: 32px;
      color: rgb(0 0 0 / 85%);
      background: linear-gradient(270deg, #f9fcff 0%, #f0f7ff 100%);
      border-bottom: 0;
      border-radius: 4px;

      .#{$namespace}-collapse-item__arrow {
        margin: 0 28px 0 2px;
        font-size: 12px;

        svg {
          display: none;
        }

        &::before {
          font-family: xui-icons, sans-serif !important;
          font-style: normal;
          content: "\e76c";
        }

        &.is-active {
          transform: none;

          &::before {
            content: "\e76a";
          }
        }

        &::after {
          position: absolute;
          left: 22px;
          width: 12.17px;
          height: 14px;
          content: "";
          background: url("@/assets/header_title_icon.png");
          background-repeat: no-repeat;
          background-position: 0 center;
          background-size: cover;
        }
      }
    }

    &__content {
      padding-top: 12px;
      padding-bottom: 0;
    }
  }
}

.#{$namespace}-collapse-form-block__title .#{$namespace}-header-title {
  margin-right: 32px;
}

/****************************************************************************
* 其它杂项 重置样式
****************************************************************************/
.#{$namespace}-result__title {
  font-weight: bold;
}

.#{$namespace}-result__subtitle {
  color: rgb(0 0 0 / 45%);

  p {
    color: inherit;
  }
}

// 按钮
.#{$namespace}-button {
  padding: 0 8px;
  &:has(.#{$namespace}-icon) {
    padding: 8px;
  }
  .#{$namespace}-icon + span {
    margin-left: 4px;
  }
}

// 文本域的数字
.#{$namespace}-textarea .#{$namespace}-input__count {
  right: 6px;
  bottom: 1px;
}

/****************************************************************************
* xq-descriptions 描述列表 重置样式
****************************************************************************/
.#{$namespace}-descriptions {
  &__body &__table {
    &.is-bordered .#{$namespace}-descriptions__cell {
      padding: 0 12px;
    }
    .#{$namespace}-descriptions__cell {
      font-size: 12px;
      line-height: 19px;
    }
  }

  &__header {
    margin-bottom: 2px;
    line-height: 20px;
  }

  &__title {
    font-size: 14px;
  }

  &__body &__table:not(.is-bordered) &__cell {
    padding-bottom: 4px;
  }

  &:has(.is-bordered) &__header {
    margin-bottom: 5px;
  }
}

/****************************************************************************
* xq-table-v2 虚拟列表 重置样式
****************************************************************************/
.#{$namespace}-table-v2 {
  font-size: 12px;
  line-height: 14px;
  color: var(--#{$var-namespace}-text-color-regular);

  &__header-cell {
    padding: 0 12px;

    &-resizer {
      flex-shrink: 0;
      height: 12px;
      margin-right: -12px;
    }

    &-text {
      margin-right: 12px;
      text-align: justify;
    }
  }

  &__row-cell {
    padding: 0 12px;
  }
}

/****************************************************************************
* xq-dropdown 下拉菜单 重置样式
****************************************************************************/
.#{$namespace}-popper.is-light {
  border: none;
  .#{$namespace}-popper__arrow {
    &::before {
      border: 1px solid #e8eaed;
    }
  }
}
.#{$namespace}-dropdown__popper {
  .#{$namespace}-dropdown-menu {
    padding: 4px 0;

    &__item {
      padding: 1px 16px;

      &--divided {
        margin: 4px 0;
      }
    }
  }
}
.#{$namespace}-select__popper {
  .#{$namespace}-select-dropdown {
    &__list {
      padding: 4px 0;
    }
  }
}

/****************************************************************************
* xq-switch 开关 重置样式
****************************************************************************/
.#{$namespace}-switch {
  padding: 0;
}

/****************************************************************************
* date-picker 日期选择器 重置样式
****************************************************************************/
.#{$namespace}-date-editor .#{$namespace}-range-input {
  text-align: left;
}
