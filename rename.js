const fs = require("fs")
const path = require("path")

function replaceAndWrite(filePath) {
  const content = fs.readFileSync(filePath, "utf8")

  let replaced = content.replace(/(?<=[\s|'|"|`|(|.|</?])(El)(?=[A-Z][a-z])/g, () => {
    return "Xq"
  })
  replaced = replaced.replace(/(?<=[\s|'|"|`|.|</?|])(el)(?=-[a-z])/g, () => {
    return "xq"
  })
  if (replaced !== content) {
    fs.writeFileSync(filePath, replaced)
    // eslint-disable-next-line no-console
    console.log(`Replaced and wrote: ${filePath}`)
  }
}

function traverse(dir) {
  if (fs.statSync(dir).isDirectory() === false && /\.vue$|\.md$|\.js$|\.tsx?$|.s?css$/.test(dir)) {
    replaceAndWrite(dir)
    return
  }
  fs.readdirSync(dir).forEach(file => {
    const filePath = path.join(dir, file)

    if (fs.statSync(filePath).isDirectory()) {
      traverse(filePath)
    } else if (/\.vue$|\.md$|\.js$|\.tsx?$|.s?css$/.test(file)) {
      replaceAndWrite(filePath)
    }
  })
}

const folder = path.resolve(__dirname, "./src")
traverse(folder)
