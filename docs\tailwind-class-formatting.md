# Tailwind CSS 长类名格式化解决方案

## 🎯 问题描述
Tailwind CSS 的类名经常很长，影响代码可读性，需要自动换行和格式化。

## ✅ 解决方案

### 1. **手动换行方案**（推荐用于特别长的类名）

```vue
<!-- 原始长类名 -->
<div class="flex h-[20px] cursor-pointer flex-row items-center rounded-[4px] border-[1px] border-solid bg-[var(--bg-color)] px-[12px] text-[12px] leading-[20px] not-last:mr-2 hover:bg-[var(--hover-bg-color)]">

<!-- 手动格式化后 -->
<div class="flex h-[20px] cursor-pointer flex-row items-center
           rounded-[4px] border-[1px] border-solid 
           bg-[var(--bg-color)] px-[12px] text-[12px] 
           leading-[20px] not-last:mr-2 
           hover:bg-[var(--hover-bg-color)]">
```

### 2. **使用组合类名**

在 `tailwind.config.js` 中定义常用组合：

```javascript
module.exports = {
  theme: {
    extend: {
      // 定义组合类名
    }
  },
  plugins: [
    // 自定义组件类
    function({ addComponents }) {
      addComponents({
        '.btn-primary': {
          @apply: 'flex h-[20px] cursor-pointer flex-row items-center rounded-[4px] border-[1px] border-solid px-[12px] text-[12px] leading-[20px]'
        }
      })
    }
  ]
}
```

### 3. **VS Code 代码片段**

使用 `twml` 代码片段快速创建多行类名：

```vue
<!-- 输入 twml 然后 Tab -->
<div class="flex h-[20px] cursor-pointer
           rounded-[4px] border-[1px] 
           hover:bg-primary">
```

### 4. **CSS-in-JS 风格**

对于特别复杂的样式，考虑使用 computed 属性：

```vue
<script setup>
const buttonClasses = computed(() => [
  'flex h-[20px] cursor-pointer flex-row items-center',
  'rounded-[4px] border-[1px] border-solid',
  'bg-[var(--bg-color)] px-[12px] text-[12px]',
  'leading-[20px] not-last:mr-2',
  'hover:bg-[var(--hover-bg-color)]'
].join(' '))
</script>

<template>
  <div :class="buttonClasses">
    <!-- 内容 -->
  </div>
</template>
```

## ⚙️ 配置说明

### Prettier 配置
- `printWidth: 80` - 合理的行宽
- `singleAttributePerLine: false` - 不强制每个属性一行
- `htmlWhitespaceSensitivity: "ignore"` - 忽略HTML空格敏感性

### VS Code 设置
- 启用了 `editor.wordWrap: "on"` 用于长行自动换行
- 添加了 `editor.rulers: [80, 120]` 显示行宽指示器
- 配置了 Tailwind CSS 智能提示

## 🔧 使用建议

1. **短类名**（< 80字符）：保持单行
2. **中等类名**（80-120字符）：考虑手动换行
3. **长类名**（> 120字符）：使用组合方案或 computed 属性

## 📝 最佳实践

1. **逻辑分组**：将相关的类名放在同一行
   ```vue
   <!-- 好的分组 -->
   class="flex items-center justify-center    <!-- 布局 -->
          w-full h-[40px] p-4               <!-- 尺寸 -->
          bg-blue-500 text-white            <!-- 颜色 -->
          rounded-lg shadow-md              <!-- 外观 -->
          hover:bg-blue-600 focus:ring-2"   <!-- 交互 -->
   ```

2. **使用注释**：为复杂的类名添加注释
   ```vue
   <!-- 复杂按钮样式 -->
   <button class="/* 基础布局 */ flex items-center justify-center
                  /* 尺寸样式 */ w-full h-[40px] px-4 py-2
                  /* 外观样式 */ bg-primary text-white rounded-lg
                  /* 交互效果 */ hover:bg-primary-dark focus:ring-2">
   ```

3. **提取重复样式**：将常用的类名组合提取为组件或工具类
