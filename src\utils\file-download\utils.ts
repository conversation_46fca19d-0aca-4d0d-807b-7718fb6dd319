/**
 * 文件下载工具类
 */

import type { DownloadProgress } from "./types"

/**
 * 检查浏览器是否支持 File System Access API
 *
 * 支持的浏览器版本：
 * - Chrome ≥ 86 (2020年10月)
 * - Edge ≥ 86 (2020年10月)
 * - Opera ≥ 72 (2020年11月)
 * - 其他基于 Chromium ≥ 86 的浏览器
 *
 * 不支持的浏览器：
 * - Firefox (截至2025年6月仍不支持)
 * - Safari (截至2025年6月仍不支持)
 * - Internet Explorer (所有版本)
 * - 老版本的 Chrome/Edge < 86
 *
 * @returns {boolean} 是否支持 File System Access API
 */
export function isFileSystemAccessSupported(): boolean {
  return "showSaveFilePicker" in window
}

/**
 * 从 Content-Disposition 头部提取文件名
 */
export function extractFilenameFromHeader(
  contentDisposition: string
): string | null {
  if (!contentDisposition) return null

  // 匹配 filename*=UTF-8''encoded-filename 格式
  const utf8Match = contentDisposition.match(/filename\*=UTF-8''(.+)/i)
  if (utf8Match) {
    try {
      return decodeURIComponent(utf8Match[1])
    } catch {
      // 解码失败，继续尝试其他格式
    }
  }

  // 匹配 filename="filename" 或 filename=filename 格式
  const regularMatch = contentDisposition.match(
    /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/i
  )
  if (regularMatch && regularMatch[1]) {
    return regularMatch[1].replace(/['"]/g, "")
  }

  return null
}

/**
 * 从 URL 提取文件名
 */
export function extractFilenameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const filename = pathname.split("/").pop() || "download"

    // 如果没有扩展名，添加默认扩展名
    if (!filename.includes(".")) {
      return `${filename}.bin`
    }

    return filename
  } catch {
    return `download_${Date.now()}.bin`
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B"

  const k = 1024
  const sizes = ["B", "KB", "MB", "GB", "TB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 格式化下载速度
 */
export function formatSpeed(bytesPerSecond: number): string {
  return `${formatFileSize(bytesPerSecond)}/s`
}

/**
 * 格式化剩余时间
 */
export function formatRemainingTime(seconds: number): string {
  if (!seconds || !isFinite(seconds)) return "未知"

  if (seconds < 60) {
    return `${Math.round(seconds)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

/**
 * 计算下载进度
 */
export function calculateProgress(
  downloaded: number,
  total?: number,
  startTime?: number
): DownloadProgress {
  const percentage = total ? (downloaded / total) * 100 : 0
  const now = Date.now()
  const elapsedTime = startTime ? (now - startTime) / 1000 : 0
  const speed = elapsedTime > 0 ? downloaded / elapsedTime : 0
  const remainingTime =
    total && speed > 0 ? (total - downloaded) / speed : undefined

  return {
    downloaded,
    total,
    percentage: Math.min(percentage, 100),
    speed,
    remainingTime,
  }
}

/**
 * 获取响应头中的文件信息
 */
export async function getFileInfoFromResponse(
  url: string,
  headers?: Record<string, string>
) {
  try {
    const response = await fetch(url, {
      method: "HEAD",
      headers: headers || {},
    })

    const contentLength = response.headers.get("Content-Length")
    const contentDisposition = response.headers.get("Content-Disposition")
    const contentType = response.headers.get("Content-Type")

    return {
      size: contentLength ? parseInt(contentLength, 10) : undefined,
      filename: extractFilenameFromHeader(contentDisposition || ""),
      contentType: contentType || "application/octet-stream",
      supportsRange: response.headers.get("Accept-Ranges") === "bytes",
    }
  } catch (error) {
    console.warn("获取文件信息失败:", error)
    return {
      size: undefined,
      filename: null,
      contentType: "application/octet-stream",
      supportsRange: false,
    }
  }
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 检查是否为有效的URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 安全的文件名（移除不安全字符）
 */
export function sanitizeFilename(filename: string): string {
  // 移除或替换不安全的字符
  return filename
    .replace(/[<>:"/\\|?*]/g, "_")
    .replace(/\s+/g, "_")
    .replace(/_{2,}/g, "_")
    .trim()
}

/**
 * 获取详细的浏览器兼容性信息
 *
 * @returns {object} 浏览器信息和功能支持情况
 */
export function getBrowserCompatibilityInfo() {
  const userAgent = navigator.userAgent
  const info = {
    browser: "Unknown",
    version: "Unknown",
    supportsFileSystemAPI: isFileSystemAccessSupported(),
    supportsWebWorker: typeof Worker !== "undefined",
    supportsStreams: typeof ReadableStream !== "undefined",
    recommendedDownloadMethod: "fallback" as "modern" | "fallback",
  }

  // 检测浏览器类型和版本
  if (userAgent.includes("Chrome/")) {
    const chromeMatch = userAgent.match(/Chrome\/(\d+)/)
    if (chromeMatch) {
      info.browser = "Chrome"
      info.version = chromeMatch[1]
      info.recommendedDownloadMethod =
        parseInt(chromeMatch[1]) >= 86 ? "modern" : "fallback"
    }
  } else if (userAgent.includes("Edg/")) {
    const edgeMatch = userAgent.match(/Edg\/(\d+)/)
    if (edgeMatch) {
      info.browser = "Edge"
      info.version = edgeMatch[1]
      info.recommendedDownloadMethod =
        parseInt(edgeMatch[1]) >= 86 ? "modern" : "fallback"
    }
  } else if (userAgent.includes("Firefox/")) {
    const firefoxMatch = userAgent.match(/Firefox\/(\d+)/)
    if (firefoxMatch) {
      info.browser = "Firefox"
      info.version = firefoxMatch[1]
      info.recommendedDownloadMethod = "fallback" // Firefox 不支持 File System Access API
    }
  } else if (userAgent.includes("Safari/") && !userAgent.includes("Chrome")) {
    const safariMatch = userAgent.match(/Version\/(\d+)/)
    if (safariMatch) {
      info.browser = "Safari"
      info.version = safariMatch[1]
      info.recommendedDownloadMethod = "fallback" // Safari 不支持 File System Access API
    }
  }

  return info
}

/**
 * 获取下载功能的兼容性报告
 *
 * @returns {object} 功能支持详情
 */
export function getDownloadCapabilities() {
  const browserInfo = getBrowserCompatibilityInfo()

  return {
    // 基础信息
    browserName: browserInfo.browser,
    browserVersion: browserInfo.version,

    // 功能支持
    canUseModernDownload: browserInfo.supportsFileSystemAPI,
    canUseWebWorker: browserInfo.supportsWebWorker,
    canUseStreaming: browserInfo.supportsStreams,

    // 推荐方式
    recommendedMethod: browserInfo.recommendedDownloadMethod,

    // 功能限制
    limitations: (() => {
      const limits: string[] = []

      if (!browserInfo.supportsFileSystemAPI) {
        limits.push("无法让用户选择保存位置")
        limits.push("无法覆盖已存在的文件")
      }

      if (!browserInfo.supportsWebWorker) {
        limits.push("大文件下载可能阻塞主线程")
      }

      if (!browserInfo.supportsStreams) {
        limits.push("大文件可能占用过多内存")
      }

      if (browserInfo.browser === "Safari") {
        limits.push("下载大文件时可能出现兼容性问题")
      }

      return limits
    })(),

    // 建议
    recommendations: (() => {
      const recommendations: string[] = []

      if (
        browserInfo.browser === "Firefox" ||
        browserInfo.browser === "Safari"
      ) {
        recommendations.push("建议使用 Chrome 或 Edge 获得最佳下载体验")
      }

      if (
        browserInfo.browser === "Chrome" &&
        parseInt(browserInfo.version) < 86
      ) {
        recommendations.push("建议升级到 Chrome 86+ 以获得现代下载功能")
      }

      if (
        browserInfo.browser === "Edge" &&
        parseInt(browserInfo.version) < 86
      ) {
        recommendations.push("建议升级到 Edge 86+ 以获得现代下载功能")
      }

      return recommendations
    })(),
  }
}
