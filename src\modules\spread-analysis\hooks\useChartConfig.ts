import {
  CustomDynamicXAxisEnum,
  CustomStaticXAxisEnum,
  SpreadPageTypeEnum,
} from "../consts"
// import { useStaticViewStore } from "../stores/staticViewStore"
import { StaticChartFormModel } from "../types"

interface QueryFormReturn {
  formModels: StaticChartFormModel
}

export function useChartConfig(
  page: SpreadPageTypeEnum = SpreadPageTypeEnum.STATIC
): QueryFormReturn {
  // const store = useStaticViewStore()

  const formModels = ref<StaticChartFormModel>({
    xAxis:
      page === SpreadPageTypeEnum.STATIC
        ? CustomStaticXAxisEnum.X_AXIS_1
        : CustomDynamicXAxisEnum.X_AXIS_1, // 默认横轴为内部行业
  })

  return {
    formModels,
  }
}
