import Axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CustomParamsSerializer,
} from "axios"
import jsFileDownload from "js-file-download"
import Qs, { stringify } from "qs"

import { BASE_API, POWERED_BY_QIANKUN } from "@/business/dictionary"
import { formatToken, getToken, getTokenSource } from "@/utils/auth"
// import { useUserStore } from "@/store/modules/user";
import { XqMessage } from "@xquant/x-ui-plus"

import NProgress from "../progress"
import {
  PureHttpError,
  PureHttpRequestConfig,
  PureHttpResponse,
  RequestMethods,
} from "./types.d"

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  baseURL: POWERED_BY_QIANKUN ? BASE_API : BASE_API, // 始终使用BASE_API以触发Vite代理
  // 请求超时时间
  timeout: 10000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest",
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer,
  },
}

class PureHttp {
  constructor() {
    this.httpInterceptorsRequest()
    this.httpInterceptorsResponse()
  }

  /** token过期后，暂存待执行的请求 */
  private static requests = []

  /** 防止重复刷新token */
  private static isRefreshing = false

  /** 初始化配置对象 */
  private static initConfig: PureHttpRequestConfig = {}

  /** 保存当前Axios实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig)

  /** 重连原始请求 */
  // private static retryOriginalRequest(config: PureHttpRequestConfig) {
  //   return new Promise(resolve => {
  //     PureHttp.requests.push((token: string) => {
  //       config.headers["Cube-Authorization"] = formatToken(token);
  //       resolve(config);
  //     });
  //   });
  // }

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        // 开启进度条动画
        NProgress.start()
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config)
          return config
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config)
          return config
        }
        /** 请求白名单，放置一些不需要token的接口（通过设置请求白名单，防止token过期后再请求造成的死循环问题） */
        const whiteList = ["/refreshToken", "/login"]
        return whiteList.some(v => config.url && config.url.indexOf(v) > -1)
          ? config
          : new Promise(resolve => {
              const data = getToken()
              const tokenSource = getTokenSource()

              if (window.__POWERED_BY_QIANKUN__) {
                config.headers &&
                  (config.headers["Authorization-Source"] = tokenSource)
              }

              if (data) {
                config.headers &&
                  (config.headers["Cube-Authorization"] = formatToken(data))
                resolve(config)
                // 这里不做token刷新的事
                // const now = new Date().getTime();
                // const expired = parseInt(data.expires) - now <= 0;
                // if (expired) {
                //   if (!PureHttp.isRefreshing) {
                //     PureHttp.isRefreshing = true;                //     // token过期刷新
                //     const userStore = useUserStore()
                //     userStore
                //       .handleRefreshToken({ refreshToken: data.refreshToken })
                //       .then(res => {
                //         const token = res.data.accessToken;
                //         config.headers["Cube-Authorization"] = formatToken(token);
                //         PureHttp.requests.forEach(cb => cb(token));
                //         PureHttp.requests = [];
                //       })
                //       .finally(() => {
                //         PureHttp.isRefreshing = false;
                //       });
                //   }
                //   resolve(PureHttp.retryOriginalRequest(config));
                // } else {
                //   config.headers["Cube-Authorization"] = formatToken(
                //     data.accessToken
                //   );
                //   resolve(config);
                // }
              } else {
                resolve(config)
              }
            })
      },
      error => {
        return Promise.reject(error)
      }
    )
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    const instance = PureHttp.axiosInstance
    instance.interceptors.response.use(
      (response: PureHttpResponse) => {
        // 关闭进度条动画
        NProgress.done()
        const $config = response.config

        // 这里各项目可以根据自己的需求进行处理，对错误统一拦截
        // const res = response.data
        // if (![10200].includes(res.code)) {
        //   switch (res.code) {
        //     case 10400:
        //       throw new Error("no login")
        //     case 10403:
        //       throw new Error("no login")
        //     case 10402:
        //       throw new Error("no login")
        //     case 1999:
        //       break
        //     default:
        //       break
        //   }
        //   return Promise.reject(res)
        // }

        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response)
          return response.data
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response)
          return response.data
        }
        return response.data
      },
      (error: PureHttpError) => {
        const $error = error
        $error.isCancelRequest = Axios.isCancel($error)
        // 关闭进度条动画
        NProgress.done()
        // 所有的响应异常 区分来源为取消请求/非取消请求
        return Promise.reject($error)
      }
    )
  }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<AxiosResponse<T> | T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig,
    } as PureHttpRequestConfig

    // 单独处理自定义请求/响应回调
    return new Promise<AxiosResponse<T> | T>((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then(response => {
          resolve(response)
        })
        .catch(error => {
          // 添加错误提示
          let cubeResponseMsg = error.response?.headers["cube-response-msg"]
          if (cubeResponseMsg)
            cubeResponseMsg = decodeURIComponent(cubeResponseMsg)
          XqMessage({
            message: cubeResponseMsg || error.message || "系统错误！",
            type: "error",
          })
          reject(error)
        })
    })
  }

  /** 单独抽离的post工具函数 */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: PureHttpRequestConfig
  ): Promise<AxiosResponse<P> | P> {
    return this.request<P>("post", url, params, config)
  }

  /** 单独抽离的get工具函数 */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: PureHttpRequestConfig
  ): Promise<AxiosResponse<P> | P> {
    return this.request<P>("get", url, params, config)
  }

  // download 通过get请求
  public downloadByGet = (url: string) => {
    /**
     * 要传递的对象, 下载的文件名
     */
    return (params = {}, name = "下载文件") => {
      // 下载不过拦截器
      return this.request("get", url, params, {
        paramsSerializer: x =>
          Qs.stringify(x, {
            arrayFormat: "repeat",
          }),
        responseType: "blob",
        headers: {
          filename: encodeURI(name),
        },
      }).then((res: any) => {
        if (res.headers["content-disposition"]) {
          // 'attachment;filename=xxx.zip' 固定格式
          name = decodeURIComponent(
            res.headers["content-disposition"].substring(20)
          )
        } else {
          const _index = name.lastIndexOf(".")
          const filename = name.substring(0, _index)
          const suffix = res.headers.filenameext || name.substr(_index + 1)
          name = `${filename}.${suffix}`
        }
        jsFileDownload(res.data, name)
      })
    }
  }
}

export const http = new PureHttp()
