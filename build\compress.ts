import type { Plugin } from "vite"
import { compression } from "vite-plugin-compression2"

export const configCompressPlugin = (compress: ViteCompression): Plugin | Plugin[] | null => {
  if (compress === "none") return null

  // 检查是否需要删除原始文件
  const deleteOriginalAssets = compress.includes("clear")
  
  // 基础配置
  const baseConfig = {
    threshold: 0, // 所有文件都进行压缩
    deleteOriginalAssets,
    skipIfLargerOrEqual: true, // 如果压缩后文件更大则跳过
  }

  const plugins: Plugin[] = []

  // 根据配置类型选择算法
  if (compress.includes("gzip") || compress.includes("both")) {
    // 添加 gzip 压缩
    plugins.push(
      compression({
        ...baseConfig,
        algorithm: "gzip",
        compressionOptions: { level: 9 }, // 最高压缩级别
      })
    )
  }

  if (compress.includes("brotli") || compress.includes("both")) {
    // 添加 brotli 压缩
    plugins.push(
      compression({
        ...baseConfig,
        algorithm: "brotliCompress",
        compressionOptions: {
          params: {
            [require("zlib").constants.BROTLI_PARAM_QUALITY]: 11, // 最高质量
          },
        },
      })
    )
  }

  return plugins.length === 1 ? plugins[0] : plugins.length > 1 ? plugins : null
}
