<script setup lang="ts">
// 异步导入组件
const AsyncComplianceQueryForm = defineAsyncComponent(
  () => import("../components/compliance/query-form.vue")
)
const AsyncComplianceTable = defineAsyncComponent(
  () => import("../components/compliance/table.vue")
)

defineOptions({
  name: "RiskIndicatorsCompliance",
})

/** 作为 `<suspend />` 加载，不用关闭 */
const isAsyncLoading = ref(true)
</script>

<template>
  <xq-card
    class="h-full !overflow-y-scroll border-none"
    body-class="!p-[12px] h-full"
    shadow="never"
  >
    <div class="flex h-full flex-col">
      <module-title title="合规类指标" />

      <!-- 查询表单 -->
      <div class="mt-[8px] bg-[var(--xq-color-info-light-9)] pt-[4px]">
        <suspense>
          <AsyncComplianceQueryForm />
          <template #fallback>
            <portal-target name="module-loading" :slot-props="{ height: 48 }" />
          </template>
        </suspense>
      </div>

      <xq-divider />

      <module-panel title="筛选结果" class="flex flex-1 flex-col">
        <!-- 数据表格 -->
        <div class="flex flex-1 items-center overflow-hidden">
          <suspense>
            <AsyncComplianceTable />
            <template #fallback>
              <portal-target name="module-loading" />
            </template>
          </suspense>
        </div>
      </module-panel>
    </div>
  </xq-card>

  <portal v-slot="{ height }" to="module-loading">
    <module-loading v-model="isAsyncLoading" :height="height" />
  </portal>
</template>
