<script setup lang="ts">
import {
  customDataTagOptions,
  customDynamicXAxisOptions,
  customDynamicYAxisOptions,
  customStaticXAxisOptions,
  customStaticYAxisOptions,
  SpreadPageTypeEnum,
} from "../consts"
import { useChartConfig } from "../hooks/useChartConfig"
import { SpreadPageType } from "../types"

defineOptions({
  name: "SpreadAnalysisChartConfig",
})

const { page = SpreadPageTypeEnum.STATIC } = defineProps<{
  /** 页面类型，“静态” 或 “动态” 视图 */
  page?: SpreadPageType
}>()

const { formModels } = useChartConfig()

/** 横轴配置 */
const xAxisOptions = computed(() => {
  return page === SpreadPageTypeEnum.STATIC ? customStaticXAxisOptions : customDynamicXAxisOptions
})

/** 纵轴配置 */
const yAxisOptions = computed(() => {
  return page === SpreadPageTypeEnum.STATIC ? customStaticYAxisOptions : customDynamicYAxisOptions
})
</script>

<template>
  <xq-form :model="formModels" label-width="100" class="chart-config h-full w-full">
    <xq-form-item label="自定义横轴">
      <xq-select v-model="formModels.xAxis" clearable>
        <template v-for="option in xAxisOptions" :key="option.value">
          <xq-option :value="option.value" :label="option.label" />
        </template>
      </xq-select>
    </xq-form-item>

    <!-- /* --------------------------------- 纵轴（静态） --------------------------------- */ -->
    <template v-if="page === SpreadPageTypeEnum.STATIC">
      <fieldset class="ml-none !p-0 py-[4px]">
        <legend class="mb-[8px] w-[80px] text-[12px]">
          <span class="inline-block w-full pr-[4px] text-right font-bold">自定义纵轴</span>
        </legend>
        <xq-form-item label="主坐标轴">
          <xq-select v-model="formModels.yAxis" clearable>
            <template v-for="option in yAxisOptions" :key="option.value">
              <xq-option :value="option.value" :label="option.label" />
            </template>
          </xq-select>
        </xq-form-item>
        <xq-form-item label="次坐标轴">
          <xq-select v-model="formModels.yAxisSecondary" clearable>
            <template v-for="option in yAxisOptions" :key="option.value">
              <xq-option :value="option.value" :label="option.label" />
            </template>
          </xq-select>
        </xq-form-item>
      </fieldset>
    </template>

    <!-- /* --------------------------------- 纵轴（动态） --------------------------------- */ -->
    <template v-else>
      <xq-form-item label="自定义纵轴">
        <xq-select v-model="formModels.yAxis" clearable>
          <template v-for="option in yAxisOptions" :key="option.value">
            <xq-option :value="option.value" :label="option.label" />
          </template>
        </xq-select>
      </xq-form-item>
    </template>

    <template v-if="page === SpreadPageTypeEnum.DYNAMIC">
      <xq-form-item label="自定义数据标签">
        <xq-select v-model="formModels.xAxis" clearable>
          <template v-for="option in customDataTagOptions" :key="option.value">
            <xq-option :value="option.value" :label="option.label" />
          </template>
        </xq-select>
      </xq-form-item>
    </template>
  </xq-form>
</template>

<style scoped lang="scss">
.chart-config {
  :deep(.#{$ns}-form-item) {
    margin-bottom: 12px;
  }
}
</style>
