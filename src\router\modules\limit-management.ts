import Layout from "@/layout/LayoutProxy"

export default {
  path: "/limit-management",
  name: "LimitManagement",
  component: Layout,
  redirect: "/limit-management/index",
  meta: {
    icon: "homeFilled",
    title: "极限管理",
  },
  children: [
    {
      path: "/limit-management/index",
      name: "LimitManagementIndex",
      component: () => import("@/modules/limit-management/views/index.vue"),
      meta: {
        title: "极限管理",
      },
    },
  ],
} as RouteConfigsTable
