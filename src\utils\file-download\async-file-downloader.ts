/**
 * 异步文件生成下载器
 * 专门处理后端需要时间生成文件的下载场景
 *
 * 使用场景：
 * 1. 后端从云服务获取数据
 * 2. 后端需要处理、压缩数据
 * 3. 大数据量导出需要时间
 */

import {
  computed,
  onUnmounted,
  readonly,
  ref,
} from "vue"

import { downloadFile } from "./index"
import type {
  DownloadOptions,
  DownloadProgress,
  DownloadResult,
} from "./types"

/**
 * 异步文件生成状态
 */
export interface AsyncFileStatus {
  status: "pending" | "processing" | "ready" | "failed"
  progress?: number // 生成进度 0-100
  message?: string // 状态描述
  estimatedTime?: number // 预计剩余时间（秒）
  downloadUrl?: string // 文件生成完成后的下载链接
  error?: string // 错误信息
}

/**
 * 异步下载选项
 */
export interface AsyncDownloadOptions
  extends Omit<DownloadOptions, "onProgress"> {
  // 状态轮询相关
  pollInterval?: number // 轮询间隔（毫秒，默认2000）
  maxWaitTime?: number // 最大等待时间（毫秒，默认5分钟）

  // 回调函数
  onStatusChange?: (status: AsyncFileStatus) => void // 状态变化回调
  onProgress?: (progress: DownloadProgress) => void // 下载进度回调（文件生成完成后）
  onGenerationProgress?: (progress: number) => void // 文件生成进度回调

  // 请求相关
  statusUrl?: string // 状态查询接口（如果不同于请求接口）
  headers?: Record<string, string> // 请求头
}

/**
 * 异步下载结果
 */
export interface AsyncDownloadResult extends DownloadResult {
  generationTime?: number // 文件生成耗时（毫秒）
  waitTime?: number // 总等待时间（毫秒）
}

/**
 * 异步文件下载器类
 */
export class AsyncFileDownloader {
  private abortController?: AbortController
  private pollTimer?: NodeJS.Timeout
  private startTime: number = 0
  private generationStartTime: number = 0

  /**
   * 开始异步下载
   *
   * @param requestUrl 发起文件生成请求的接口
   * @param filename 文件名
   * @param options 下载选项
   */
  async download(
    requestUrl: string,
    filename?: string,
    options: AsyncDownloadOptions = {}
  ): Promise<AsyncDownloadResult> {
    const {
      pollInterval = 2000,
      maxWaitTime = 5 * 60 * 1000, // 5分钟
      onStatusChange,
      onProgress,
      onGenerationProgress,
      statusUrl,
      headers = {},
      ...downloadOptions
    } = options

    this.startTime = Date.now()
    this.abortController = new AbortController()

    try {
      // 1. 发起文件生成请求
      onStatusChange?.({
        status: "pending",
        message: "正在向服务器发起文件生成请求...",
        progress: 0,
      })

      const response = await fetch(requestUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...headers,
        },
        signal: this.abortController.signal,
      })

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`)
      }

      const requestResult = await response.json()

      // 检查响应格式
      if (!requestResult.taskId && !requestResult.downloadUrl) {
        throw new Error("服务器响应格式错误，缺少任务ID或下载链接")
      }

      // 2. 如果已经有下载链接，直接下载
      if (requestResult.downloadUrl) {
        onStatusChange?.({
          status: "ready",
          message: "文件已准备就绪，开始下载...",
          progress: 100,
          downloadUrl: requestResult.downloadUrl,
        })

        return await this.performDownload(
          requestResult.downloadUrl,
          filename,
          downloadOptions,
          onProgress
        )
      }

      // 3. 开始轮询状态
      this.generationStartTime = Date.now()
      const checkUrl =
        statusUrl || `${requestUrl}/status/${requestResult.taskId}`

      onStatusChange?.({
        status: "processing",
        message: "后端正在处理文件，请稍候...",
        progress: 0,
      })

      const downloadUrl = await this.pollStatus(
        checkUrl,
        pollInterval,
        maxWaitTime,
        headers,
        onStatusChange,
        onGenerationProgress
      )

      // 4. 开始实际下载
      onStatusChange?.({
        status: "ready",
        message: "文件生成完成，开始下载...",
        progress: 100,
        downloadUrl,
      })

      const generationTime = Date.now() - this.generationStartTime
      const result = await this.performDownload(
        downloadUrl,
        filename,
        downloadOptions,
        onProgress
      )

      return {
        ...result,
        generationTime,
        waitTime: Date.now() - this.startTime,
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "下载失败"

      onStatusChange?.({
        status: "failed",
        message: errorMessage,
        error: errorMessage,
      })

      return {
        success: false,
        error: errorMessage,
        waitTime: Date.now() - this.startTime,
      }
    } finally {
      this.cleanup()
    }
  }

  /**
   * 轮询文件生成状态
   */
  private async pollStatus(
    statusUrl: string,
    pollInterval: number,
    maxWaitTime: number,
    headers: Record<string, string>,
    onStatusChange?: (status: AsyncFileStatus) => void,
    onGenerationProgress?: (progress: number) => void
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const checkStatus = async () => {
        try {
          if (this.abortController?.signal.aborted) {
            reject(new Error("下载已取消"))
            return
          }

          // 检查是否超时
          if (Date.now() - this.generationStartTime > maxWaitTime) {
            reject(new Error("文件生成超时，请稍后重试"))
            return
          }

          const response = await fetch(statusUrl, {
            headers,
            signal: this.abortController?.signal,
          })

          if (!response.ok) {
            throw new Error(`状态查询失败: ${response.status}`)
          }

          const status: AsyncFileStatus = await response.json()

          // 触发状态更新回调
          onStatusChange?.(status)

          // 触发生成进度回调
          if (status.progress !== undefined) {
            onGenerationProgress?.(status.progress)
          }

          switch (status.status) {
            case "ready":
              if (status.downloadUrl) {
                resolve(status.downloadUrl)
              } else {
                reject(new Error("文件生成完成但缺少下载链接"))
              }
              break

            case "failed":
              reject(new Error(status.error || "文件生成失败"))
              break

            case "processing":
            case "pending":
              // 继续轮询
              this.pollTimer = setTimeout(checkStatus, pollInterval)
              break

            default:
              reject(new Error(`未知状态: ${status.status}`))
          }
        } catch (error) {
          reject(error)
        }
      }

      // 开始第一次检查
      checkStatus()
    })
  }

  /**
   * 执行实际文件下载
   */
  private async performDownload(
    downloadUrl: string,
    filename?: string,
    options: DownloadOptions = {},
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<DownloadResult> {
    return await downloadFile(downloadUrl, filename, {
      ...options,
      onProgress,
    })
  }

  /**
   * 取消下载
   */
  cancel(): void {
    this.abortController?.abort()
    this.cleanup()
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.pollTimer) {
      clearTimeout(this.pollTimer)
      this.pollTimer = undefined
    }
  }
}

/**
 * 便捷的异步下载函数
 *
 * @example
 * ```typescript
 * // 基础使用
 * const result = await downloadAsyncFile('/api/export/data', 'export.zip')
 *
 * // 带状态监听
 * const result = await downloadAsyncFile('/api/export/data', 'export.zip', {
 *   onStatusChange: (status) => {
 *     console.log('状态:', status.message)
 *   },
 *   onGenerationProgress: (progress) => {
 *     console.log('生成进度:', progress + '%')
 *   }
 * })
 * ```
 */
export async function downloadAsyncFile(
  requestUrl: string,
  filename?: string,
  options: AsyncDownloadOptions = {}
): Promise<AsyncDownloadResult> {
  const downloader = new AsyncFileDownloader()
  return await downloader.download(requestUrl, filename, options)
}

/**
 * 创建可重用的异步下载器实例
 *
 * @example
 * ```typescript
 * const downloader = createAsyncDownloader()
 *
 * // 开始下载
 * const downloadPromise = downloader.download('/api/export', 'data.zip')
 *
 * // 可以随时取消
 * setTimeout(() => {
 *   downloader.cancel()
 * }, 10000)
 * ```
 */
export function createAsyncDownloader(): AsyncFileDownloader {
  return new AsyncFileDownloader()
}

/**
 * Vue 组合式函数：异步文件下载
 */
export function useAsyncFileDownload() {
  const downloader = new AsyncFileDownloader()
  const isDownloading = ref(false)
  const downloadStatus = ref<AsyncFileStatus | null>(null)
  const downloadProgress = ref<DownloadProgress | null>(null)
  const generationProgress = ref(0)

  const download = async (
    requestUrl: string,
    filename?: string,
    options: Omit<
      AsyncDownloadOptions,
      "onStatusChange" | "onProgress" | "onGenerationProgress"
    > = {}
  ) => {
    if (isDownloading.value) {
      throw new Error("已有下载任务正在进行")
    }

    isDownloading.value = true
    downloadStatus.value = null
    downloadProgress.value = null
    generationProgress.value = 0

    try {
      const result = await downloader.download(requestUrl, filename, {
        ...options,
        onStatusChange: status => {
          downloadStatus.value = status
        },
        onProgress: progress => {
          downloadProgress.value = progress
        },
        onGenerationProgress: progress => {
          generationProgress.value = progress
        },
      })

      return result
    } finally {
      isDownloading.value = false
    }
  }

  const cancel = () => {
    downloader.cancel()
    isDownloading.value = false
  }

  // 清理函数
  onUnmounted(() => {
    cancel()
  })

  // 格式化函数
  const formatGenerationProgress = computed(() => {
    return `${generationProgress.value.toFixed(1)}%`
  })

  const formatWaitTime = computed(() => {
    if (!downloadStatus.value?.estimatedTime) return ""

    const minutes = Math.floor(downloadStatus.value.estimatedTime / 60)
    const seconds = downloadStatus.value.estimatedTime % 60

    if (minutes > 0) {
      return `预计还需 ${minutes}分${seconds}秒`
    } else {
      return `预计还需 ${seconds}秒`
    }
  })

  return {
    // 状态
    isDownloading: readonly(isDownloading),
    downloadStatus: readonly(downloadStatus),
    downloadProgress: readonly(downloadProgress),
    generationProgress: readonly(generationProgress),

    // 方法
    download,
    cancel,

    // 格式化
    formatGenerationProgress,
    formatWaitTime,
  }
}
