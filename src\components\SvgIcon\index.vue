<template>
  <svg aria-hidden="true" :style="{ width: size + 'px', height: size + 'px' }">
    <use :href="symbolId" :fill="color" />
  </svg>
</template>
<script>
export default defineComponent({
  name: "SvgIcon",
  props: {
    prefix: {
      type: String,
      default: "icon",
    },
    name: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      default: "#595959",
    },
    size: {
      type: Number,
      default: 20,
    },
  },
  setup(props) {
    const symbolId = computed(() => `#${props.prefix}-${props.name}`)
    return { symbolId }
  },
})
</script>
