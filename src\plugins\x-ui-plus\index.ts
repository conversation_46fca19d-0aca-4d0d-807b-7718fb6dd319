import {
  App,
  Component,
} from "vue"

import {
  XqAffix,
  XqAlert,
  XqAside,
  XqAutocomplete,
  XqAvatar,
  XqBacktop,
  XqBadge,
  XqBreadcrumb,
  XqBreadcrumbItem,
  XqButton,
  XqButtonGroup,
  XqCard,
  XqCheckbox,
  XqCheckboxGroup,
  XqCol,
  XqCollapse,
  XqCollapseItem,
  XqColorPicker,
  XqDatePicker,
  XqDescriptions,
  XqDescriptionsItem,
  XqDialog,
  XqDivider,
  XqDrawer,
  XqDropdown,
  XqDropdownItem,
  XqDropdownMenu,
  XqEditTableColumn,
  XqEmpty,
  XqFieldSelectTree,
  XqFloatButton,
  XqFloatButtonItem,
  XqForm,
  XqFormItem,
  XqFormLayoutItem,
  XqFormQuery,
  XqIcon,
  XqImage,
  XqInfiniteScroll,
  XqInput,
  XqInputNumber,
  XqLink,
  XqLoading,
  XqMenu,
  XqMenuItem,
  XqOption,
  XqPageHeader,
  XqPagination,
  XqPopconfirm,
  XqPopover,
  XqPopper,
  XqProTable,
  XqRadio,
  XqRadioButton,
  XqRadioGroup,
  XqResult,
  XqRow,
  XqScrollbar,
  XqSegmented,
  XqSelect,
  XqSkeleton,
  XqSpace,
  XqSplit,
  XqStep,
  XqSteps,
  XqSubMenu,
  XqSwitch,
  XqTable,
  XqTableColumn,
  XqTabPane,
  XqTabs,
  XqTag,
  XqTimeline,
  XqTimelineItem,
  XqTooltip,
  XqTree,
  XqTreeV2,
  XqUpload,
} from "@xquant/x-ui-plus"

// Directives
const plugins = [XqLoading, XqInfiniteScroll]

const components = [
  XqTag,
  XqAffix,
  XqAside,
  XqAutocomplete,
  XqSkeleton,
  XqSegmented,
  XqBreadcrumb,
  XqBreadcrumbItem,
  XqScrollbar,
  XqSubMenu,
  XqButton,
  XqFloatButton,
  XqFloatButtonItem,
  XqButtonGroup,
  XqCol,
  XqRow,
  XqSpace,
  XqDivider,
  XqCard,
  XqDropdown,
  XqDialog,
  XqMenu,
  XqMenuItem,
  XqDropdownItem,
  XqDropdownMenu,
  XqIcon,
  XqInput,
  XqInputNumber,
  XqForm,
  XqFormItem,
  XqFormLayoutItem,
  XqFormQuery,
  XqPopover,
  XqPopper,
  XqTooltip,
  XqDrawer,
  XqPagination,
  XqAlert,
  XqRadio,
  XqRadioButton,
  XqRadioGroup,
  XqDescriptions,
  XqDescriptionsItem,
  XqBacktop,
  XqSwitch,
  XqBadge,
  XqTabs,
  XqTabPane,
  XqAvatar,
  XqEmpty,
  XqCollapse,
  XqCollapseItem,
  XqTree,
  XqTreeV2,
  XqPopconfirm,
  XqCheckbox,
  XqCheckboxGroup,
  XqTable,
  XqProTable,
  XqTableColumn,
  XqEditTableColumn,
  XqLink,
  XqColorPicker,
  XqSelect,
  XqFieldSelectTree,
  XqOption,
  XqTimeline,
  XqTimelineItem,
  XqResult,
  XqSteps,
  XqStep,
  XqPageHeader,
  XqUpload,
  XqDatePicker,
  XqImage,
  XqSplit,
]

export function XUIPlus(app: App) {
  // 注册组件
  components.forEach((component: Component) => {
    app.component(component.name!, component)
  })
  // 注册指令
  plugins.forEach(plugin => {
    app.use(plugin)
  })
}
