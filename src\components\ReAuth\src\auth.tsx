import { defineComponent, Fragment } from "vue"

import { hasAuth, hasEveryAuth } from "@/utils/auth"
import type { PropType } from "vue"

export default defineComponent({
  name: "Auth",
  props: {
    value: {
      type: Array as PropType<Array<string>>,
      default: () => [],
    },
    every: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { slots }) {
    return () => {
      if (!slots) return null
      const hasPermission = props.every ? hasEveryAuth(props.value) : hasAuth(props.value)
      return hasPermission ? <Fragment>{slots.default?.()}</Fragment> : null
    }
  },
})
