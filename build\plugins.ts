import path from "path"
import { visualizer } from "rollup-plugin-visualizer"
import AutoImport from "unplugin-auto-import/vite"
import qiankun from "vite-plugin-qiankun-lite"
import removeConsole from "vite-plugin-remove-console"
import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import vueDevTools from "vite-plugin-vue-devtools"

import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"

import { cdn } from "./cdn"
import { configCompressPlugin } from "./compress"
import { viteBuildInfo } from "./info"
import { vitePerformancePlugin } from "./performance-monitor"

export function getPluginsList(
  command: string,
  VITE_CDN: boolean,
  VITE_COMPRESSION: ViteCompression,
  appName: string
) {
  const lifecycle = process.env.npm_lifecycle_event
  return [
    vue(),
    // jsx、tsx语法支持
    vueJsx(),
    vueDevTools(), // 预设的自动导入
    VITE_CDN ? cdn : null,
    // AutoImport配置，自动导入常用函数
    AutoImport({
      imports: [
        "vue",
        "vue-router",
        {
          "es-toolkit/object": ["cloneDeep"],
          "es-toolkit/compat": ["intersection", "isArray", "isObjectLike"],
          "es-toolkit/predicate": ["isFunction"],
          "es-toolkit/function": ["debounce", "throttle"],
          "../src/utils/legacy": [
            "isAllEmpty",
            "storageSession",
            "subBefore",
            "subAfter",
            "formatBytes",
          ],
        },
      ],
      dts: true,
      resolvers: [],
      eslintrc: {
        enabled: true,
        filepath: "./.eslintrc-auto-import.json",
        globalsPropValue: true,
      },
      vueTemplate: true,
      // 增强配置以确保类型正确解析
      dirs: [],
    }),
    configCompressPlugin(VITE_COMPRESSION),
    // 线上环境删除console
    removeConsole({ external: [] }),
    viteBuildInfo(),
    // svg组件化支持
    // svgLoader(),
    createSvgIconsPlugin({
      iconDirs: [
        path.resolve(process.cwd(), "src/assets/status"),
        path.resolve(process.cwd(), "src/assets/svg"),
        path.resolve(process.cwd(), "src/assets/svg/menuIcon"),
      ],
      symbolId: "icon-[dir]-[name]",
      customDomId: "__cube__svg__icons__dom__",
    }),
    // 手动按需导入 XUI 组件
    // ComponentCSSTreeShaking({
    //   lib: "@xquant/x-ui-plus",
    //   prefix: "Xq",
    //   useSource: true, // 使用scss文件，使用自定义 css namespace 必须
    //   ignoreComponents: ["XqAutoResizer"]
    // }),
    // 自动按需导入 XUI 组件（会存在跳转页面 reloading 情况，体验不是很好）    // AutoImport({
    //   resolvers: [XUIPlusResolver()]
    // }),
    // Components({
    //   resolvers: [XUIPlusResolver()]
    // }),
    qiankun({
      name: appName,
    }),
    // 性能监控插件
    vitePerformancePlugin(),
    // 打包分析
    lifecycle === "report"
      ? visualizer({ open: true, brotliSize: true, filename: "report.html" })
      : null,
  ]
}
