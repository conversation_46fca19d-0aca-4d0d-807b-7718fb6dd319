/**
 * 利差页面类型
 */
export const SpreadPageTypeEnum = {
  /** 静态视图 */
  STATIC: "static",
  /** 动态视图 */
  DYNAMIC: "dynamic",
} as const

/**
 * 自定义筛选条件窗口选项卡类型枚举
 */
export const CustomFilterTabEnum = {
  /** 内部行业 */
  TAB_1: "01",
  /** 债券代码 */
  TAB_2: "02",
  /** 债券简称 */
  TAB_3: "03",
  /** 债券品种 */
  TAB_4: "04",
  /** 额度占用方 */
  TAB_5: "05",
  /** 个性化标签 */
  TAB_6: "06",
} as const

/**
 * 发行方式枚举
 */
export const IssueMethodEnum = {
  /** 公募 */
  PUBLIC: "01",
  /** 私募 */
  PRIVATE: "02",
}

/**
 * 到期期限（考虑行权）选项类型枚举
 */
export const ExerciseDeadlineEnum = {
  /** 397天及以内 */
  OPTION_1: "01",
  /** 397天-2年 */
  OPTION_2: "02",
  /** 2-3年 */
  OPTION_3: "03",
  /** 3-5年 */
  OPTION_4: "04",
  /** 5年以上 */
  OPTION_5: "05",
} as const

/**
 * 自定义【横轴维度】选项列表（静态视图）
 */
export const CustomStaticXAxisEnum = {
  /** 内部行业 */
  X_AXIS_1: "01",
  /** 资产代码 */
  X_AXIS_2: "02",
  /** 债券简称 */
  X_AXIS_3: "03",
  /** 债券品种 */
  X_AXIS_4: "04",
  /** 额度占用方 */
  X_AXIS_5: "05",
  /** 发行方式 */
  X_AXIS_6: "06",
  /** 到期期限（考虑行权） */
  X_AXIS_7: "07",
  /** 利差区间 */
  X_AXIS_8: "08",
  /** 个性化标签 */
  X_AXIS_9: "09",
  /** 企业性质 */
  X_AXIS_10: "10",
  /** 内部评级 */
  X_AXIS_11: "11",
}

/**
 * 自定义【横轴维度】选项列表（动态视图）
 */
export const CustomDynamicXAxisEnum = {
  /** 日度 */
  X_AXIS_1: "01",
  /** 周度 */
  X_AXIS_2: "02",
  /** 月度 */
  X_AXIS_3: "03",
  /** 季度 */
  X_AXIS_4: "04",
  /** 半年度 */
  X_AXIS_5: "05",
  /** 年度 */
  X_AXIS_6: "06",
}

/**
 * 自定义纵轴维度类型枚举（静态视图）
 */
export const CustomStaticYAxisEnum = {
  /** 券面总额 */
  Y_AXIS_1: "01",
  /** 利差 */
  Y_AXIS_2: "02",
  /** 利差均值 */
  Y_AXIS_3: "03",
  /** 利差中位数 */
  Y_AXIS_4: "04",
  /** 利差标准差 */
  Y_AXIS_5: "05",
}

/**
 * 自定义纵轴维度类型枚举（动态视图）
 */
export const CustomDynamicYAxisEnum = {
  /** 利差 */
  Y_AXIS_1: "01",
  /** 利差均值 */
  Y_AXIS_2: "02",
  /** 利差中位数 */
  Y_AXIS_3: "03",
  /** 利差标准差 */
  Y_AXIS_4: "04",
}

/**
 * 自定义标签（只存在于动态视图中）枚举
 */
export const CustomDataTagEnum = {
  /** 内部行业 */
  TAG_1: "01",
  /** 债券品种 */
  TAG_2: "02",
  /** 发行方式 */
  TAG_3: "03",
  /** 行权期限 */
  TAG_4: "04",
  /** 个性化标签 */
  TAG_5: "05",
  /** 企业性质 */
  TAG_6: "06",
  /** 内部评级 */
  TAG_7: "07",
}

/* ------------------------------ 【发行方式枚举】选项列表 ------------------------------ */
export const issueMethodOptions = [
  {
    label: "公募",
    value: IssueMethodEnum.PUBLIC,
  },
  {
    label: "私募",
    value: IssueMethodEnum.PRIVATE,
  },
]

/* ----------------------------- 【到期期限（考虑行权）】选项列表 ----------------------------- */
/**
 * 到期期限（考虑行权）】选项列表
 */
export const exerciseDeadlineOptions = [
  {
    label: "397天及以内",
    value: ExerciseDeadlineEnum.OPTION_1,
  },
  {
    label: "397天-2年",
    value: ExerciseDeadlineEnum.OPTION_2,
  },
  {
    label: "2-3年",
    value: ExerciseDeadlineEnum.OPTION_3,
  },
  {
    label: "3-5年",
    value: ExerciseDeadlineEnum.OPTION_4,
  },
  {
    label: "5年以上",
    value: ExerciseDeadlineEnum.OPTION_5,
  },
]

/* ------------------------- 自定义【横轴维度】选项列表（静态视图）选项列表 ------------------------ */
/**
 * 自定义【横轴维度】选项列表（静态视图）
 */
export const customStaticXAxisOptions = [
  {
    label: "内部行业",
    value: CustomStaticXAxisEnum.X_AXIS_1,
  },
  {
    label: "资产代码",
    value: CustomStaticXAxisEnum.X_AXIS_2,
  },
  {
    label: "债券简称",
    value: CustomStaticXAxisEnum.X_AXIS_3,
  },
  {
    label: "债券品种",
    value: CustomStaticXAxisEnum.X_AXIS_4,
  },
  {
    label: "额度占用方",
    value: CustomStaticXAxisEnum.X_AXIS_5,
  },
  {
    label: "发行方式",
    value: CustomStaticXAxisEnum.X_AXIS_6,
  },
  {
    label: "到期期限（考虑行权）",
    value: CustomStaticXAxisEnum.X_AXIS_7,
  },
  {
    label: "利差区间",
    value: CustomStaticXAxisEnum.X_AXIS_8,
  },
  {
    label: "个性化标签",
    value: CustomStaticXAxisEnum.X_AXIS_9,
  },
  {
    label: "企业性质",
    value: CustomStaticXAxisEnum.X_AXIS_10,
  },
  {
    label: "内部评级",
    value: CustomStaticXAxisEnum.X_AXIS_11,
  },
]

/* ------------------------- 自定义【横轴维度】选项列表（动态视图）选项列表 ------------------------ */
export const customDynamicXAxisOptions = [
  {
    label: "日度",
    value: CustomDynamicXAxisEnum.X_AXIS_1,
  },
  {
    label: "周度",
    value: CustomDynamicXAxisEnum.X_AXIS_2,
  },
  {
    label: "月度",
    value: CustomDynamicXAxisEnum.X_AXIS_3,
  },
  {
    label: "季度",
    value: CustomDynamicXAxisEnum.X_AXIS_4,
  },
  {
    label: "半年度",
    value: CustomDynamicXAxisEnum.X_AXIS_5,
  },
  {
    label: "年度",
    value: CustomDynamicXAxisEnum.X_AXIS_6,
  },
]

/* ------------------------- 【自定义纵轴维度类型枚举（静态视图）】选项列表 ------------------------ */
/**
 * 自定义【纵轴维度】选项列表（静态视图）
 */
export const customStaticYAxisOptions = [
  {
    label: "券面总额",
    value: CustomStaticYAxisEnum.Y_AXIS_1,
  },
  {
    label: "利差",
    value: CustomStaticYAxisEnum.Y_AXIS_2,
  },
  {
    label: "利差均值",
    value: CustomStaticYAxisEnum.Y_AXIS_3,
  },
  {
    label: "利差中位数",
    value: CustomStaticYAxisEnum.Y_AXIS_4,
  },
  {
    label: "利差标准差",
    value: CustomStaticYAxisEnum.Y_AXIS_5,
  },
]

/* ------------------------- 【自定义纵轴维度类型枚举（动态视图）】选项列表 ------------------------ */
/**
 * 自定义【纵轴维度】选项列表（动态视图）
 */
export const customDynamicYAxisOptions = [
  {
    label: "利差",
    value: CustomDynamicYAxisEnum.Y_AXIS_1,
  },
  {
    label: "利差均值",
    value: CustomDynamicYAxisEnum.Y_AXIS_2,
  },
  {
    label: "利差中位数",
    value: CustomDynamicYAxisEnum.Y_AXIS_3,
  },
  {
    label: "利差标准差",
    value: CustomDynamicYAxisEnum.Y_AXIS_4,
  },
]

/* ---------------------------- 自定义标签（只存在于动态视图中） ---------------------------- */
export const customDataTagOptions = [
  {
    label: "内部行业",
    value: CustomDataTagEnum.TAG_1,
  },
  {
    label: "债券品种",
    value: CustomDataTagEnum.TAG_2,
  },
  {
    label: "发行方式",
    value: CustomDataTagEnum.TAG_3,
  },
  {
    label: "行权期限",
    value: CustomDataTagEnum.TAG_4,
  },
  {
    label: "个性化标签",
    value: CustomDataTagEnum.TAG_5,
  },
  {
    label: "企业性质",
    value: CustomDataTagEnum.TAG_6,
  },
  {
    label: "内部评级",
    value: CustomDataTagEnum.TAG_7,
  },
]
