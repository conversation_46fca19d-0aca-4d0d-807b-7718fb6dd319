<script setup lang="ts">
import { ref } from "vue"

import { useQueryForm } from "../../hooks/preference/useQueryForm"

defineOptions({
  name: "PreferenceQueryForm",
})

const { formModels, listLoading, handleQuery: handleFinish, handleReset } = useQueryForm()

// 表单折叠状态
const isCollapsed = ref(false)
</script>

<template>
  <xq-form-query
    label-width="100"
    :collapsed="isCollapsed"
    :model="formModels"
    :form-layout="{ gutter: 20, span: 6 }"
  >
    <!-- 指标编码 -->
    <xq-form-layout-item label="指标编码">
      <xq-input
        v-model="formModels.indexCode"
        class="w-full"
        placeholder="请输入指标编码"
        clearable
      />
    </xq-form-layout-item>

    <!-- 指标管理部门 -->
    <xq-form-layout-item label="指标管理部门">
      <xq-input
        v-model="formModels.indexManageDept"
        class="w-full"
        placeholder="请输入指标管理部门"
        clearable
      />
    </xq-form-layout-item>

    <!-- 指标状态 -->
    <xq-form-layout-item label="指标状态">
      <xq-input
        v-model="formModels.vcItStatus"
        class="w-full"
        placeholder="请输入指标状态"
        clearable
      />
    </xq-form-layout-item>

    <!-- 查询区间 -->
    <xq-form-layout-item label="查询区间">
      <xq-date-picker
        v-model="formModels.queryDateRange"
        type="daterange"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="w-full"
        clearable
      />
    </xq-form-layout-item>

    <template #submitter>
      <div class="flex items-center space-x-2">
        <xq-button type="primary" :loading="listLoading" @click="handleFinish">查询</xq-button>
        <xq-button @click="handleReset">重置</xq-button>
        <xq-button type="text" @click="isCollapsed = !isCollapsed">
          {{ isCollapsed ? "展开" : "收起" }}
          <xq-icon :name="isCollapsed ? 'ArrowDown' : 'ArrowUp'" />
        </xq-button>
      </div>
    </template>
  </xq-form-query>
</template>
