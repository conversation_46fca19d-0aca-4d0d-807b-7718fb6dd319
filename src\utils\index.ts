import { AxiosRequestConfig, AxiosResponse } from "axios"
import { isFunction } from "es-toolkit/predicate"
import jsFileDownload from "js-file-download"

import { ServerConfig } from "@/types"
import { http } from "@/utils/http"
import { XqMessage, XqMessageBox } from "@xquant/x-ui-plus"

import { RequestMethods } from "./http/types"

/**
 * 关闭当前活跃的tag
 */
export function closeCurrentActiveTag() {
  const activeDom = document.querySelector(
    "#tags-view-container .xqlayout-tags-view-item.active"
  )
  if (!activeDom) return
  // 手动去关闭的时候可能会存在异步的操作,所以需要路由跳转之后才能去关闭
  setTimeout(() => {
    activeDom.querySelector("i")?.click()
  }, 500)
}

/**
 * 生成唯一标识
 */
export function uuid() {
  let d = Date.now()
  if (performance !== undefined && isFunction(performance.now)) {
    d += performance.now()
  }
  return (
    "x" +
    "xxxxxxxx-fxxx-wxxx-fxxx-exxxxxxxxxx".replace(/[xy]/g, c => {
      const r = (d + Math.random() * 16) % 16 | 0
      d = Math.floor(d / 16)
      return (c === "x" ? r : (r & 0x3) | 0x8).toString(16)
    })
  )
}

/**
 * 确认函数
 */
export async function checkTip(message: string, callback?: () => void) {
  return XqMessageBox.confirm(message, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    customClass: "check-tip",
    type: "warning",
    closeOnClickModal: false,
    closeOnPressEscape: false,
    beforeClose: (action, instance, done) => {
      if (callback) {
        callback()
      }
      done()
    },
  })
}

/**
 * 空闲请求封装
 */
export function idleHttp(
  url: string,
  method: RequestMethods,
  config: AxiosRequestConfig,
  options?: {
    /** 最大等待时间（毫秒） */
    timeout: number
    /** 是否启用超时回退 */
    fallback: boolean
  }
) {
  const { timeout = 2000, fallback = true } = options || {}

  return new Promise((resolve, reject) => {
    const controller = new AbortController()
    const enhancedConfig = {
      ...config,
      signal: controller.signal,
    }

    // 空闲任务回调
    const idleTask: IdleRequestCallback = deadline => {
      if (deadline.timeRemaining() > 0 || deadline.didTimeout) {
        http.request(method, url, enhancedConfig).then(resolve).catch(reject)
      } else {
        if (fallback) scheduleFallback()
      }
    }

    // 超时回退机制
    const scheduleFallback = () => {
      setTimeout(() => {
        if (!requestId) controller.abort()
        http.request(method, url, enhancedConfig).then(resolve).catch(reject)
      })
    }

    // 注册空闲回调
    const requestId = requestIdleCallback(idleTask, { timeout })

    // 主动取消接口
    return () => {
      cancelIdleCallback(requestId)
      controller.abort()
    }
  })
}

/**
 * 文件下载功能
 */
export async function download(
  /** 请求配置 */
  serverConfig: ServerConfig,
  /** 文件保存名 */
  filename?: string,
  /** 成功提示文字 */
  successTips?: string,
  /** 失败提示文字 */
  errorTips?: string,
  /** 文件名解析失败时的缺省名 */
  defaultFilename = "未知文件名"
) {
  try {
    const { method = "get", url, headers = {}, params = {} } = serverConfig
    const res: AxiosResponse = await http.request(
      method,
      url as string,
      params,
      {
        timeout: 60000,
        headers,
        responseType: "blob",
      }
    )

    const hasServerError = await checkIfError(res, errorTips as string)
    if (hasServerError) return

    let name

    const disposition =
      res.headers["content-disposition"] || res.headers["Content-Disposition"]
    const result = disposition.match("filename=(.*)")

    if (result) {
      name = decodeURIComponent(result[1])
    } else {
      name = defaultFilename
    }

    if (filename) {
      name = `${filename}${name.substring(name.lastIndexOf("."))}`
    }

    try {
      jsFileDownload(res.data, name)
      XqMessage.success(successTips || `【${name}】文件下载成功`)
    } catch (error) {
      XqMessage.error(errorTips || (error as Error).message || "文件保存失败")
    }
  } catch (error) {
    XqMessage.error(errorTips || (error as Error).message || "文件下载失败")
  }
}

/* ---------------------------------- 内部函数 ---------------------------------- */
// 校验返回的 Blob 中是否包含错误信息
// 如果包含错误信息，则提示错误并返回 true，否则返回 false
// 注意：此函数假设返回的 Blob 是 JSON 格式的错误信息
function checkIfError(res: AxiosResponse, errorTips: string) {
  return new Promise(resolve => {
    const blob = new Blob([res.data])
    const fileReader = new FileReader()
    fileReader.onload = () => {
      const text = fileReader.result as string
      if (!isJSON(text)) {
        const json = JSON.parse(text)
        XqMessage.error(errorTips || json.message)
        resolve(true)
      } else {
        resolve(false)
      }
    }
    fileReader.readAsText(blob)
  })
}

// 检查字符串是否为有效的 JSON 格式
function isJSON(str: string) {
  try {
    JSON.parse(str)
    return true
  } catch (err: unknown) {
    return false
  }
}
