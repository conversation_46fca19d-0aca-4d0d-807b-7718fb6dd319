# 表格分组样式工具函数使用说明

## 概述

`tableStyleUtils.ts` 提供了一套通用的表格分组样式工具函数，用于统一处理分组表头和单元格的样式类名。这些工具函数配合全局样式 `src/style/index.scss` 中的分组表格样式，可以快速实现统一的分组表格UI效果。

## 全局样式类名

全局样式已在 `src/style/index.scss` 中定义，包含以下样式类：

### 表头样式
- `product-group-header` / `product-header` - 产品信息分组表头（蓝色系）
- `indicator-group-header` / `indicator-header` - 指标基础信息分组表头（橙色系）  
- `usage-group-header` / `usage-header` - 指标使用情况分组表头（绿色系）

### 单元格样式
- `product-cell` / `product-cell-striped` - 产品信息单元格（蓝色系）
- `indicator-cell` / `indicator-cell-striped` - 指标基础信息单元格（橙色系）
- `usage-cell` / `usage-cell-striped` - 指标使用情况单元格（绿色系）
- `normal-cell-striped` - 普通列斑马纹
- `status-error` - 状态错误文字（红色）

## 基础用法

### 1. 导入工具函数

```typescript
import { createGroupedTableStyleFunctions } from "@/utils/tableStyleUtils"
```

### 2. 创建样式函数

```typescript
// 使用默认分组标签
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions()

// 或使用自定义分组标签
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions({
  productLabel: "产品信息",
  indicatorLabel: "指标基础信息", 
  usageLabel: "指标使用情况"
})
```

### 3. 在表格中使用

```vue
<template>
  <xq-table
    :data="tableData"
    :header-cell-class-name="getHeaderCellClassName"
    :cell-class-name="getCellClassName"
  >
    <!-- 产品信息分组 -->
    <xq-table-column label="产品信息" align="center">
      <xq-table-column prop="vcFundCode" label="产品代码" />
      <xq-table-column prop="vcFundName" label="产品名称" />
    </xq-table-column>

    <!-- 指标基础信息分组 -->
    <xq-table-column label="指标基础信息" align="center">
      <xq-table-column prop="vcItCode" label="指标编号" />
      <xq-table-column prop="vcItName" label="指标名称" />
    </xq-table-column>

    <!-- 指标使用情况分组 -->
    <xq-table-column label="指标使用情况" align="center">
      <xq-table-column prop="vcItStatus" label="指标状态" />
    </xq-table-column>
  </xq-table>
</template>
```

## 高级用法

### 带状态错误处理

如果需要对特定状态值显示错误样式（如红色文字），可以使用 `getCellClassNameWithStatus`：

```typescript
const { getHeaderCellClassName, getCellClassNameWithStatus } = createGroupedTableStyleFunctions()

// 自定义单元格样式函数
const getCellClassName = (params: { column: any; row: any; rowIndex: number }) => {
  return getCellClassNameWithStatus({
    ...params,
    statusField: "vcItStatus",        // 状态字段名
    errorValues: ["违规", "预警"]      // 错误状态值
  })
}
```

### 直接使用工具函数

也可以直接使用单个工具函数：

```typescript
import { 
  getGroupedHeaderCellClassName,
  getGroupedCellClassName,
  getGroupedCellClassNameWithStatus 
} from "@/utils/tableStyleUtils"

// 表头样式
const headerClassName = getGroupedHeaderCellClassName({ column })

// 普通单元格样式
const cellClassName = getGroupedCellClassName({ column, row, rowIndex })

// 带状态处理的单元格样式
const cellClassNameWithStatus = getGroupedCellClassNameWithStatus({ 
  column, 
  row, 
  rowIndex,
  statusField: "status",
  errorValues: ["error", "warning"]
})
```

## 完整示例

参考 `src/modules/risk-indicators/components/compliance/table.vue` 的实现：

```vue
<script setup lang="ts">
import { createGroupedTableStyleFunctions } from "@/utils/tableStyleUtils"

// 创建样式函数
const { getHeaderCellClassName, getCellClassNameWithStatus } = createGroupedTableStyleFunctions()

// 带状态错误处理的单元格样式
const getCellClassName = (params: { column: any; row: any; rowIndex: number }) => {
  return getCellClassNameWithStatus({
    ...params,
    statusField: "vcItStatus",
    errorValues: ["违规", "预警"]
  })
}
</script>

<template>
  <xq-table
    :data="tableData"
    :header-cell-class-name="getHeaderCellClassName"
    :cell-class-name="getCellClassName"
  >
    <!-- 表格列定义 -->
  </xq-table>
</template>
```

## 注意事项

1. **分组标签匹配**：确保表格列的 `label` 属性与工具函数中的分组标签一致
2. **全局样式依赖**：需要确保 `src/style/index.scss` 中的分组表格样式已正确引入
3. **动态前缀**：全局样式使用了 `#{$ns}` 动态前缀，确保项目配置正确
4. **扩展性**：如需新增分组类型，可在全局样式和工具函数中同步添加对应的样式类和逻辑
