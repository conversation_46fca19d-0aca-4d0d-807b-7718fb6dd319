import { http } from "@/utils/http"

import type {
  PreferenceFormModel,
  PreferenceTableData,
} from "../types"

/**
 * 偏好类指标明细列表查询参数
 */
export interface PreferenceListParams extends PreferenceFormModel {
  /** 当前页码 */
  page: number
  /** 每页条数 */
  pageSize: number
}

/**
 * 偏好类指标明细列表响应数据
 */
export interface PreferenceListResponse {
  /** 列表数据 */
  list: PreferenceTableData[]
  /** 总条数 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页条数 */
  pageSize: number
}

/**
 * 获取偏好类指标明细列表
 */
export function getPreferenceList(params: PreferenceListParams) {
  return http.get<PreferenceListResponse>(
    "/api/risk-indicators/preference/list",
    {
      params,
    }
  )
}

/**
 * 导出偏好类指标明细列表
 */
export function exportPreferenceList(params: PreferenceListParams) {
  return http.post("/api/risk-indicators/preference/export", params, {
    responseType: "blob",
  })
}
