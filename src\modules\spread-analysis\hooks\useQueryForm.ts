// import { useStaticViewStore } from "../stores/staticViewStore"
import { QueryFormModel } from "../types"

interface QueryFormReturn {
  formModels: QueryFormModel
}

export function useQueryForm(): QueryFormReturn {
  // const store = useStaticViewStore()

  const formModels = ref<QueryFormModel>({
    // 内部行业
    industry: "",
    // 债券代码
    bondCode: [],
    // 债券简称
    bondName: [],
    // 债券品种
    bondType: [],
    // 额度占用方
    quotaOccupy: [],
    // 发行方式
    issueMethod: [],
    // 行权期限
    exerciseDeadline: [],
    // 企业性质
    enterpriseNature: [],
    // 内部评级
    internalRating: "",
    // 个性化标签
    personalizedTags: "",
    // 利差区间
    interestMarginRange: [],
    interestMarginRangeStart: "",
    interestMarginRangeEnd: "",
    // 查询日期
    queryDate: [],
  })

  return {
    formModels,
  }
}
