import {
  computed,
  ref,
} from "vue"

import { defineStore } from "pinia"

import { useComplianceService } from "@/services/useComplianceService"

import type { ComplianceFormModel } from "../types"

/**
 * 监管合规风险指标明细 Store
 */
export const useComplianceStore = defineStore(
  "risk-indicators-compliance",
  () => {
    const { useComplianceList, useComplianceExport } = useComplianceService()

    // 表单数据
    const queryForm = ref<ComplianceFormModel>({
      vcFundTypeCode: "",
      vcFundSeriesName: "",
      vcFundCode: "",
      vcManagerName: "",
      vcItType: "",
      vcItCode: "",
      vcItName: "",
      vcItStatus: "",
      vcCheckedStatus: "",
      startDate: "",
      endDate: "",
    })

    // 分页参数
    const pagination = ref({
      page: 1,
      pageSize: 10,
    })

    // 组合查询参数
    const queryParams = computed(() => ({
      ...queryForm.value,
      ...pagination.value,
    }))

    // 是否启用查询
    const enableQuery = ref(true)

    // 获取列表数据
    const {
      data: listData,
      isLoading: listLoading,
      error: listError,
      refetch: refetchList,
    } = useComplianceList(queryParams, {
      enabled: enableQuery.value,
    })

    // 导出功能
    const { mutate: exportData, isPending: exportLoading } =
      useComplianceExport()

    // 表格数据
    const tableData = computed(() => listData.value?.data?.list || [])
    const total = computed(() => listData.value?.data?.total || 0)

    /**
     * 执行查询
     */
    function handleQuery() {
      pagination.value.page = 1
      enableQuery.value = true
      refetchList()
    }

    /**
     * 重置查询条件
     */
    function handleReset() {
      queryForm.value = {
        vcFundTypeCode: "",
        vcFundSeriesName: "",
        vcFundCode: "",
        vcManagerName: "",
        vcItType: "",
        vcItCode: "",
        vcItName: "",
        vcItStatus: "",
        vcCheckedStatus: "",
        startDate: "",
        endDate: "",
      }
      pagination.value.page = 1
      enableQuery.value = false
    }

    /**
     * 分页变化
     */
    function handlePageChange(page: number) {
      pagination.value.page = page
      if (enableQuery.value) {
        refetchList()
      }
    }

    /**
     * 页面大小变化
     */
    function handlePageSizeChange(pageSize: number) {
      pagination.value.pageSize = pageSize
      pagination.value.page = 1
      if (enableQuery.value) {
        refetchList()
      }
    }

    /**
     * 导出数据
     */
    function handleExport() {
      exportData(queryForm.value, {
        onSuccess: data => {
          console.log("导出成功:", data)
          // 这里可以添加下载逻辑
        },
        onError: error => {
          console.error("导出失败:", error)
        },
      })
    }

    return {
      // 状态
      queryForm,
      pagination,
      queryParams,
      enableQuery,

      // 计算属性
      tableData,
      total,

      // 加载状态
      listLoading,
      exportLoading,
      listError,

      // 方法
      handleQuery,
      handleReset,
      handlePageChange,
      handlePageSizeChange,
      handleExport,
      refetchList,
    }
  }
)
