import Layout from "@/layout/LayoutProxy"

export default {
  path: "/attribution-analysis",
  name: "AttributionAnalysis",
  component: Layout,
  redirect: "/attribution-analysis/index",
  meta: {
    icon: "homeFilled",
    title: "归因分析",
  },
  children: [
    {
      path: "/attribution-analysis/index",
      name: "AttributionAnalysisIndex",
      component: () => import("@/modules/attribution-analysis/views/index.vue"),
      meta: {
        title: "归因分析",
      },
    },
  ],
} as RouteConfigsTable
