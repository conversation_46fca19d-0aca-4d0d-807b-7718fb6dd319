<script lang="ts" setup>
import {
  QLayout,
  useLayoutAppStore,
  useLayoutPermissionStore,
  useLayoutSettingsStore,
  useLayoutUserStore,
} from "@xquant/xp-admin-layout"
import layoutCss from "@/style/xp-admin-layout.scss?inline"
import { usePermissionStore } from "@/store/modules/permission"
import { useUserStore } from "@/store/modules/user"
import type { RouteLocationNormalizedLoaded } from "vue-router"
import { default as setRouteTitle } from "./utils/customTagName"
import { inertCSS } from "./utils/index"
import { POWERED_BY_QIANKUN } from "@/business/dictionary"

if (!POWERED_BY_QIANKUN) {
  inertCSS(layoutCss, "layout-css")
}

const appStore = useLayoutAppStore()
appStore.resolveOnlyOneChild = false
appStore.disableLogoLink = true
appStore.logo = "/logo.png"
appStore.miniLogo = "/logo_small.png"
appStore.title = "杭州衡泰"

const settingsStore = useLayoutSettingsStore()
settingsStore.isCollapse = false
settingsStore.isLock = true

const userStore = useLayoutUserStore()
userStore.userMenus = ["个人档案", "修改密码"]
userStore.name = "张三"
userStore.avatar =
  "https://images.pexels.com/photos/1081685/pexels-photo-1081685.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"

//布局1
// appStore.$patch({
//   menuPosition: 'both',
//   tagsViewPosition: 'right',
//   routeLevel: 0
// })

//布局2
// appStore.$patch({
//   menuPosition: "both",
//   routeLevel: 0,
// });

//布局3
appStore.$patch({
  menuPosition: "left",
})

// 同步所有静、动态菜单
const permissionStore = useLayoutPermissionStore()
watch(
  () => usePermissionStore().wholeMenus,
  v => permissionStore.setRoutes(v),
  {
    immediate: true,
  }
)
// 刷新tag时如果不是当前 route，进行跳转处理
const router = useRouter()
const currentRoute = useRoute()
const refreshRoute = (route: RouteLocationNormalizedLoaded) => {
  if (route.path !== currentRoute.path) {
    nextTick(() => {
      router.push(route.fullPath)
    })
  }
}
// 针对动态路由，自定义打开的 tag 显示名称
const customTagName = async (openRoute: RouteLocationNormalizedLoaded) => {
  const name = await setRouteTitle(openRoute)
  if (name) {
    openRoute.meta.title = name
  }
  return Promise.resolve(openRoute)
}

const userMenuClick = (name: string) => {
  if (name == "logout") {
    useUserStore().logOut()
  }
}
</script>

<template>
  <QLayout
    keep-alive-name="KeepAliveKey"
    :custom-tag-name="customTagName"
    :user-menu-click="userMenuClick"
    @tag-refresh-after="refreshRoute"
  />
</template>
