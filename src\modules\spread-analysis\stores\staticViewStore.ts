import {
  CustomFilterTabType,
  QueryFormModel,
  StaticChartFormModel,
} from "../types"

interface State {
  /** 当前触发【自定义筛选条件窗口】的选项卡类型 */
  activeFilterTab: CustomFilterTabType | null
  /** 模板视图 */
  customViewId?: string
  /** 自定义数据筛选维度数据 */
  queryConfig: QueryFormModel | null
  /** 自定义数据展示维度数据 */
  chartConfig: StaticChartFormModel | null
}

export const useStaticViewStore = {
  id: "staticViewStore",
  state: (): State => ({
    activeFilterTab: null,
    customViewId: "",
    queryConfig: null,
    chartConfig: null,
  }),
}
