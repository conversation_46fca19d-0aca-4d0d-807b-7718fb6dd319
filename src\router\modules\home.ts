import Layout from '@/layout/LayoutProxy';

export default {
  path: "/",
  name: "Home",
  component: Layout,
  redirect: "/homepage",
  meta: {
    icon: "homeFilled",
    title: "首页",
    rank: 0,
  },
  children: [
    {
      path: "/homepage",
      name: "Homepage",
      component: () => import("@/views/homepage/index.vue"),
      meta: {
        title: "主面板",
      },
    },
    {
      path: "/permission",
      name: "Permission",
      component: () => import("@/views/permission/button/index.vue"),
      meta: {
        title: "按钮权限演示",
      },
    },
    {
      path: "/color-table-2",
      name: "ColorTable2",
      component: () => import("@/views/demo/xui-table.vue"),
      meta: {
        title: "xui 颜色表格演示",
      },
    },
  ],
} as RouteConfigsTable
