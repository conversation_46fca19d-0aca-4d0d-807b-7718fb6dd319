import dayjs from "dayjs"
import { resolve } from "path"
import {
  ConfigEnv,
  loadEnv,
  UserConfigExport,
} from "vite"

import { wrapperEnv } from "./build"
import {
  exclude,
  include,
} from "./build/optimize"
import { getPluginsList } from "./build/plugins"
import pkg from "./package.json"
import namespace, {
  cssVarNS,
  cubeUIBizNS,
} from "./src/namespace"

/** 当前执行node命令时文件夹的地址（工作目录） */
const root: string = process.cwd()

/** 路径查找 */
const pathResolve = (dir: string): string => {
  return resolve(__dirname, ".", dir)
}

/** 设置别名 */
const alias: Record<string, string> = {
  "@": pathResolve("src"),
  "@build": pathResolve("build"),
}

const { dependencies, devDependencies, name, version } = pkg
const APP_INFO = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
}

export default ({ command, mode }: ConfigEnv): UserConfigExport => {
  const {
    VITE_CDN,
    VITE_PORT,
    VITE_COMPRESSION,
    VITE_PUBLIC_PATH,
    VITE_APP_NAME,
    VITE_USE_MOCK,
    VITE_APP_BASE_API,
  } = wrapperEnv(loadEnv(mode, root))

  const SCSS_VARS = [
    `$ns: ${namespace};`, // 配置给业务层使用 x-ui-plus namespace
    `$var-ns: ${cssVarNS};`, // 配置给业务层使用 x-ui-plus css var namespace
    `$ns-biz: ${cubeUIBizNS};`, // 配置给业务层使用 cube-ui-biz namespace
    `$xui-namespace: $ns;`,
    `$xui-css-var-namespace: $var-ns;`,
    `@forward "@xquant/x-ui-plus/theme-chalk/src/mixins/config.scss" with (
      $namespace: $ns,
      $var-namespace: $var-ns
    );`, // 配置 x-ui namespace
    `@forward "@cube-ui/biz/styles/src/mixins/config.scss" with (
      $namespaceBiz: $ns-biz,
    );`, // 配置 @cube-ui/biz Css namespace
    `$app: #app-${VITE_APP_NAME};`, // 配置子应用入口
  ]

  // 是否为生产环境
  const isProd = mode === "production"

  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias,
    },
    // 缓存目录配置
    cacheDir: "node_modules/.vite",

    // 服务端配置 - 优化版本
    server: {
      port: VITE_PORT,
      host: "0.0.0.0",
      // HMR 优化
      hmr: {
        overlay: false, // 减少开发时的干扰
        clientPort: VITE_PORT,
      },
      // 文件系统访问优化
      fs: {
        strict: false,
        allow: [".."],
      },
      // 本地跨域代理 - 优化版本
      proxy: {
        [VITE_APP_BASE_API]: {
          target: "http://***********:8988/",
          changeOrigin: true,
          rewrite: path => path.replace(new RegExp(`^${VITE_APP_BASE_API}`), ""),
          // 代理优化
          timeout: 10000,
          configure: (proxy, options) => {
            proxy.on("error", (err, req, res) => {
              console.log("proxy error", err)
            })
          },
        },
      },
    },

    plugins: getPluginsList(command, VITE_CDN, VITE_COMPRESSION, VITE_APP_NAME, VITE_USE_MOCK),

    // 依赖优化 - 增强版本
    optimizeDeps: {
      include,
      exclude,
      force: false, // 不强制重新预构建
      esbuildOptions: {
        target: "es2020",
      },
    },

    // CSS 配置 - 优化版本
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: SCSS_VARS.join(""),
          charset: false,
          // 静默 deprecation 警告
          quietDeps: true,
          silenceDeprecations: ["legacy-js-api"],
        },
      },
      devSourcemap: false, // 开发环境禁用 CSS sourcemap
    },

    // 构建配置 - 优化版本
    build: {
      cssCodeSplit: true, // 启用 CSS 代码分割以提升缓存效率
      sourcemap: false,
      chunkSizeWarningLimit: 4000,
      // Terser 优化
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: isProd,
          drop_debugger: isProd,
          pure_funcs: isProd ? ["console.log"] : [],
        },
      },
      rollupOptions: {
        input: {
          index: pathResolve("index.html"),
        },
        // Tree-shaking 优化
        treeshake: {
          preset: "recommended",
          moduleSideEffects: false,
        },
        // 静态资源分类打包 - 优化版本
        output: {
          // 智能分包策略
          manualChunks: {
            // 框架核心
            vue: ["vue", "vue-router", "pinia"],
            // UI 组件库
            "ui-lib": ["@xquant/x-ui-plus", "@cube-ui/biz"],
            // 工具库
            utils: ["es-toolkit", "dayjs", "axios"],
            // 图表库
            charts: ["echarts", "vue-echarts"],
            // 第三方工具
            vendor: ["qs", "mitt", "nprogress"],
          },
          // 优化缓存策略的文件命名
          chunkFileNames: chunkInfo => {
            const facadeModuleId = chunkInfo.facadeModuleId
            if (facadeModuleId && facadeModuleId.includes("node_modules")) {
              return "static/js/vendor/[name]-[hash].js"
            }
            return "static/js/[name]-[hash].js"
          },
          entryFileNames: "static/js/[name]-[hash].js",
          // 按类型分类资源
          assetFileNames: assetInfo => {
            const extType = assetInfo.name.split(".").pop()
            if (["png", "jpg", "jpeg", "gif", "svg", "ico"].includes(extType)) {
              return "static/images/[name]-[hash].[ext]"
            }
            if (["woff", "woff2", "ttf", "eot"].includes(extType)) {
              return "static/fonts/[name]-[hash].[ext]"
            }
            return "static/[ext]/[name]-[hash].[ext]"
          },
        },
      },
    },

    define: {
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(APP_INFO),
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: mode !== "production",
    },
  }
}
