import Layout from "@/layout/LayoutProxy"

export default {
  path: "/proprietary-risk-overview",
  name: "ProprietaryRiskOverview",
  component: Layout,
  redirect: "/proprietary-risk-overview/index",
  meta: {
    icon: "homeFilled",
    title: "自营端风险情况总览",
  },
  children: [
    {
      path: "/proprietary-risk-overview/index",
      name: "ProprietaryRiskOverviewIndex",
      component: () =>
        import("@/modules/proprietary-risk-overview/views/index.vue"),
      meta: {
        title: "自营端风险情况总览",
      },
    },
  ],
} as RouteConfigsTable
