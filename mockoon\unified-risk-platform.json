{"uuid": "5c3c957d-9740-4622-b6e0-81c94b8f5a5e", "lastMigration": 33, "name": "Unified risk platform", "endpointPrefix": "", "latency": 0, "port": 3001, "hostname": "", "folders": [], "routes": [{"uuid": "login-route", "type": "http", "documentation": "用户登录接口", "method": "post", "endpoint": "login", "responses": [{"uuid": "login-success", "body": "{{> (dataRaw 'login-response.json')}}", "latency": 200, "statusCode": 200, "label": "登录成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "refresh-token-route", "type": "http", "documentation": "刷新Token接口", "method": "post", "endpoint": "refreshToken", "responses": [{"uuid": "refresh-token-success", "body": "{\n  \"success\": true,\n  \"data\": {\n    \"accessToken\": \"eyJhbGciOiJIUzUxMiJ9.refreshed\",\n    \"refreshToken\": \"eyJhbGciOiJIUzUxMiJ9.newRefresh\",\n    \"expires\": \"2025/12/30 00:00:00\"\n  }\n}", "latency": 100, "statusCode": 200, "label": "刷新成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "permissions-route", "type": "http", "documentation": "获取用户权限接口", "method": "get", "endpoint": "user/permissions", "responses": [{"uuid": "permissions-success", "body": "{{> (dataRaw 'permissions.json')}}", "latency": 150, "statusCode": 200, "label": "获取权限成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "async-routes-route", "type": "http", "documentation": "获取异步路由接口", "method": "get", "endpoint": "getAsyncRoutes", "responses": [{"uuid": "async-routes-success", "body": "{{> (dataRaw 'async-routes.json')}}", "latency": 200, "statusCode": 200, "label": "获取路由成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "compliance-list-route", "type": "http", "documentation": "监管合规风险指标明细列表", "method": "get", "endpoint": "api/risk-indicators/compliance/list", "responses": [{"uuid": "compliance-list-success", "body": "{\n  \"success\": true,\n  \"data\": {\n    \"list\": {{dataRaw 'compliance-list.json'}},\n    \"total\": 50,\n    \"page\": {{queryParam 'page' '1'}},\n    \"pageSize\": {{queryParam 'pageSize' '10'}}\n  }\n}", "latency": 300, "statusCode": 200, "label": "获取合规指标成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "internal-list-route", "type": "http", "documentation": "内部管理风险指标明细列表", "method": "get", "endpoint": "api/risk-indicators/internal/list", "responses": [{"uuid": "internal-list-success", "body": "{\n  \"success\": true,\n  \"data\": {\n    \"list\": {{dataRaw 'internal-list.json'}},\n    \"total\": 40,\n    \"page\": {{queryParam 'page' '1'}},\n    \"pageSize\": {{queryParam 'pageSize' '10'}}\n  }\n}", "latency": 300, "statusCode": 200, "label": "获取内部指标成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "preference-list-route", "type": "http", "documentation": "风险偏好指标明细列表", "method": "get", "endpoint": "api/risk-indicators/preference/list", "responses": [{"uuid": "preference-list-success", "body": "{\n  \"success\": true,\n  \"data\": {\n    \"list\": {{dataRaw 'preference-list.json'}},\n    \"total\": 30,\n    \"page\": {{queryParam 'page' '1'}},\n    \"pageSize\": {{queryParam 'pageSize' '10'}}\n  }\n}", "latency": 300, "statusCode": 200, "label": "获取偏好指标成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "spread-analysis-views-get", "type": "http", "documentation": "获取利差分析视图列表", "method": "get", "endpoint": "api/spread-analysis/views", "responses": [{"uuid": "spread-analysis-views-get-success", "body": "{\n  \"success\": true,\n  \"data\": {{dataRaw 'spread-analysis-views.json'}}\n}", "latency": 200, "statusCode": 200, "label": "获取视图列表成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "spread-analysis-views-post", "type": "http", "documentation": "创建利差分析视图", "method": "post", "endpoint": "api/spread-analysis/views", "responses": [{"uuid": "spread-analysis-views-post-success", "body": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"{{faker 'datatype.uuid'}}\",\n    \"message\": \"视图创建成功\"\n  }\n}", "latency": 400, "statusCode": 200, "label": "创建视图成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "spread-analysis-views-delete", "type": "http", "documentation": "删除利差分析视图", "method": "delete", "endpoint": "api/spread-analysis/views/*", "responses": [{"uuid": "spread-analysis-views-delete-success", "body": "{\n  \"success\": true,\n  \"data\": {\n    \"message\": \"视图删除成功\"\n  }\n}", "latency": 300, "statusCode": 200, "label": "删除视图成功", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "login-route"}, {"type": "route", "uuid": "refresh-token-route"}, {"type": "route", "uuid": "permissions-route"}, {"type": "route", "uuid": "async-routes-route"}, {"type": "route", "uuid": "compliance-list-route"}, {"type": "route", "uuid": "internal-list-route"}, {"type": "route", "uuid": "preference-list-route"}, {"type": "route", "uuid": "spread-analysis-views-get"}, {"type": "route", "uuid": "spread-analysis-views-post"}, {"type": "route", "uuid": "spread-analysis-views-delete"}], "proxyMode": false, "proxyHost": "", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [], "callbacks": []}