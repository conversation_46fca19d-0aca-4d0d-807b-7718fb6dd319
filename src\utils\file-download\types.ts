/**
 * 大文件下载相关类型定义
 */

export interface DownloadProgress {
  /** 已下载字节数 */
  downloaded: number
  /** 总文件大小（如果已知） */
  total?: number
  /** 下载进度百分比 */
  percentage: number
  /** 下载速度（字节/秒） */
  speed: number
  /** 预计剩余时间（秒） */
  remainingTime?: number
}

export interface DownloadOptions {
  /** 建议的文件名 */
  suggestedName?: string
  /** 文件类型过滤器 */
  types?: Array<{
    description: string
    accept: Record<string, string[]>
  }>
  /** 分块大小（字节），默认 1MB */
  chunkSize?: number
  /** 最大重试次数，默认 3 次 */
  maxRetries?: number
  /** 是否自动从响应头获取文件名，默认 true */
  autoDetectFilename?: boolean
  /** 请求头 */
  headers?: Record<string, string>
  /** 进度回调 */
  onProgress?: (progress: DownloadProgress) => void
  /** 错误回调 */
  onError?: (error: Error) => void
}

export interface DownloadResult {
  /** 是否成功 */
  success: boolean
  /** 文件句柄（如果成功） */
  fileHandle?: FileSystemFileHandle
  /** 错误信息（如果失败） */
  error?: string
  /** 最终文件名 */
  filename?: string
  /** 文件大小 */
  fileSize?: number
}

export interface ChunkInfo {
  /** 分块索引 */
  index: number
  /** 开始字节位置 */
  start: number
  /** 结束字节位置 */
  end: number
  /** 分块大小 */
  size: number
}

export interface WorkerMessage {
  /** 消息类型 */
  type: "start" | "progress" | "chunk" | "complete" | "error" | "cancel"
  /** 消息数据 */
  data?: any
  /** 错误信息 */
  error?: string
}

export interface DownloadTask {
  /** 任务ID */
  id: string
  /** 下载URL */
  url: string
  /** 文件名 */
  filename: string
  /** 任务状态 */
  status:
    | "pending"
    | "downloading"
    | "paused"
    | "completed"
    | "error"
    | "cancelled"
  /** 进度信息 */
  progress: DownloadProgress
  /** 创建时间 */
  createdAt: Date
  /** 更新时间 */
  updatedAt: Date
}
