import Layout from "@/layout/LayoutProxy"

export default {
  path: "/market-risk-analysis",
  name: "MarketRiskAnalysis",
  component: Layout,
  redirect: "/market-risk-analysis/index",
  meta: {
    icon: "homeFilled",
    title: "市场风险分析",
  },
  children: [
    {
      path: "/market-risk-analysis/index",
      name: "MarketRiskAnalysisIndex",
      component: () => import("@/modules/market-risk-analysis/views/index.vue"),
      meta: {
        title: "市场风险分析",
      },
    },
  ],
} as RouteConfigsTable
