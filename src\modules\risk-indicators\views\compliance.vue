<script setup lang="ts">
// 异步导入组件
const ComplianceQueryForm = defineAsyncComponent(
  () => import("../components/compliance/query-form.vue")
)
const ComplianceTable = defineAsyncComponent(() => import("../components/compliance/table.vue"))

defineOptions({
  name: "RiskIndicatorsCompliance",
})
</script>

<template>
  <xq-card
    class="h-full !overflow-y-scroll border-none"
    body-class="!p-[12px] h-full"
    shadow="never"
  >
    <div class="flex h-full flex-col">
      <module-title title="合规类指标" />

      <!-- 查询表单 -->
      <ComplianceQueryForm />

      <xq-divider />

      <module-panel title="筛选结果">
        <!-- 数据表格 -->
        <ComplianceTable />
      </module-panel>
    </div>
  </xq-card>
</template>
