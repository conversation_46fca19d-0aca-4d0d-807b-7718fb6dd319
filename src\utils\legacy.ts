/**
 * Legacy utility functions to replace @pureadmin/utils
 * Functions not available in es-toolkit are implemented here
 * Browser-compatible implementations only
 */

/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 */
export function isAllEmpty(value: any): boolean {
  if (value == null) return true
  if (typeof value === "string") return value.trim() === ""
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === "object") return Object.keys(value).length === 0
  return false
}

/**
 * Storage session utility
 */
export function storageSession() {
  return {
    getItem<T = any>(key: string): T | null {
      try {
        const item = sessionStorage.getItem(key)
        return item ? JSON.parse(item) : null
      } catch {
        return null
      }
    },
    setItem(key: string, value: any): void {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch {
        // Silent fail
      }
    },
    removeItem(key: string): void {
      try {
        sessionStorage.removeItem(key)
      } catch {
        // Silent fail
      }
    },
  }
}

/**
 * Extract substring before a delimiter
 */
export function subBefore(str: string, delimiter: string): string {
  const index = str.indexOf(delimiter)
  return index === -1 ? str : str.slice(0, index)
}

/**
 * Extract substring after a delimiter
 */
export function subAfter(str: string, delimiter: string): string {
  const index = str.indexOf(delimiter)
  return index === -1 ? "" : str.slice(index + delimiter.length)
}

/**
 * Format bytes to human readable string
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i]
}
