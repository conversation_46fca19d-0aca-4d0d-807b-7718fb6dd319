import {
  VcCheckedStatusEnum,
  VcFundTypeEnum,
  VcItStatusEnum,
  VcItTypeEnum,
  VcItTypeInternalEnum,
} from "./consts"

/**
 * 产品类型类型
 */
export type VcFundTypeType =
  (typeof VcFundTypeEnum)[keyof typeof VcFundTypeEnum]

/**
 * 指标状态类型
 */
export type VcItStatusType =
  (typeof VcItStatusEnum)[keyof typeof VcItStatusEnum]

/**
 * 指标类型类型
 */
export type VcItTypeType = (typeof VcItTypeEnum)[keyof typeof VcItTypeEnum]

/**
 * 指标类型类型 (内部管理模块)
 */
export type VcItTypeInternalType =
  (typeof VcItTypeInternalEnum)[keyof typeof VcItTypeInternalEnum]

/**
 * 指标处理状态类型
 */
export type VcCheckedStatusType =
  (typeof VcCheckedStatusEnum)[keyof typeof VcCheckedStatusEnum]

/**
 * 监管合规风险指标明细表单模型
 */
export interface ComplianceFormModel {
  /** 产品类型 */
  vcFundTypeCode: VcFundTypeType | ""
  /** 产品系列  */
  vcFundSeriesName: string
  /** 产品代码 */
  vcFundCode: string
  /** 投资经理 */
  vcManagerName: string
  /** 指标类型 */
  vcItType: VcItTypeType | ""
  /** 指标编号 */
  vcItCode: string
  /** 指标名称 */
  vcItName: string
  /** 指标状态 */
  vcItStatus: VcItStatusType | ""
  /** 指标处理状态 */
  vcCheckedStatus: VcCheckedStatusType | ""
  /** 查询区间 */
  queryDateRange: [string, string]
  /** 日期开始时间 */
  startDate: string
  /** 日期结束时间 */
  endDate: string
}

/**
 * 监管合规风险指标明细列表项模型
 */
export interface ComplianceTableData {
  /** 产品类型名称 */
  vcFundTypeName: string
  /** 产品系列 */
  vcFundSeriesName: string
  /** 产品代码 */
  vcFundCode: string
  /** 产品名称 */
  vcFundName: string
  /** 投资经理 */
  vcManagerName: string
  /** 指标类型名称 */
  vcItTypeName: string
  /** 指标编号 */
  vcItCode: string
  /** 指标名称 */
  vcItName: string
  /** 限额值 */
  vcOutlineNum: number
  /** 预警值 */
  vcWarnNum: number
  /** 使用值 */
  fDactualMoney: number
  /** 使用比例 */
  fDactualValue: number
  /** 指标状态 */
  vcItStatus: VcItStatusType
  /** 指标处理状态 */
  vcCheckedStatus: VcCheckedStatusType
}

/**
 * 内部管理风险指标明细列表项模型
 */
export interface InternalTableData {
  /** 产品代码 */
  vcFundCode: string
  /** 产品名称 */
  vcFundName: string
  /** 专户代码 */
  vcZhCode: string
  /** 专户名称 */
  vcZhName: string
  /** 投资经理 */
  vcManagerName: string
  /** 投资部门 */
  vcDeptName: string
  /** 日期 */
  dDate: string
  /** 指标编号 */
  vcItCode: string
  /** 指标名称 */
  vcItName: string
  /** 指标标识 */
  vcSymbolCode: string
  /** 指标标识名称 */
  vcSymbolName: string
  /** 限额结果 */
  vcOutlineNum: number
  /** 指标结果 */
  fDactualMoney: number
  /** 指标状态 */
  vcItStatus: VcItStatusType
}

/**
 * 内部管理风险指标明细表单模型
 */
export interface InternalFormModel {
  /** 产品代码 */
  vcFundCode: string
  /** 产品名称 */
  vcFundName: string
  /** 专户代码 */
  vcZhCode: string
  /** 专户名称 */
  vcZhName: string
  /** 投资经理 */
  vcManagerName: string
  /** 投资部门 */
  vcDeptName: string
  /** 指标编号 */
  vcItCode: string
  /** 指标名称 */
  vcItName: string
  /** 指标状态 */
  vcItStatus: VcItStatusType | ""
  /** 查询区间 */
  queryDateRange: [string, string]
  /** 日期开始时间 */
  startDate: string
  /** 日期结束时间 */
  endDate: string
}

/**
 * 偏好类指标明细表单模型
 */
export interface PreferenceFormModel {
  /** 指标编码 */
  indexCode: string
  /** 指标管理部门 */
  indexManageDept: string
  /** 指标状态 */
  vcltStatus: string
  /** 查询区间 */
  queryDateRange: [string, string]
  /** 日期开始时间 */
  startDate: string
  /** 日期结束时间 */
  endDate: string
}

/**
 * 偏好类指标明细列表项模型
 */
export interface PreferenceTableData {
  /** 指标类型名称 */
  vcltTypeName: string
  /** 指标名称 */
  indexName: string
  /** 额度值 */
  tolVal: number
  /** 预警值 */
  earlyWarnVal: number
  /** 指标管理部门 */
  indexManageDept: string
  /** 实际值 */
  actualVal: number
  /** 指标状态 */
  vcltStatus: string
}
