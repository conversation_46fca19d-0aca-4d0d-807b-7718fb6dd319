import { ColorEnum } from "@/constants"
import { RequestMethods } from "@/utils/http/types"

export type PrimitiveType = string | number | boolean | null | undefined
export type Awaitable<T> = T | Promise<T>

/**
 * 请求配置
 */
export interface ServerConfig {
  url?: string
  method?: RequestMethods
  timeout?: number
  headers?: Record<string, string>
  params?: Record<string, PrimitiveType>
  data?: Record<string, PrimitiveType | PrimitiveType[]>
}

/**
 * 布尔值字段映射
 */
export interface BoolFieldMapping {
  field: string
  trueValue?: boolean | number | string
  falseValue?: boolean | number | string
}

/**
 * 颜色映射
 */
export type ColorType = (typeof ColorEnum)[keyof typeof ColorEnum]
