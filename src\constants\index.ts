/**
 * 自定义筛选条件窗口选项卡类型枚举
 */
export const CustomViewCodeEnum = {
  /** 利差视图（静态） */
  VIEW_1: "01",
  /** 利差视图（动态） */
  VIEW_2: "02",
} as const

/**
 * 颜色配置（10种）枚举
 * COLOR_1: #2563eb（蓝），COLOR_2: #22c55e（绿），COLOR_3: #f59e42（橙），COLOR_4: #eab308（黄），COLOR_5: #a855f7（紫），
 * COLOR_6: #ec4899（粉），COLOR_7: #0ea5e9（青），COLOR_8: #64748b（灰蓝），COLOR_9: #f43f5e（红），COLOR_10: #14b8a6（青绿）
 */
export const ColorEnum = {
  COLOR_1: "01", // #2563eb 蓝
  COLOR_2: "02", // #22c55e 绿
  COLOR_3: "03", // #f59e42 橙
  COLOR_4: "04", // #eab308 黄
  COLOR_5: "05", // #a855f7 紫
  COLOR_6: "06", // #ec4899 粉
  COLOR_7: "07", // #0ea5e9 青
  COLOR_8: "08", // #64748b 灰蓝
  COLOR_9: "09", // #f43f5e 红
  COLOR_10: "10", // #14b8a6 青绿
} as const

/* ------------------------------- 颜色配置（10种）配置 ------------------------------ */
/**
 * 颜色配置（10种）配置
 * @description 每种颜色包含四种颜色值：主色、背景色、hover背景色、边框色
 */
export const colorConfig = {
  [ColorEnum.COLOR_1]: ["#2563eb", "#eff6ff", "#dbeafe", "#2563eb"], // 蓝
  [ColorEnum.COLOR_2]: ["#22c55e", "#f0fdf4", "#dcfce7", "#22c55e"], // 绿
  [ColorEnum.COLOR_3]: ["#f59e42", "#fff7ed", "#fed7aa", "#f59e42"], // 橙
  [ColorEnum.COLOR_4]: ["#eab308", "#fefce8", "#fef3c7", "#eab308"], // 黄
  [ColorEnum.COLOR_5]: ["#a855f7", "#f5f3ff", "#e9d5ff", "#a855f7"], // 紫
  [ColorEnum.COLOR_6]: ["#ec4899", "#fdf2f8", "#fce7f3", "#ec4899"], // 粉
  [ColorEnum.COLOR_7]: ["#0ea5e9", "#e0f2fe", "#bae6fd", "#0ea5e9"], // 青
  [ColorEnum.COLOR_8]: ["#64748b", "#f1f5f9", "#e2e8f0", "#64748b"], // 灰蓝
  [ColorEnum.COLOR_9]: ["#f43f5e", "#fef2f2", "#fecaca", "#f43f5e"], // 红
  [ColorEnum.COLOR_10]: ["#14b8a6", "#f0fdfa", "#ccfbf1", "#14b8a6"], // 青绿
}
