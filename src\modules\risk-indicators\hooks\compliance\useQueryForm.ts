import { ComplianceFormModel } from "../../types"

interface QueryFormReturn {
  formModels: Ref<ComplianceFormModel>
}

export function useQueryForm(): QueryFormReturn {
  // const store = useComplianceStore()

  const formModels = ref<ComplianceFormModel>({
    // 产品类型
    vcFundTypeCode: "",
    // 产品系列
    vcFundSeriesName: "",
    // 产品代码
    vcFundCode: "",
    // 投资经理
    vcManagerName: "",
    // 指标类型
    vcItType: "",
    // 指标编号
    vcItCode: "",
    // 指标名称
    vcItName: "",
    // 指标状态
    vcItStatus: "",
    // 指标处理状态
    vcCheckedStatus: "",
    // 日期开始时间
    startDate: "",
    // 日期结束时间
    endDate: "",
  })

  return {
    formModels,
  }
}
