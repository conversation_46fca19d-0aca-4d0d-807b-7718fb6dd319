<script setup lang="ts">
import ModuleIconButton from "@/components/module-icon-button/index.vue"
import {
  colorConfig,
  ColorEnum,
} from "@/constants"
import {
  CustomViewCodeType,
  CustomViewConfig,
} from "@/types"
import { checkTip } from "@/utils"
import { createTemplatePromise } from "@vueuse/core"
import {
  FormInstance,
  FormRules,
  XqMessage,
} from "@xquant/x-ui-plus"
import {
  Check,
  Info,
  MoreFilled,
  Plus,
} from "@xquant/x-ui-plus-icons-vue"

type DialogResult = "ok" | "cancel"
type FormModels = Pick<CustomViewConfig, "name" | "description" | "colorCode" | "id">

defineOptions({
  name: "CustomViewToolbar",
})

const props = withDefaults(
  defineProps<{
    /** 视图编码 */
    code: CustomViewCodeType
    /** 序列化后的表单查询和图表维度设置数据 */
    data: string
  }>(),
  {
    data: "",
  }
)

const EditDialog = createTemplatePromise<DialogResult, [CustomViewCodeType]>()

const formRef = ref<FormInstance>()

/** 当前选中 */
const current = ref<string>("")
/** 视图模板编辑对话框标题 */
const dialogTitle = ref("添加视图模板")

/** 配置数据 */
const options = ref<FormModels[]>([
  {
    name: "视图1",
    id: "01",
    description: "这是视图1的描述",
    colorCode: ColorEnum.COLOR_1,
  },
  {
    name: "视图2",
    id: "02",
    description: "这是视图2的描述",
    colorCode: ColorEnum.COLOR_2,
  },
])

/** 模板更多菜单配置 */
const templateMenuOptions = [
  { label: "编辑当前视图", value: "edit" },
  { label: "删除", value: "delete", danger: true },
]

/** 主操作菜单配置 */
const mainMenuOptions = [
  { label: "添加新的视图", value: "add" },
  { label: "保存当前视图", value: "save" },
  { label: "还原原始视图", value: "reset" },
]

/** 颜色配置 */
const colorOptions = Object.keys(colorConfig).map(key => ({
  label: colorConfig[key][0],
  value: key,
  style: {
    hoverColor: colorConfig[key][2],
    backgroundColor: colorConfig[key][0],
    borderColor: colorConfig[key][3],
  },
}))

const formModels = reactive<FormModels>({
  name: "",
  description: "",
  colorCode: ColorEnum.COLOR_1,
  id: "",
})

const rules = reactive<FormRules<FormModels>>({
  name: [{ required: true, message: "请填写", trigger: "blur" }],
})

/**
 * 打开【视图模板设置】对话框
 */
async function openEditDialog(option: FormModels | null = null) {
  if (option) {
    dialogTitle.value = `编辑视图模板【${option.name}】`
    formModels.name = option?.name || ""
    formModels.description = option?.description || ""
    formModels.colorCode = option?.colorCode || ColorEnum.COLOR_1
    formModels.id = option?.id || ""
  } else {
    dialogTitle.value = "添加视图模板"
  }

  const result = await EditDialog.start(props.code)
  console.log(result, option)
}

/**
 * 【选中】模板
 */
async function handleTemplateSelect(option: FormModels) {
  if (current.value) {
    try {
      await checkTip(`确认切换视图模板【${option.name}】吗？`)
      current.value = option.id
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (err: unknown) {
      // 用户取消操作
      return
    }
  } else {
    current.value = option.id
  }
}

/**
 * 【删除】模板
 */
async function handleTemplateDelete(option: FormModels, index: number) {
  await checkTip(`确认删除该视图模板【${option.name}】吗？`)

  // 在删除当前使用的模板时需要特殊处理
  if (option.id === current.value) {
    current.value = ""
  }

  options.value.splice(index, 1)

  XqMessage.success(`视图模板【${option.name}】已删除`)
}

/**
 * 模板更多菜单项点击处理
 */
function handleTemplateMenuClick(value: string, option: FormModels, index: number) {
  switch (value) {
    case "edit":
      openEditDialog(option)
      break
    case "delete":
      handleTemplateDelete(option, index)
      break
  }
}

/**
 * 主操作菜单项点击处理
 */
function handleMainMenuClick(value: string) {
  switch (value) {
    case "add":
      openEditDialog()
      break
    case "save":
      // TODO: 保存当前视图逻辑
      console.log("保存当前视图")
      break
    case "reset":
      // TODO: 还原原始视图逻辑
      console.log("还原原始视图")
      break
  }
}

/**
 * 表单【提交】处理
 */
function handleFormSubmit(resolve: () => void) {
  if (!formRef.value) return
  formRef.value.validate((valid, fields) => {
    if (valid) {
      // 提交逻辑
      console.log("提交成功", formModels, fields)
      if (formModels.id) {
        // TODO: 调用接口
        options.value = options.value.map(item =>
          item.id === formModels.id ? { ...formModels } : item
        )
      } else {
        // TODO: 调用接口
        // 新增逻辑
        options.value.push({ ...formModels, id: Date.now().toString() })
      }
      resolve()
    } else {
      console.error("表单验证失败", fields)
    }
  })
}
</script>

<template>
  <div class="flex h-[44px] items-center justify-between px-[12px]">
    <template v-if="options.length > 0">
      <div class="flex w-full">
        <template v-for="(option, index) in options" :key="index">
          <span
            :style="{
              color: current === option.id ? 'white' : colorConfig[option.colorCode][0],
              borderColor: colorConfig[option.colorCode][3],
              '--text-color': current === option.id ? 'white' : colorConfig[option.colorCode][0],
              '--bg-color':
                current === option.id
                  ? colorConfig[option.colorCode][0]
                  : colorConfig[option.colorCode][1],
              '--hover-bg-color':
                current === option.id
                  ? colorConfig[option.colorCode][0]
                  : colorConfig[option.colorCode][2],
            }"
            class="flex h-[20px] cursor-pointer flex-row items-center rounded-[4px] border-[1px] border-solid bg-[var(--bg-color)] pr-[6px] pl-[12px] text-[12px] leading-[20px] not-last:mr-2 hover:bg-[var(--hover-bg-color)]"
          >
            <span
              class="border-r-solid border-r-gray-500] border-r-[1px] pr-[12px]"
              @click="handleTemplateSelect(option)"
            >
              {{ option.name }}
            </span>
            <div class="ml-[6px] flex">
              <xq-popover trigger="hover" placement="bottom-start" :width="120">
                <template #reference>
                  <xq-icon>
                    <MoreFilled
                      :style="{
                        '--text-color':
                          current === option.id ? 'white' : colorConfig[option.colorCode][0],
                      }"
                      class="h-[16px] w-[16px] rotate-90 text-[var(--text-color)]"
                    />
                  </xq-icon>
                </template>
                <div class="flex flex-col">
                  <div
                    v-for="menuItem in templateMenuOptions"
                    :key="menuItem.value"
                    :class="[
                      'mx-[-4px] h-[30px] cursor-pointer px-[12px] leading-[30px] hover:bg-[var(--xq-color-primary-light-9)]',
                      menuItem.danger
                        ? 'text-[var(--xq-color-danger)]'
                        : 'text-gray-500 hover:text-[var(--xq-color-primary)]',
                    ]"
                    @click="handleTemplateMenuClick(menuItem.value, option, index)"
                  >
                    {{ menuItem.label }}
                  </div>
                </div>
              </xq-popover>
            </div>
          </span>
        </template>
      </div>
      <ModuleIconButton
        :menu-options="mainMenuOptions"
        :width="120"
        @menu-item-click="handleMainMenuClick"
      />
    </template>
    <template v-else>
      <span class="flex items-center text-[12px] leading-[20px]">
        <xq-icon class="mr-[4px] text-[var(--xq-color-primary)]">
          <Info />
        </xq-icon>
        <span class="text-gray-500">
          视图中的查询条件及图表维度配置可保存为【视图模板】下次直接加载使用。
        </span>
      </span>
      <xq-button @click="() => openEditDialog()">
        <xq-icon class="mr-[4px]"><Plus /></xq-icon>
        保存当前视图
      </xq-button>
    </template>
  </div>

  <EditDialog v-slot="scope">
    <xq-dialog
      width="500"
      :title="dialogTitle"
      :model-value="true"
      :before-close="
        done => {
          if (!scope.isResolving) {
            scope.resolve('cancel')
            done()
          }
        }
      "
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <xq-form
        ref="formRef"
        :model="formModels"
        label-width="80"
        show-message="tooltip"
        :rules="rules"
        @submit.prevent="scope.resolve('ok')"
      >
        <xq-form-item label="视图名称" prop="name">
          <xq-input v-model="formModels.name" placeholder="请输入视图名称" />
        </xq-form-item>
        <xq-form-item label="颜色" prop="colorCode">
          <xq-segmented v-model="formModels.colorCode" size="small" :options="colorOptions" block>
            <template #item="{ item }">
              <xq-tooltip effect="dark" placement="top" :content="item.label" :show-after="200">
                <span
                  :style="{
                    '--bg-color': item.style.backgroundColor,
                  }"
                  class="flex h-[20px] w-[20px] items-center justify-center rounded-[4px] bg-[var(--bg-color)]"
                >
                  <template v-if="formModels.colorCode === item.value">
                    <xq-icon class="h-[12px] w-[12px]">
                      <Check class="text-white" />
                    </xq-icon>
                  </template>
                </span>
              </xq-tooltip>
            </template>
          </xq-segmented>
        </xq-form-item>
        <xq-form-item label="描述" prop="description">
          <xq-input v-model="formModels.description" type="textarea" placeholder="请输入描述" />
        </xq-form-item>
      </xq-form>
      <template #footer>
        <xq-button @click="() => scope.resolve('cancel')">取消</xq-button>
        <xq-button type="primary" @click="() => handleFormSubmit(() => scope.resolve('ok'))">
          确定
        </xq-button>
      </template>
    </xq-dialog>
  </EditDialog>
</template>

<style lang="scss" scoped>
:deep(.#{$ns}-segmented) {
  padding: 0 !important;
}

:deep(.#{$ns}-segmented-item__label) {
  padding: 0 !important;
  height: 20px;
}

:deep(.#{$ns}-segmented-item:not(:last-child)) {
  margin-right: 4px;
}

:deep(.#{$ns}-segmented__group .#{$ns}-segmented-item::after) {
  content: none;
}
</style>
