/**
 * 简化版本的 LargeFileDownloader
 * 使用内联 Worker 代码，避免构建复杂性
 */

/// <reference path="./file-system-api.d.ts" />

import type { DownloadOptions, DownloadResult, DownloadProgress } from "./types"
import {
  isFileSystemAccessSupported,
  getFileInfoFromResponse,
  extractFilenameFromUrl,
  sanitizeFilename,
  calculateProgress,
  isValidUrl,
} from "./utils"

/**
 * 创建内联 Worker
 */
function createInlineWorker(): Worker {
  const workerCode = `
    // Worker 内联代码
    let currentDownload = null;

    function postProgressMessage(type, data) {
      self.postMessage({ type, data });
    }

    async function delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }

    async function downloadWithRetry(url, headers, signal, maxRetries = 3) {
      let attempt = 0;

      while (attempt < maxRetries) {
        try {
          const response = await fetch(url, { headers, signal });

          if (!response.ok) {
            throw new Error('HTTP ' + response.status + ': ' + response.statusText);
          }

          if (!response.body) {
            throw new Error('响应体为空');
          }

          const reader = response.body.getReader();
          let downloaded = 0;
          const startTime = Date.now();

          while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            if (signal.aborted) {
              throw new Error('下载已取消');
            }

            downloaded += value.length;

            // 发送数据块到主线程
            self.postMessage({
              type: 'chunk',
              data: {
                chunk: value,
                downloaded,
                startTime
              }
            });
          }

          self.postMessage({
            type: 'complete',
            data: { downloaded }
          });

          return;

        } catch (error) {
          attempt++;

          if (signal.aborted) {
            throw new Error('下载已取消');
          }

          if (attempt >= maxRetries) {
            throw error;
          }

          // 等待后重试
          const delayTime = Math.min(1000 * Math.pow(2, attempt), 10000);
          await delay(delayTime);
        }
      }
    }

    self.addEventListener('message', async (event) => {
      const { type, data } = event.data;

      switch (type) {
        case 'start': {
          const { url, headers = {}, maxRetries = 3 } = data;

          currentDownload = {
            abortController: new AbortController()
          };

          postProgressMessage('progress', { status: 'starting' });

          try {
            await downloadWithRetry(
              url,
              headers,
              currentDownload.abortController.signal,
              maxRetries
            );
          } catch (error) {
            self.postMessage({
              type: 'error',
              error: error.message
            });
          }
          break;
        }

        case 'cancel': {
          if (currentDownload) {
            currentDownload.abortController.abort();
            currentDownload = null;
            postProgressMessage('progress', { status: 'cancelled' });
          }
          break;
        }
      }
    });

    // Worker 初始化完成
    postProgressMessage('progress', { status: 'ready' });
  `

  const blob = new Blob([workerCode], { type: "application/javascript" })
  return new Worker(URL.createObjectURL(blob))
}

export class SimpleFileDownloader {
  private worker: Worker | null = null
  private currentDownload: {
    url: string
    filename: string
    fileHandle: FileSystemFileHandle | null
    writable: FileSystemWritableFileStream | null
    startTime: number
    options: DownloadOptions
    totalSize?: number
  } | null = null

  constructor() {
    this.initWorker()
  }

  /**
   * 初始化 Web Worker
   */
  private initWorker() {
    try {
      this.worker = createInlineWorker()

      this.worker.onmessage = this.handleWorkerMessage.bind(this)
      this.worker.onerror = error => {
        console.error("Worker error:", error)
        this.currentDownload?.options.onError?.(new Error("Worker 执行错误"))
      }
    } catch (error) {
      console.error("Failed to initialize worker:", error)
    }
  }

  /**
   * 处理 Worker 消息
   */
  private async handleWorkerMessage(event: MessageEvent) {
    const { type, data, error } = event.data

    if (!this.currentDownload) return

    switch (type) {
      case "progress":
        // Worker 状态更新
        break

      case "chunk":
        await this.handleChunk(data)
        break

      case "complete":
        await this.handleComplete()
        break

      case "error":
        this.handleError(new Error(error || "Unknown error"))
        break
    }
  }

  /**
   * 处理接收到的数据块
   */
  private async handleChunk(data: any) {
    if (!this.currentDownload) return

    const { chunk, downloaded, startTime } = data

    try {
      if (this.currentDownload.writable) {
        await this.currentDownload.writable.write(chunk)

        // 更新进度
        const progress = calculateProgress(
          downloaded,
          this.currentDownload.totalSize,
          startTime || this.currentDownload.startTime
        )

        this.currentDownload.options.onProgress?.(progress)
      }
    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 处理下载完成
   */
  private async handleComplete() {
    if (!this.currentDownload) return

    try {
      // 关闭文件流
      if (this.currentDownload.writable) {
        await this.currentDownload.writable.close()
      }

      // 最终进度更新
      const finalProgress: DownloadProgress = {
        downloaded: this.currentDownload.totalSize || 0,
        total: this.currentDownload.totalSize,
        percentage: 100,
        speed: 0,
      }

      this.currentDownload.options.onProgress?.(finalProgress)
    } catch (error) {
      this.handleError(error as Error)
    } finally {
      this.cleanup()
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error) {
    this.currentDownload?.options.onError?.(error)
    this.cleanup()
  }

  /**
   * 清理资源
   */
  private cleanup() {
    if (this.currentDownload?.writable) {
      try {
        this.currentDownload.writable.abort()
      } catch {
        // 忽略关闭错误
      }
    }
    this.currentDownload = null
  }

  /**
   * 下载文件
   */
  async download(
    url: string,
    options: DownloadOptions = {}
  ): Promise<DownloadResult> {
    try {
      // 验证输入
      if (!isValidUrl(url)) {
        throw new Error("无效的 URL")
      }

      if (!isFileSystemAccessSupported()) {
        throw new Error("浏览器不支持 File System Access API，请使用现代浏览器")
      }

      if (!this.worker) {
        throw new Error("Worker 未初始化")
      }

      // 获取文件信息
      const fileInfo = await getFileInfoFromResponse(url, options.headers)

      // 确定文件名
      let filename = options.suggestedName
      if (!filename && options.autoDetectFilename !== false) {
        filename = fileInfo.filename || extractFilenameFromUrl(url)
      }
      if (!filename) {
        filename = `download_${Date.now()}`
      }

      filename = sanitizeFilename(filename)

      // 设置文件类型过滤器
      const types = options.types || [
        {
          description: "All files",
          accept: { "*/*": [] },
        },
      ]

      // 显示保存对话框
      const fileHandle = await window.showSaveFilePicker({
        suggestedName: filename,
        types,
      })

      // 创建可写流
      const writable = await fileHandle.createWritableFileStream()

      // 初始化下载上下文
      this.currentDownload = {
        url,
        filename,
        fileHandle,
        writable,
        startTime: Date.now(),
        options,
        totalSize: fileInfo.size,
      }

      // 启动 Worker 下载
      this.worker.postMessage({
        type: "start",
        data: {
          url,
          filename,
          headers: options.headers || {},
          maxRetries: options.maxRetries || 3,
          totalSize: fileInfo.size,
        },
      })

      return new Promise((resolve, reject) => {
        const originalOnError = options.onError
        const originalOnProgress = options.onProgress

        // 重写回调以处理 Promise 解析
        this.currentDownload!.options.onError = error => {
          originalOnError?.(error)
          reject(error)
        }

        this.currentDownload!.options.onProgress = progress => {
          originalOnProgress?.(progress)

          if (progress.percentage >= 100) {
            resolve({
              success: true,
              fileHandle,
              filename,
              fileSize: fileInfo.size,
            })
          }
        }
      })
    } catch (error) {
      this.cleanup()

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 取消下载
   */
  cancel() {
    if (this.worker && this.currentDownload) {
      this.worker.postMessage({ type: "cancel" })
      this.cleanup()
    }
  }

  /**
   * 销毁下载器
   */
  destroy() {
    this.cancel()

    if (this.worker) {
      this.worker.terminate()
      this.worker = null
    }
  }
}

/**
 * 创建下载器实例（单例模式）
 */
let downloaderInstance: SimpleFileDownloader | null = null

export function createDownloader(): SimpleFileDownloader {
  if (!downloaderInstance) {
    downloaderInstance = new SimpleFileDownloader()
  }
  return downloaderInstance
}

/**
 * 便捷的下载函数
 */
export async function downloadFile(
  url: string,
  filename?: string,
  options?: Partial<DownloadOptions>
): Promise<DownloadResult> {
  const downloader = createDownloader()

  return downloader.download(url, {
    suggestedName: filename,
    ...options,
  })
}

// 导出类型
export type { DownloadOptions, DownloadResult, DownloadProgress } from "./types"
