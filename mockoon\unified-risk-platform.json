{"uuid": "5c3c957d-9740-4622-b6e0-81c94b8f5a5e", "lastMigration": 33, "name": "Unified Risk Platform", "endpointPrefix": "", "latency": 0, "port": 8888, "hostname": "", "folders": [], "routes": [{"uuid": "f5247e1d-242a-4559-b966-7c6c79fe90e2", "type": "http", "documentation": "User login API", "method": "post", "endpoint": "login", "responses": [{"uuid": "42e5b1c8-f5b5-448d-8d6a-33b0268b741a", "body": "{{data 'login-response'}}", "latency": 200, "statusCode": 200, "label": "Login Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "4f22f5d8-cc30-475f-b116-77805fc55a13", "type": "http", "documentation": "Refresh token API", "method": "post", "endpoint": "refreshToken", "responses": [{"uuid": "fc9073f0-800b-44a8-a82a-558712da2314", "body": "{\"success\": true, \"data\": {\"accessToken\": \"eyJhbGciOiJIUzUxMiJ9.refreshed\", \"refreshToken\": \"eyJhbGciOiJIUzUxMiJ9.newRefresh\", \"expires\": \"2025/12/30 00:00:00\"}}", "latency": 100, "statusCode": 200, "label": "Refresh Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "4e45d087-5cc9-4728-bb52-6941c12e94b0", "type": "http", "documentation": "Get user permissions API", "method": "get", "endpoint": "user/permissions", "responses": [{"uuid": "74e416c7-29e8-49a0-940e-bd4a6599856a", "body": "{{data 'permissions'}}", "latency": 150, "statusCode": 200, "label": "Get Permissions Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "45697fba-2265-4a89-b02d-bba025c444fa", "type": "http", "documentation": "Get async routes API", "method": "get", "endpoint": "getAsyncRoutes", "responses": [{"uuid": "19e07ccc-2f28-4bb1-80bd-43c743e93f3b", "body": "{{data 'async-routes'}}", "latency": 200, "statusCode": 200, "label": "Get Routes Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "9112cbf3-cdbe-489b-bcd8-1e6dd85a170d", "type": "http", "documentation": "Compliance risk indicators list", "method": "get", "endpoint": "api/risk-indicators/compliance/list", "responses": [{"uuid": "2d85b5ec-1167-42c6-981e-a66eacc3fbba", "body": "{{data 'compliance-list'}}", "latency": 300, "statusCode": 200, "label": "Get Compliance Indicators Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "6e56f939-c5a4-46c8-be11-92f677121bd4", "type": "http", "documentation": "Internal risk indicators list", "method": "get", "endpoint": "api/risk-indicators/internal/list", "responses": [{"uuid": "45d74e27-7b65-43c0-b7a5-164cdb6cc409", "body": "{{data 'internal-list'}}", "latency": 300, "statusCode": 200, "label": "Get Internal Indicators Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "f7a5f949-3df8-44ce-90b0-8e372ddf9a14", "type": "http", "documentation": "Risk preference indicators list", "method": "get", "endpoint": "api/risk-indicators/preference/list", "responses": [{"uuid": "4f507dbc-8049-476c-9c91-d2be7ed07277", "body": "{{data 'preference-list'}}", "latency": 300, "statusCode": 200, "label": "Get Preference Indicators Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "cd639ac3-fb11-42cd-93a7-aec58eb98f4d", "type": "http", "documentation": "Get spread analysis views list", "method": "get", "endpoint": "api/spread-analysis/views", "responses": [{"uuid": "9d652f92-8c39-48f8-9c2c-85d5e6e86ee3", "body": "{{data 'spread-analysis-views'}}", "latency": 200, "statusCode": 200, "label": "Get Views List Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "e4d0100e-3381-4548-9d3a-c26543cb7d2c", "type": "http", "documentation": "Create spread analysis view", "method": "post", "endpoint": "api/spread-analysis/views", "responses": [{"uuid": "d15164a9-c8b3-4de1-9ce8-17faa7f99e72", "body": "{\"success\": true, \"data\": {\"id\": \"{{faker 'datatype.uuid'}}\", \"message\": \"View created successfully\"}}", "latency": 400, "statusCode": 200, "label": "Create View Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "0531e4be-1645-4ff3-87f1-6ecb7fd579cd", "type": "http", "documentation": "Delete spread analysis view", "method": "delete", "endpoint": "api/spread-analysis/views/*", "responses": [{"uuid": "334aa7c1-6379-4fa1-b27c-ac3e064398f7", "body": "{\"success\": true, \"data\": {\"message\": \"View deleted successfully\"}}", "latency": 300, "statusCode": 200, "label": "Delete View Success", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "f5247e1d-242a-4559-b966-7c6c79fe90e2"}, {"type": "route", "uuid": "4f22f5d8-cc30-475f-b116-77805fc55a13"}, {"type": "route", "uuid": "4e45d087-5cc9-4728-bb52-6941c12e94b0"}, {"type": "route", "uuid": "45697fba-2265-4a89-b02d-bba025c444fa"}, {"type": "route", "uuid": "9112cbf3-cdbe-489b-bcd8-1e6dd85a170d"}, {"type": "route", "uuid": "6e56f939-c5a4-46c8-be11-92f677121bd4"}, {"type": "route", "uuid": "f7a5f949-3df8-44ce-90b0-8e372ddf9a14"}, {"type": "route", "uuid": "cd639ac3-fb11-42cd-93a7-aec58eb98f4d"}, {"type": "route", "uuid": "e4d0100e-3381-4548-9d3a-c26543cb7d2c"}, {"type": "route", "uuid": "0531e4be-1645-4ff3-87f1-6ecb7fd579cd"}], "proxyMode": false, "proxyHost": "", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [{"uuid": "ffb1389d-ceea-411e-8832-3d8fa70e6c76", "id": "login-response", "name": "Login Response", "documentation": "Login response data", "value": "{\"success\": true, \"data\": {\"username\": \"admin\", \"roles\": [\"admin\"], \"accessToken\": \"eyJhbGciOiJIUzUxMiJ9.admin\", \"refreshToken\": \"eyJhbGciOiJIUzUxMiJ9.adminRefresh\", \"expires\": \"2025/12/30 00:00:00\"}}"}, {"uuid": "e96884a7-7cbc-4536-886a-e38b9a966455", "id": "permissions", "name": "Permissions", "documentation": "User permissions data", "value": "{\"success\": true, \"data\": [\"btn_add\", \"btn_edit\", \"btn_delete\", \"btn_export\", \"btn_import\", \"btn_view\", \"btn_audit\", \"btn_approve\"]}"}, {"uuid": "00b30248-7dc4-4a64-b3e0-6f6432c6b1d4", "id": "async-routes", "name": "Async Routes", "documentation": "Async routes data", "value": "{\"success\": true, \"data\": [{\"path\": \"/permission\", \"meta\": {\"title\": \"Permission Management\", \"icon\": \"xq-icon-dashboard\", \"rank\": 10}, \"children\": [{\"path\": \"/permission/button/index\", \"name\": \"PermissionButton\", \"meta\": {\"title\": \"Button Permission\", \"roles\": [\"admin\", \"common\"], \"auths\": [\"btn_add\", \"btn_edit\", \"btn_delete\"]}}]}, {\"path\": \"/spread-analysis\", \"meta\": {\"title\": \"Spread Analysis\", \"icon\": \"xq-icon-chart\", \"rank\": 20}, \"children\": [{\"path\": \"/spread-analysis/index\", \"name\": \"SpreadAnalysis\", \"meta\": {\"title\": \"Spread Analysis\", \"roles\": [\"admin\", \"common\"]}}]}, {\"path\": \"/risk-indicators\", \"meta\": {\"title\": \"Risk Indicators\", \"icon\": \"xq-icon-warning\", \"rank\": 30}, \"children\": [{\"path\": \"/risk-indicators/compliance\", \"name\": \"RiskIndicatorsCompliance\", \"meta\": {\"title\": \"Compliance Risk Indicators\", \"roles\": [\"admin\", \"common\"]}}, {\"path\": \"/risk-indicators/internal\", \"name\": \"RiskIndicatorsInternal\", \"meta\": {\"title\": \"Internal Risk Indicators\", \"roles\": [\"admin\", \"common\"]}}, {\"path\": \"/risk-indicators/preference\", \"name\": \"RiskIndicatorsPreference\", \"meta\": {\"title\": \"Risk Preference Indicators\", \"roles\": [\"admin\", \"common\"]}}]}]}"}, {"uuid": "07b7127e-73f1-4682-93dc-1def0ee6dd5a", "id": "compliance-list", "name": "Compliance List", "documentation": "Compliance risk indicators data", "value": "{\"success\": true, \"data\": {\"list\": [{\"id\": \"1\", \"productCode\": \"FUND001\", \"productName\": \"Stable Growth Fund\", \"productType\": \"1\", \"indicatorType\": \"CCBL\", \"indicatorCode\": \"CCBL_001\", \"indicatorName\": \"Single Stock Investment Ratio\", \"indicatorValue\": 8.5, \"thresholdValue\": 10.0, \"indicatorStatus\": \"Compliant\", \"processingStatus\": \"Approved\", \"riskLevel\": \"Low\", \"reportDate\": \"2024-01-15\", \"updateTime\": \"2024-01-15 09:30:00\", \"remark\": \"Within normal range\"}, {\"id\": \"2\", \"productCode\": \"FUND002\", \"productName\": \"Aggressive Growth Fund\", \"productType\": \"2\", \"indicatorType\": \"TZBL\", \"indicatorCode\": \"TZBL_002\", \"indicatorName\": \"Stock Investment Ratio\", \"indicatorValue\": 85.2, \"thresholdValue\": 80.0, \"indicatorStatus\": \"Warning\", \"processingStatus\": \"Pending Review\", \"riskLevel\": \"Medium\", \"reportDate\": \"2024-01-15\", \"updateTime\": \"2024-01-15 10:15:00\", \"remark\": \"Close to limit\"}, {\"id\": \"3\", \"productCode\": \"FUND003\", \"productName\": \"Balanced Fund\", \"productType\": \"4\", \"indicatorType\": \"ZJBL\", \"indicatorCode\": \"ZJBL_003\", \"indicatorName\": \"Bond Investment Ratio\", \"indicatorValue\": 65.8, \"thresholdValue\": 60.0, \"indicatorStatus\": \"Non-compliant\", \"processingStatus\": \"Rejected\", \"riskLevel\": \"High\", \"reportDate\": \"2024-01-15\", \"updateTime\": \"2024-01-15 11:00:00\", \"remark\": \"Exceeds limit\"}], \"total\": 3, \"pageNum\": 1, \"pageSize\": 10}}"}, {"uuid": "b2c2dccf-dec2-4f47-81a0-f0845b2480fc", "id": "internal-list", "name": "Internal List", "documentation": "Internal risk indicators data", "value": "{\"code\": 200, \"message\": \"success\", \"data\": {\"list\": [{\"id\": \"1\", \"vcFundCode\": \"FUND001\", \"vcFundName\": \"稳健增长基金\", \"vcZhCode\": \"ZH001\", \"vcZhName\": \"专户001\", \"vcManagerName\": \"张经理\", \"vcDeptName\": \"权益投资部\", \"dDate\": \"2024-01-15\", \"vcItCode\": \"MUTEX_001\", \"vcItName\": \"关联方交易比例\", \"vcSymbolCode\": \"000001\", \"vcSymbolName\": \"平安银行\", \"vcOutlineNum\": \"5.00%\", \"fDactualMoney\": 3.2, \"vcItStatus\": \"合规\"}, {\"id\": \"2\", \"vcFundCode\": \"FUND002\", \"vcFundName\": \"积极成长基金\", \"vcZhCode\": \"ZH002\", \"vcZhName\": \"专户002\", \"vcManagerName\": \"李经理\", \"vcDeptName\": \"固收投资部\", \"dDate\": \"2024-01-15\", \"vcItCode\": \"CCBL_002\", \"vcItName\": \"集中度风险\", \"vcSymbolCode\": \"000002\", \"vcSymbolName\": \"万科A\", \"vcOutlineNum\": \"15.00%\", \"fDactualMoney\": 12.5, \"vcItStatus\": \"预警\"}, {\"id\": \"3\", \"vcFundCode\": \"FUND003\", \"vcFundName\": \"平衡配置基金\", \"vcZhCode\": \"ZH003\", \"vcZhName\": \"专户003\", \"vcManagerName\": \"王经理\", \"vcDeptName\": \"混合投资部\", \"dDate\": \"2024-01-15\", \"vcItCode\": \"TZBL_003\", \"vcItName\": \"单一股票投资比例\", \"vcSymbolCode\": \"000858\", \"vcSymbolName\": \"五粮液\", \"vcOutlineNum\": \"10.00%\", \"fDactualMoney\": 11.8, \"vcItStatus\": \"违规\"}], \"total\": 3, \"page\": 1, \"pageSize\": 10, \"totalPages\": 1}}"}, {"uuid": "8387a26b-6dfd-453c-baaf-fe567b21ab59", "id": "preference-list", "name": "Preference List", "documentation": "Risk preference indicators data", "value": "{\"success\": true, \"data\": {\"list\": [{\"id\": \"1\", \"productCode\": \"FUND001\", \"productName\": \"Stable Growth Fund\", \"productType\": \"1\", \"indicatorType\": \"CCBL\", \"indicatorCode\": \"CCBL_001\", \"indicatorName\": \"Volatility Control\", \"indicatorValue\": 8.5, \"thresholdValue\": 10.0, \"indicatorStatus\": \"Compliant\", \"processingStatus\": \"Approved\", \"riskLevel\": \"Low\", \"reportDate\": \"2024-01-15\", \"updateTime\": \"2024-01-15 09:30:00\", \"remark\": \"Risk under control\"}], \"total\": 1, \"pageNum\": 1, \"pageSize\": 10}}"}, {"uuid": "1a7cc9ac-d873-4f94-a1d8-ce700d322789", "id": "spread-analysis-views", "name": "Spread Analysis Views", "documentation": "Spread analysis views data", "value": "{\"success\": true, \"data\": [{\"id\": \"view1\", \"name\": \"Credit Spread Analysis\", \"type\": \"credit_spread\", \"description\": \"Analyze spread changes between corporate and government bonds\", \"config\": {\"timeRange\": \"1Y\", \"bondTypes\": [\"corporate\", \"government\"], \"ratingFilter\": [\"AAA\", \"AA+\", \"AA\"]}}, {\"id\": \"view2\", \"name\": \"Term Spread Analysis\", \"type\": \"term_spread\", \"description\": \"Analyze spread structure across different maturities\", \"config\": {\"timeRange\": \"6M\", \"terms\": [\"1Y\", \"3Y\", \"5Y\", \"10Y\"], \"bondType\": \"government\"}}]}"}], "callbacks": []}