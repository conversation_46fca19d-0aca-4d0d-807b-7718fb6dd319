<script lang="ts" setup>
defineOptions({
  name: "ModuleLoading",
})

const isLoading = defineModel<boolean>()
const { height = 20 } = defineProps<{
  /** 加载区域高度 */
  height?: number
}>()
</script>

<template>
  <div :class="`w-full h-[${height}px] relative flex items-center justify-center px-[12px]`">
    <loading
      v-model:active="isLoading"
      loader="dots"
      color="var(--xq-color-primary)"
      :is-full-page="false"
      :height="height"
    />
  </div>

  <!-- 用于解决 tailwindcss 动态样式未生成问题 -->
  <div :class="`hidden h-[${height}px]`" />
</template>
