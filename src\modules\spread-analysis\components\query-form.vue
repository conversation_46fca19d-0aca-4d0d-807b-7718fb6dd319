<script setup lang="ts">
import { DArrowDown, DArrowUp, Upload } from "@xquant/x-ui-plus-icons-vue"

import { exerciseDeadlineOptions, issueMethodOptions, SpreadPageTypeEnum } from "../consts"
import { useQueryForm } from "../hooks/useQueryForm"
import { SpreadPageType } from "../types"

defineOptions({
  name: "SpreadAnalysisQueryForm",
})

const { page = SpreadPageTypeEnum.STATIC } = defineProps<{
  /** 页面类型，“静态” 或 “动态” 视图 */
  page?: SpreadPageType
}>()

const { formModels } = useQueryForm()

/** 筛选展开状态 */
const isCollapsed = ref(true)

const shortcuts = [
  {
    text: "Today",
    value: new Date(),
  },
  {
    text: "Yesterday",
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return date
    },
  },
  {
    text: "A week ago",
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      return date
    },
  },
]

/**
 * 处理筛选表单提交
 */
function handleFinish() {
  // 处理筛选表单提交逻辑
  console.log("筛选表单已提交", formModels)
}

/**
 * 【查询日期】禁用日期处理函数
 */
function disabledQueryDataDateHandler(date: Date) {
  // 选择范围：
  // 1. 前一日至三年前，若超出则报错并提示“不可选当日”或“仅可选择历史三年”
  // 2. 仅可选择交易日，若超出则报错并提示“请选择工作日”
  const today = new Date()
  const threeYearsAgo = new Date(today)
  threeYearsAgo.setFullYear(today.getFullYear() - 3)

  // 检查是否为工作日（周一至周五）
  const day = date.getDay()
  if (day === 0 || day === 6) {
    return true // 禁止选择周末
  }

  // 检查是否在三年前的范围内
  return date < threeYearsAgo || date > today || date.toDateString() === today.toDateString()
}
</script>

<template>
  <xq-form-query
    label-width="70"
    :collapsed="isCollapsed"
    :model="formModels"
    :form-layout="{ gutter: 20, span: 6 }"
    :default-rows-number="2"
    submitter-fixed
    @finish="handleFinish"
  >
    <template #toggle-button>
      <xq-button
        :icon="isCollapsed ? DArrowDown : DArrowUp"
        type="primary"
        size="small"
        @click="isCollapsed = !isCollapsed"
      >
        {{ isCollapsed ? "展开筛选" : "收起筛选" }}
      </xq-button>
    </template>
    <!-- /* ---------------------------------- 内部行业 ---------------------------------- */ -->
    <xq-form-layout-item label="内部行业">
      <div class="flex items-center justify-between w-full">
        <xq-select v-model="formModels.industry" class="flex-1" clearable></xq-select>
        <xq-button type="info" size="small" class="ml-[-1px]" plain :icon="Upload" />
      </div>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 债券代码 ---------------------------------- */ -->
    <xq-form-layout-item label="债券代码">
      <div class="flex items-center justify-between w-full">
        <xq-select
          v-model="formModels.bondCode"
          class="flex-1"
          multiple
          clearable
          disabled
          collapse-tags
          collapse-tags-tooltip
        ></xq-select>
        <xq-button type="info" size="small" class="ml-[-1px]" plain :icon="Upload" />
      </div>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 债券简称 ---------------------------------- */ -->
    <xq-form-layout-item label="债券简称">
      <div class="flex items-center justify-between w-full">
        <xq-select
          v-model="formModels.bondName"
          class="flex-1"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
        ></xq-select>
        <xq-button type="info" size="small" class="ml-[-1px]" plain :icon="Upload" />
      </div>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 债券品种 ---------------------------------- */ -->
    <xq-form-layout-item label="债券品种">
      <div class="flex items-center justify-between w-full">
        <xq-select
          v-model="formModels.bondType"
          class="flex-1"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
        ></xq-select>
        <xq-button type="info" size="small" class="ml-[-1px]" plain :icon="Upload" />
      </div>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 额度占用方 --------------------------------- */ -->
    <xq-form-layout-item label="额度占用方">
      <div class="flex items-center justify-between w-full">
        <xq-select
          v-model="formModels.quotaOccupy"
          class="flex-1"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
        ></xq-select>
        <xq-button type="info" size="small" class="ml-[-1px]" plain :icon="Upload" />
      </div>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 发行方式 ---------------------------------- */ -->
    <xq-form-layout-item label="发行方式">
      <xq-select v-model="formModels.issueMethod" class="flex-1" clearable>
        <xq-option
          v-for="option in issueMethodOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 行权期限 ---------------------------------- */ -->
    <xq-form-layout-item label="行权期限">
      <xq-select v-model="formModels.exerciseDeadline" class="flex-1" clearable>
        <xq-option
          v-for="option in exerciseDeadlineOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 企业性质 ---------------------------------- */ -->
    <xq-form-layout-item label="企业性质">
      <xq-select v-model="formModels.enterpriseNature" class="flex-1" clearable>
        <xq-option
          v-for="option in exerciseDeadlineOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 内部评级 ---------------------------------- */ -->
    <xq-form-layout-item label="内部评级">
      <xq-select v-model="formModels.internalRating" class="flex-1" clearable>
        <xq-option
          v-for="option in exerciseDeadlineOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <!-- /* ---------------------------------- 个性化标签 --------------------------------- */ -->
    <xq-form-layout-item label="个性化标签">
      <xq-select v-model="formModels.personalizedTags" class="flex-1" clearable>
        <xq-option
          v-for="option in exerciseDeadlineOptions"
          :key="option.value"
          :value="option.value"
          :label="option.label"
        />
      </xq-select>
    </xq-form-layout-item>

    <template v-if="page === SpreadPageTypeEnum.STATIC">
      <!-- /* ---------------------------------- 利差区间 ---------------------------------- */ -->
      <xq-form-layout-item label="利差区间">
        <div class="flex items-center w-full space-x-2">
          <xq-input
            v-model="formModels.interestMarginRangeStart"
            type="number"
            placeholder="最低值"
            class="flex-1"
            min="0"
          />
          <span>至</span>
          <xq-input
            v-model="formModels.interestMarginRangeEnd"
            type="number"
            placeholder="最高值"
            class="flex-1"
            min="0"
          />
        </div>
      </xq-form-layout-item>

      <!-- /* ---------------------------------- 查询日期 ---------------------------------- */ -->
      <xq-form-layout-item label="查询日期">
        <xq-date-picker
          v-model="formModels.queryDate"
          class="flex-1"
          :disabled-date="disabledQueryDataDateHandler"
          clearable
        />
      </xq-form-layout-item>
    </template>

    <template v-else>
      <!-- /* ---------------------------------- 查询区间 ---------------------------------- */ -->
      <xq-form-layout-item label="查询区间">
        <xq-date-picker
          v-model="formModels.queryDateRange"
          type="monthrange"
          unlink-panels
          range-separator="至"
          start-placeholder="Start day"
          end-placeholder="End day"
          :shortcuts="shortcuts"
        />
      </xq-form-layout-item>
    </template>
  </xq-form-query>
</template>
