import type { InternalFormModel, InternalTableData } from "@/modules/risk-indicators/types"

// 内部管理风险指标明细模块
export const createInternalRoutes = (server: any) => {
  // 获取内部管理风险指标明细列表
  server.get("/api/risk-indicators/internal/list", (schema, request) => {
    const queryParams = request.queryParams as Partial<InternalFormModel> & {
      page?: string
      pageSize?: string
    }

    // 模拟分页数据
    const page = parseInt(queryParams.page || "1")
    const pageSize = parseInt(queryParams.pageSize || "10")
    
    // 生成模拟数据
    const mockData: InternalTableData[] = Array.from({ length: 60 }, (_, index) => ({
      vcFundCode: `FUND${String(index + 1).padStart(4, "0")}`,
      vcFundName: `产品名称${index + 1}`,
      vcZhCode: `ZH${String(index + 1).padStart(4, "0")}`,
      vcZhName: `专户名称${index + 1}`,
      vcManagerName: `投资经理${index + 1}`,
      vcDeptName: `投资部门${index + 1}`,
      dDate: new Date(2024, 0, (index % 30) + 1).toISOString().split('T')[0],
      vcItCode: `IT${String(index + 1).padStart(6, "0")}`,
      vcItName: `内部风险指标${index + 1}`,
      vcSymbolCode: `SYM${String(index + 1).padStart(4, "0")}`,
      vcSymbolName: `指标标识${index + 1}`,
      vcOutlineNum: Math.floor(Math.random() * 1000000) + 100000,
      fDactualMoney: Math.floor(Math.random() * 900000) + 90000,
      vcItStatus: ["合规", "预警", "违规", "手动合规"][index % 4] as any,
    }))

    // 简单的筛选逻辑
    let filteredData = mockData
    
    if (queryParams.vcFundCode) {
      filteredData = filteredData.filter(item => 
        item.vcFundCode.toLowerCase().includes(queryParams.vcFundCode!.toLowerCase())
      )
    }
    
    if (queryParams.vcFundName) {
      filteredData = filteredData.filter(item => 
        item.vcFundName.toLowerCase().includes(queryParams.vcFundName!.toLowerCase())
      )
    }
    
    if (queryParams.vcZhCode) {
      filteredData = filteredData.filter(item => 
        item.vcZhCode.toLowerCase().includes(queryParams.vcZhCode!.toLowerCase())
      )
    }
    
    if (queryParams.vcZhName) {
      filteredData = filteredData.filter(item => 
        item.vcZhName.toLowerCase().includes(queryParams.vcZhName!.toLowerCase())
      )
    }
    
    if (queryParams.vcManagerName) {
      filteredData = filteredData.filter(item => 
        item.vcManagerName.toLowerCase().includes(queryParams.vcManagerName!.toLowerCase())
      )
    }
    
    if (queryParams.vcDeptName) {
      filteredData = filteredData.filter(item => 
        item.vcDeptName.toLowerCase().includes(queryParams.vcDeptName!.toLowerCase())
      )
    }
    
    if (queryParams.vcItCode) {
      filteredData = filteredData.filter(item => 
        item.vcItCode.toLowerCase().includes(queryParams.vcItCode!.toLowerCase())
      )
    }
    
    if (queryParams.vcItName) {
      filteredData = filteredData.filter(item => 
        item.vcItName.toLowerCase().includes(queryParams.vcItName!.toLowerCase())
      )
    }
    
    if (queryParams.vcItStatus) {
      filteredData = filteredData.filter(item => item.vcItStatus === queryParams.vcItStatus)
    }

    // 日期筛选
    if (queryParams.startDate) {
      filteredData = filteredData.filter(item => item.dDate >= queryParams.startDate!)
    }
    
    if (queryParams.endDate) {
      filteredData = filteredData.filter(item => item.dDate <= queryParams.endDate!)
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)

    return {
      code: 200,
      message: "success",
      data: {
        list: paginatedData,
        total: filteredData.length,
        page,
        pageSize,
        totalPages: Math.ceil(filteredData.length / pageSize),
      },
    }
  })

  // 导出内部管理风险指标明细
  server.post("/api/risk-indicators/internal/export", (schema, request) => {
    const requestBody = JSON.parse(request.requestBody) as Partial<InternalFormModel>
    
    // 模拟导出成功响应
    return {
      code: 200,
      message: "导出成功",
      data: {
        downloadUrl: "/api/files/internal-export.xlsx",
        fileName: `内部管理风险指标明细_${new Date().toISOString().split('T')[0]}.xlsx`,
      },
    }
  })

  // 获取内部管理风险指标明细详情
  server.get("/api/risk-indicators/internal/:id", (schema, request) => {
    const id = request.params.id
    
    // 模拟详情数据
    const mockDetail: InternalTableData = {
      vcFundCode: "FUND0001",
      vcFundName: "产品名称A",
      vcZhCode: "ZH0001",
      vcZhName: "专户名称A",
      vcManagerName: "李四",
      vcDeptName: "权益投资部",
      dDate: "2024-01-15",
      vcItCode: "IT000001",
      vcItName: "单一股票投资比例",
      vcSymbolCode: "SYM0001",
      vcSymbolName: "股票投资比例",
      vcOutlineNum: 500000,
      fDactualMoney: 350000,
      vcItStatus: "合规",
    }

    return {
      code: 200,
      message: "success",
      data: mockDetail,
    }
  })
}
