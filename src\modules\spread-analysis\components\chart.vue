<script setup lang="ts">
import {
  nextTick,
  onMounted,
  onUnmounted,
  ref,
  watch,
} from "vue"

import { LineChart } from "echarts/charts"
import {
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
} from "echarts/components"
import { use } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import VChart from "vue-echarts"

import type {
  AsyncFileStatus,
  DownloadProgress,
} from "@/utils/file-download"
import {
  downloadAsyncFile,
  downloadFile,
  isFileSystemAccessSupported,
} from "@/utils/file-download"
import {
  useFullscreen,
  useResizeObserver,
} from "@vueuse/core"
import { XqMessage } from "@xquant/x-ui-plus"
import {
  Download,
  FullScreen,
  FullScreenExit,
  History,
  Refresh,
} from "@xquant/x-ui-plus-icons-vue"

import { SpreadPageTypeEnum } from "../consts"
import { SpreadPageType } from "../types"

defineOptions({
  name: "SpreadAnalysisChart",
})

const { page: _page = SpreadPageTypeEnum.STATIC } = defineProps<{
  /** 页面类型，"静态" 或 "动态" 视图 */
  page?: SpreadPageType
}>()

use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent,
  GridComponent,
  LineChart,
  CanvasRenderer,
])

/** 容器引用 */
const containerRef = useTemplateRef<HTMLElement | null>("containerRef")
/** 滚动容器引用 */
const scrollContainerRef = useTemplateRef<HTMLElement | null>("scrollContainerRef")
/** 图表引用 */
const chartRef = useTemplateRef<InstanceType<typeof VChart> | null>("chartRef")

/** 全屏功能 */
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen(containerRef)

/** 导出菜单配置 */
const exportMenuOptions = [
  { label: "导出数据", value: "export-data" },
  { label: "导出完整数据集", value: "export-full-dataset" },
  { label: "导出图片", value: "export-image" },
]

const option = ref({
  title: {
    text: "Stacked Line",
  },
  tooltip: {
    trigger: "axis",
  },
  legend: {
    data: ["Email", "Union Ads", "Video Ads", "Direct", "Search Engine"],
  },
  grid: {
    left: "12px",
    right: "12px",
    bottom: "24px",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
  },
  yAxis: {
    type: "value",
  },
  series: [
    {
      name: "Email",
      type: "line",
      stack: "Total",
      data: [120, 132, 101, 134, 90, 230, 210],
    },
    {
      name: "Union Ads",
      type: "line",
      stack: "Total",
      data: [220, 182, 191, 234, 290, 330, 310],
    },
    {
      name: "Video Ads",
      type: "line",
      stack: "Total",
      data: [150, 232, 201, 154, 190, 330, 410],
    },
    {
      name: "Direct",
      type: "line",
      stack: "Total",
      data: [320, 332, 301, 334, 390, 330, 320],
    },
    {
      name: "Search Engine",
      type: "line",
      stack: "Total",
      data: [820, 932, 901, 934, 1290, 1330, 1320],
    },
  ],
})

// 考虑到筛选组件有【展开/折叠】功能，会导致图表高度改变，进而影响内容的完整展示
// 这里需要在外部容器高度改变时同步更新图表的尺寸
useResizeObserver(containerRef, () => {
  nextTick(() => {
    // 触发图表重新计算尺寸
    if (chartRef.value) {
      chartRef.value.resize()
    }
  })
})

// 监听全屏状态变化，确保图表尺寸正确调整
watch(
  isFullscreen,
  newVal => {
    console.log(`全屏状态变化: ${newVal ? "进入全屏" : "退出全屏"}`)

    // 延迟调整图表尺寸，等待DOM更新完成
    setTimeout(() => {
      if (chartRef.value) {
        chartRef.value.resize()
      }
    }, 100)
  },
  { flush: "post" }
)

// 添加键盘快捷键支持
onMounted(() => {
  const handleKeydown = (event: KeyboardEvent) => {
    // F11 键切换全屏
    if (event.key === "F11") {
      event.preventDefault()
      handleFullscreen()
    }
    // ESC 键退出全屏
    else if (event.key === "Escape" && isFullscreen.value) {
      event.preventDefault()
      handleFullscreen()
    }
  }

  document.addEventListener("keydown", handleKeydown)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener("keydown", handleKeydown)
  })
})

/**
 * 处理刷新操作
 */
function handleRefresh() {
  // TODO: 实现刷新逻辑
  console.log("刷新图表数据")
}

/**
 * 处理导出菜单点击
 */
function handleExportMenu(value: string) {
  switch (value) {
    case "export-data":
      handleExportData()
      break
    case "export-full-dataset":
      handleExportFullDataset()
      break
    case "export-image":
      handleExportImage()
      break
  }
}

/**
 * 导出数据
 */
async function handleExportData() {
  try {
    // 检查浏览器支持
    if (!isFileSystemAccessSupported()) {
      XqMessage.warning("您的浏览器不支持高级下载功能，将使用传统下载方式")
      handleExportDataFallback()
      return
    }

    // 准备导出数据（这里使用图表的示例数据）
    const exportData = option.value.series
      .map((series: any) => {
        const xAxisData = option.value.xAxis.data || []
        return series.data.map((value: number, dataIndex: number) => ({
          日期: xAxisData[dataIndex] || `数据点${dataIndex + 1}`,
          系列名称: series.name,
          数值: value,
        }))
      })
      .flat()

    // 转换为 CSV 格式
    const csvContent = convertToCSV(exportData)
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" })
    const url = URL.createObjectURL(blob)

    try {
      const result = await downloadFile(
        url,
        `利差分析数据_${new Date().toISOString().split("T")[0]}.csv`,
        {
          types: [
            {
              description: "CSV files",
              accept: { "text/csv": [".csv"] },
            },
          ],
          onProgress: (progress: DownloadProgress) => {
            console.log(`导出进度: ${progress.percentage.toFixed(1)}%`)
          },
          onError: (error: Error) => {
            XqMessage.error(`导出失败: ${error.message}`)
          },
        }
      )

      if (result.success) {
        XqMessage.success("数据导出成功")
      } else {
        XqMessage.error(`导出失败: ${result.error}`)
      }
    } finally {
      URL.revokeObjectURL(url)
    }
  } catch (error) {
    console.error("导出数据失败:", error)
    XqMessage.error("导出数据失败，请重试")
  }
}

/**
 * 导出完整数据集
 */
async function handleExportFullDataset() {
  try {
    XqMessage.info("正在准备完整数据集，请稍候...")

    // 使用异步文件下载
    const result = await downloadAsyncFile(
      "/api/spread-analysis/export-full", // 后端数据生成接口
      `利差分析完整数据集_${new Date().toISOString().split("T")[0]}.zip`,
      {
        // 轮询配置
        pollInterval: 2000, // 每2秒查询一次
        maxWaitTime: 5 * 60 * 1000, // 最大等待5分钟

        // 请求头
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },

        // 状态变化回调
        onStatusChange: (status: AsyncFileStatus) => {
          switch (status.status) {
            case "pending":
              XqMessage.info("正在向服务器发起数据导出请求...")
              break
            case "processing":
              XqMessage.info(`正在处理数据: ${status.progress || 0}%`)
              break
            case "ready":
              XqMessage.success("数据处理完成，开始下载文件")
              break
            case "failed":
              XqMessage.error(`数据处理失败: ${status.error}`)
              break
          }
        },

        // 文件生成进度回调
        onGenerationProgress: (progress: number) => {
          console.log(`数据生成进度: ${progress}%`)
          // 可以在这里更新UI进度条
        },

        // 文件下载进度回调
        onProgress: (progress: DownloadProgress) => {
          console.log(`文件下载进度: ${progress.percentage.toFixed(1)}%`)
        },
      }
    )

    if (result.success) {
      XqMessage.success(
        `完整数据集导出成功！生成耗时: ${Math.round((result.generationTime || 0) / 1000)}秒`
      )
    } else {
      XqMessage.error(`导出失败: ${result.error}`)
    }
  } catch (error) {
    console.error("异步导出失败:", error)
    XqMessage.error(`导出失败: ${error.message || "未知错误"}`)
  }
}

/**
 * 传统方式导出数据（兜底方案）
 *
 * 适用于以下浏览器版本：
 * - Chrome < 86 (2020年10月之前)
 * - Firefox 所有版本 (截至2025年仍不支持 File System Access API)
 * - Safari 所有版本 (截至2025年仍不支持 File System Access API)
 * - Edge < 86 (2020年10月之前)
 * - 其他基于 Chromium 的浏览器 < 86
 *
 * 使用传统的 Blob + a标签下载方式，兼容性最好
 */
function handleExportDataFallback() {
  try {
    // 准备导出数据
    const exportData = option.value.series
      .map((series: any) => {
        const xAxisData = option.value.xAxis.data || []
        return series.data.map((value: number, dataIndex: number) => ({
          日期: xAxisData[dataIndex] || `数据点${dataIndex + 1}`,
          系列名称: series.name,
          数值: value,
        }))
      })
      .flat()

    // 转换为 CSV 格式
    const csvContent = convertToCSV(exportData)
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" })

    // 创建下载链接
    const link = document.createElement("a")
    link.href = URL.createObjectURL(blob)
    link.download = `利差分析数据_${new Date().toISOString().split("T")[0]}.csv`

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(link.href)
    XqMessage.success("数据导出成功")
  } catch (error) {
    console.error("导出数据失败:", error)
    XqMessage.error("导出数据失败，请重试")
  }
}

/**
 * 将数据转换为 CSV 格式
 */
function convertToCSV(data: any[]): string {
  if (!data || data.length === 0) {
    return ""
  }

  // 获取表头
  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(",")

  // 转换数据行
  const csvRows = data.map(row => {
    return headers
      .map(header => {
        const value = row[header]
        // 处理包含逗号或换行符的值
        if (typeof value === "string" && (value.includes(",") || value.includes("\n"))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value ?? ""
      })
      .join(",")
  })

  return [csvHeaders, ...csvRows].join("\n")
}

/**
 * 导出图片
 */
function handleExportImage() {
  if (chartRef.value) {
    // 获取图表实例 - 使用正确的 API
    const chart = (chartRef.value as any).chart

    if (chart) {
      // 获取图表的 base64 数据
      const imageData = chart.getDataURL({
        type: "png",
        pixelRatio: 2, // 提高清晰度
        backgroundColor: "#fff",
      })

      // 创建下载链接
      const link = document.createElement("a")
      link.href = imageData
      link.download = `图表_${new Date().toISOString().slice(0, 10)}.png`

      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}

/**
 * 处理全屏操作
 */
async function handleFullscreen() {
  try {
    await toggleFullscreen()

    // 全屏状态变化后，需要重新调整图表尺寸
    await nextTick()
    if (chartRef.value) {
      chartRef.value.resize()
    }

    // 用户提示
    if (isFullscreen.value) {
      XqMessage.success("已进入全屏模式，按 ESC 键可退出全屏")
    } else {
      XqMessage.info("已退出全屏模式")
    }

    console.log(`图表全屏状态: ${isFullscreen.value ? "已开启" : "已关闭"}`)
  } catch (error) {
    console.error("切换全屏失败:", error)
    XqMessage.error("全屏功能不被支持或已被阻止")
  }
}

/**
 * 获取认证令牌
 */
function getAuthToken(): string {
  // 实际项目中从存储或状态管理中获取
  return localStorage.getItem("auth_token") || "mock-token"
}
</script>

<template>
  <div
    ref="containerRef"
    class="flex flex-col w-full h-full"
    :class="[isFullscreen ? 'bg-white p-[24px]' : 'p-[12px] pt-0']"
  >
    <div class="flex h-[30px] justify-between">
      <div class="flex h-[30px] items-center text-[12px] leading-[30px]">
        <xq-icon class="h-[20px] w-[20px]">
          <History class="text-[var(--xq-text-color-placeholder)]" />
        </xq-icon>
        <span class="ml-[4px] font-bold text-[var(--xq-color-primary)]" title="数据刷新时间">
          {{ new Date().toLocaleString() }}
        </span>
      </div>
      <div class="flex items-center space-x-1">
        <ModuleIconButton title="刷新" @click="handleRefresh">
          <Refresh />
        </ModuleIconButton>
        <ModuleIconButton
          title="导出"
          :menu-options="exportMenuOptions"
          :width="100"
          :append-to-body="!isFullscreen"
          @menu-item-click="handleExportMenu"
        >
          <Download />
        </ModuleIconButton>
        <ModuleIconButton :title="isFullscreen ? '退出全屏' : '全屏'" @click="handleFullscreen">
          <FullScreenExit v-if="isFullscreen" />
          <FullScreen v-else />
        </ModuleIconButton>
      </div>
    </div>
    <!-- 自定义滚动容器 -->
    <div
      ref="scrollContainerRef"
      class="chart-scroll-wrapper"
      :class="[isFullscreen ? 'mt-4' : 'mt-2']"
    >
      <div class="chart-content">
        <VChart ref="chartRef" class="chart-instance" :option="option" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 滚动容器包装器
.chart-scroll-wrapper {
  flex: 1;
  position: relative;
  overflow: auto;
  min-height: 300px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--xq-fill-color-lighter);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--xq-border-color-darker);
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--xq-text-color-placeholder);
    }
  }

  &::-webkit-scrollbar-corner {
    background: var(--xq-fill-color-lighter);
  }

  // Firefox 滚动条样式
  scrollbar-width: thin;
  scrollbar-color: var(--xq-border-color-darker) var(--xq-fill-color-lighter);
}

// 图表内容容器
.chart-content {
  min-width: 600px;
  min-height: 300px;
  width: 100%;
  height: 100%;
  position: relative;
}

// 图表实例
.chart-instance {
  width: 100%;
  height: 100%;
  min-width: 600px;
  min-height: 300px;
}
</style>
