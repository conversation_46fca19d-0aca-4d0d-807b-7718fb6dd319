<script setup lang="ts">
defineOptions({
  name: "403",
})

const router = useRouter()
</script>

<template>
  <div class="flex h-[640px] items-center justify-center">
    <svg-icon class="!h-[180px] !w-[180px]" name="403" />
    <div class="ml-12">
      <p
        v-motion
        class="mb-4 text-4xl font-medium dark:text-white"
        :initial="{
          opacity: 0,
          y: 100,
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 100,
          },
        }"
      >
        403
      </p>
      <p
        v-motion
        class="mb-4 text-gray-500"
        :initial="{
          opacity: 0,
          y: 100,
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 300,
          },
        }"
      >
        抱歉，你无权访问该页面
      </p>
      <xq-button
        v-motion
        type="primary"
        :initial="{
          opacity: 0,
          y: 100,
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 500,
          },
        }"
        @click="router.push('/')"
      >
        返回首页
      </xq-button>
    </div>
  </div>
</template>
