import { CustomViewCodeEnum } from "@/constants"

import { ColorType } from "./common"

/**
 * 视图页面编码，用于保存查询配置模板
 */
export type CustomViewCodeType =
  (typeof CustomViewCodeEnum)[keyof typeof CustomViewCodeEnum]

/**
 * 自定义视图配置，用于保存查询模板
 */
export interface CustomViewConfig {
  /** 视图名称 */
  name: string
  /** 视图编码 */
  code: CustomViewCodeType
  /** 视图 ID */
  id: string
  /** 视图描述 */
  description: string
  /** 视图颜色编码 */
  colorCode: "" | ColorType
  /** 视图排序 */
  order: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 视图数据 */
  data: {
    /** 查询表单模型 */
    queryConfig: unknown
    /** 自定义数据展示维度 */
    chartConfig: unknown
  }
}
