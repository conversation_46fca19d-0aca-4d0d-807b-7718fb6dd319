<!--
  浏览器兼容性检测演示组件
  展示当前浏览器对下载功能的支持情况
-->
<template>
  <div class="browser-compatibility-demo">
    <div class="compatibility-section">
      <h3>浏览器兼容性检测</h3>

      <!-- 基础信息 -->
      <div class="browser-info">
        <div class="info-card">
          <h4>当前浏览器</h4>
          <div class="info-content">
            <div class="info-item">
              <span class="label">浏览器：</span>
              <span class="value">
                {{ capabilities.browserName }} {{ capabilities.browserVersion }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">推荐方式：</span>
              <span class="value" :class="recommendedMethodClass">
                {{ capabilities.recommendedMethod === "modern" ? "现代下载" : "传统下载" }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能支持情况 -->
      <div class="features-support">
        <h4>功能支持情况</h4>
        <div class="features-grid">
          <div class="feature-item" :class="{ supported: capabilities.canUseModernDownload }">
            <i :class="capabilities.canUseModernDownload ? 'el-icon-check' : 'el-icon-close'"></i>
            <span>File System Access API</span>
            <small>
              {{ capabilities.canUseModernDownload ? "支持选择保存位置" : "需要传统下载方式" }}
            </small>
          </div>

          <div class="feature-item" :class="{ supported: capabilities.canUseWebWorker }">
            <i :class="capabilities.canUseWebWorker ? 'el-icon-check' : 'el-icon-close'"></i>
            <span>Web Worker</span>
            <small>{{ capabilities.canUseWebWorker ? "支持后台处理" : "可能阻塞主线程" }}</small>
          </div>

          <div class="feature-item" :class="{ supported: capabilities.canUseStreaming }">
            <i :class="capabilities.canUseStreaming ? 'el-icon-check' : 'el-icon-close'"></i>
            <span>流式处理</span>
            <small>
              {{ capabilities.canUseStreaming ? "支持大文件处理" : "可能占用大量内存" }}
            </small>
          </div>
        </div>
      </div>

      <!-- 功能限制 -->
      <div v-if="capabilities.limitations.length" class="limitations">
        <h4>功能限制</h4>
        <ul class="limitation-list">
          <li v-for="limitation in capabilities.limitations" :key="limitation">
            <i class="el-icon-warning"></i>
            {{ limitation }}
          </li>
        </ul>
      </div>

      <!-- 建议 -->
      <div v-if="capabilities.recommendations.length" class="recommendations">
        <h4>升级建议</h4>
        <ul class="recommendation-list">
          <li v-for="recommendation in capabilities.recommendations" :key="recommendation">
            <i class="el-icon-info"></i>
            {{ recommendation }}
          </li>
        </ul>
      </div>

      <!-- 版本对照表 -->
      <div class="version-table">
        <h4>浏览器版本支持对照表</h4>
        <table class="compatibility-table">
          <thead>
            <tr>
              <th>浏览器</th>
              <th>支持现代下载的最低版本</th>
              <th>发布时间</th>
              <th>当前状态</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="browser in browserVersions"
              :key="browser.name"
              :class="{ current: browser.isCurrent }"
            >
              <td>{{ browser.name }}</td>
              <td>{{ browser.minVersion }}</td>
              <td>{{ browser.releaseDate }}</td>
              <td>
                <span :class="`status-${browser.status}`">
                  {{ getStatusText(browser.status) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 测试下载功能 -->
      <div class="test-section">
        <h4>测试下载功能</h4>
        <div class="test-buttons">
          <XqButton
            type="primary"
            :disabled="!capabilities.canUseModernDownload"
            @click="testModernDownload"
          >
            测试现代下载
          </XqButton>
          <XqButton @click="testFallbackDownload">测试传统下载</XqButton>
          <XqButton type="info" @click="refreshCompatibility">重新检测</XqButton>
        </div>

        <div v-if="testResult" class="test-result" :class="`result-${testResult.type}`">
          <i :class="testResult.type === 'success' ? 'el-icon-check' : 'el-icon-close'"></i>
          <span>{{ testResult.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  onMounted,
  ref,
} from "vue"

import {
  downloadFile,
  getBrowserCompatibilityInfo,
  getDownloadCapabilities,
} from "@/utils/file-download"
import { XqMessage } from "@xquant/x-ui-plus"

// 兼容性信息
const capabilities = ref(getDownloadCapabilities())

// 测试结果
const testResult = ref<{ type: "success" | "error"; message: string } | null>(null)

// 浏览器版本对照表
const browserVersions = computed(() => {
  const currentBrowser = getBrowserCompatibilityInfo()

  return [
    {
      name: "Chrome",
      minVersion: "86+",
      releaseDate: "2020年10月",
      status:
        currentBrowser.browser === "Chrome" && parseInt(currentBrowser.version) >= 86
          ? "supported"
          : currentBrowser.browser === "Chrome"
            ? "outdated"
            : "unknown",
      isCurrent: currentBrowser.browser === "Chrome",
    },
    {
      name: "Edge",
      minVersion: "86+",
      releaseDate: "2020年10月",
      status:
        currentBrowser.browser === "Edge" && parseInt(currentBrowser.version) >= 86
          ? "supported"
          : currentBrowser.browser === "Edge"
            ? "outdated"
            : "unknown",
      isCurrent: currentBrowser.browser === "Edge",
    },
    {
      name: "Firefox",
      minVersion: "不支持",
      releaseDate: "-",
      status: "unsupported",
      isCurrent: currentBrowser.browser === "Firefox",
    },
    {
      name: "Safari",
      minVersion: "不支持",
      releaseDate: "-",
      status: "unsupported",
      isCurrent: currentBrowser.browser === "Safari",
    },
  ]
})

// 推荐方式的样式类
const recommendedMethodClass = computed(() => {
  return capabilities.value.recommendedMethod === "modern" ? "modern" : "fallback"
})

/**
 * 获取状态文字
 */
function getStatusText(status: string): string {
  const statusMap = {
    supported: "✅ 支持",
    outdated: "⚠️ 版本过低",
    unsupported: "❌ 不支持",
    unknown: "❓ 未知",
  }
  return statusMap[status] || "未知"
}

/**
 * 测试现代下载功能
 */
async function testModernDownload() {
  try {
    testResult.value = null

    // 创建测试文件
    const testData =
      "这是一个测试文件，用于验证现代下载功能。\n测试时间：" + new Date().toLocaleString()
    const blob = new Blob([testData], { type: "text/plain;charset=utf-8" })
    const url = URL.createObjectURL(blob)

    const result = await downloadFile(url, "现代下载测试.txt", {
      types: [
        {
          description: "Text files",
          accept: { "text/plain": [".txt"] },
        },
      ],
    })

    URL.revokeObjectURL(url)

    if (result.success) {
      testResult.value = { type: "success", message: "现代下载功能测试成功！" }
      XqMessage.success("现代下载功能正常")
    } else {
      testResult.value = { type: "error", message: `测试失败：${result.error}` }
    }
  } catch (error) {
    testResult.value = { type: "error", message: `测试失败：${error.message}` }
    XqMessage.error("现代下载功能测试失败")
  }
}

/**
 * 测试传统下载功能
 */
function testFallbackDownload() {
  try {
    testResult.value = null

    // 创建测试文件
    const testData =
      "这是一个测试文件，用于验证传统下载功能。\n测试时间：" + new Date().toLocaleString()
    const blob = new Blob([testData], { type: "text/plain;charset=utf-8" })

    // 使用传统下载方式
    const link = document.createElement("a")
    link.href = URL.createObjectURL(blob)
    link.download = "传统下载测试.txt"

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(link.href)

    testResult.value = { type: "success", message: "传统下载功能测试成功！" }
    XqMessage.success("传统下载功能正常")
  } catch (error) {
    testResult.value = { type: "error", message: `测试失败：${error.message}` }
    XqMessage.error("传统下载功能测试失败")
  }
}

/**
 * 重新检测兼容性
 */
function refreshCompatibility() {
  capabilities.value = getDownloadCapabilities()
  testResult.value = null
  XqMessage.info("兼容性信息已更新")
}

onMounted(() => {
  console.log("浏览器兼容性信息:", capabilities.value)
})
</script>

<style scoped>
.browser-compatibility-demo {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.compatibility-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.compatibility-section h3 {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 20px;
}

.compatibility-section h4 {
  margin: 24px 0 16px 0;
  color: #555;
  font-size: 16px;
}

.browser-info {
  margin-bottom: 24px;
}

.info-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  width: 80px;
  color: #666;
}

.info-item .value {
  color: #333;
}

.info-item .value.modern {
  color: #22c55e;
  font-weight: 600;
}

.info-item .value.fallback {
  color: #f59e0b;
  font-weight: 600;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.feature-item.supported {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.feature-item i {
  font-size: 24px;
  margin-bottom: 8px;
}

.feature-item.supported i {
  color: #22c55e;
}

.feature-item:not(.supported) i {
  color: #ef4444;
}

.feature-item span {
  font-weight: 500;
  margin-bottom: 4px;
}

.feature-item small {
  color: #6b7280;
  font-size: 12px;
}

.limitations,
.recommendations {
  margin-bottom: 24px;
}

.limitation-list,
.recommendation-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.limitation-list li,
.recommendation-list li {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.limitation-list li:last-child,
.recommendation-list li:last-child {
  border-bottom: none;
}

.limitation-list i {
  color: #f59e0b;
  margin-right: 8px;
}

.recommendation-list i {
  color: #3b82f6;
  margin-right: 8px;
}

.compatibility-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;
}

.compatibility-table th,
.compatibility-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.compatibility-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
}

.compatibility-table tr.current {
  background: #fef3c7;
}

.status-supported {
  color: #22c55e;
  font-weight: 500;
}

.status-outdated {
  color: #f59e0b;
  font-weight: 500;
}

.status-unsupported {
  color: #ef4444;
  font-weight: 500;
}

.status-unknown {
  color: #6b7280;
  font-weight: 500;
}

.test-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
}

.test-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.test-result {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  font-weight: 500;
}

.test-result.result-success {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.test-result.result-error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.test-result i {
  margin-right: 8px;
  font-size: 16px;
}
</style>
