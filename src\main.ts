// 引入重置样式
import "normalize.css"
// 导入cube-ui-biz样式
import "@cube-ui/biz/styles/src/index.scss"
// 浦银理财行方定制xui-plus样式（一定要在cube-ui-biz样式之后）
import "./style/xui-plus.scss"
// 导入公共样式
import "./style/index.scss"
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./style/tailwind.css"
import "vue-loading-overlay/dist/css/index.css"
//svg 引用
import "virtual:svg-icons-register"

import PortalVue from "portal-vue"
import Loading, { LoadingPlugin } from "vue-loading-overlay"
import {
  createMemoryHistory,
  createWebHistory,
  type Router,
  RouterHistory,
} from "vue-router"

import {
  APP,
  POWERED_BY_QIANKUN,
  VUE_APP_PUBLIC_PATH,
} from "@/business/dictionary/index"
import components from "@/components"
// 全局注册按钮级别权限组件
import { Auth } from "@/components/ReAuth"
// 自定义指令
import * as directives from "@/directives"
import { cubeUIBiz } from "@/plugins/cube-ui-biz"
// for tree shaking
import { XUIPlus } from "@/plugins/x-ui-plus"
import { setupStore } from "@/store"
import { VueQueryPlugin } from "@tanstack/vue-query"
// 全局注册 x-ui 图标库
import { XuiIcon } from "@xquant/xp-admin-layout"

import App from "./App.vue"
import { createRouterInstance } from "./router"
import { emitter } from "./utils/mitt"

let router: Router = null
let instance = null
const defaultOverrideOH = (mh: RouterHistory) => mh

async function render(props: QianKunProps = {}) {
  // 在应用渲染前启用 MirageJS mock 服务
  // MirageJS 已迁移到 Mockoon，无需初始化
  // 现在通过 vite.config.ts 中的代理配置连接到 Mockoon 服务

  const { container, overrideOriginHistory = defaultOverrideOH } = props
  const base = props.baseroute || VUE_APP_PUBLIC_PATH

  // 根据不同的场景创建不同的路由
  router = createRouterInstance(
    POWERED_BY_QIANKUN
      ? overrideOriginHistory(createMemoryHistory(base))
      : createWebHistory(base)
  )
  instance = createApp(App, {
    parentProps: props,
  })
  // 安装 pinia
  setupStore(instance)
  // 安装路由
  instance.use(router)
  await router.isReady()
  // 安装全局指令
  Object.keys(directives).forEach(key => {
    instance.directive(key, (directives as { [key: string]: Directive })[key])
  })
  // 安装所有公共组件
  instance.use(components)
  instance.component("Auth", Auth)
  instance.component("XuiIcon", XuiIcon)
  instance.component("Loading", Loading)
  instance.use(XUIPlus)
  instance.use(cubeUIBiz)
  instance.use(PortalVue)
  instance.use(LoadingPlugin, {
    // 这里全局配置似乎不生效
    // color: "#407fff",
  })
  // 安装 Vue Query
  instance.use(VueQueryPlugin)
  // 挂载应用
  const rootSelector = `#app-${import.meta.env.VITE_APP_NAME}`
  instance.mount(
    container ? container.querySelector(rootSelector) : rootSelector
  )
}

if (!POWERED_BY_QIANKUN) {
  render()
}

/**
 * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
 * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
 */
export async function bootstrap() {
  console.log(`${APP} app  bootstraped`)
}
/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props: QianKunProps) {
  console.log(`${APP} app  mounted`)
  // 这里应该接收来自主应用的状态，包括：登录token，用户信息？，主题配置，国际化配置等
  render(props)
}
/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount() {
  console.log(`${APP} app  unmounted`)
  instance.unmount()
  instance._container.innerHTML = ""
  instance = null
  router = null
}
/**
 * 可选生命周期钩子，仅使用 loadMicroApp 方式加载微应用时生效
 */
export async function update(props) {
  // 这里主应用可能切换了主题配置、语言设置等，子应用需要对应调整
  console.log(`${APP} app's prop  update`, props)
  const { path } = props
  if (path && router) {
    if (router.currentRoute.value.fullPath !== path) {
      const to = router.resolve(path)
      router.push({
        ...to,
        state: {
          fromMain: true,
        },
      })
    } else {
      emitter.emit("gotoSameRoute", path)
    }
  }
}
