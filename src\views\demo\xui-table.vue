<script setup lang="ts">
import { computed, ref } from "vue"

defineOptions({
  name: "XuiTablePage",
})

// 加载状态和数据状态管理
const isLoading = ref(false)
const isEmpty = ref(false)

// 模拟数据加载
const simulateDataLoading = async () => {
  isLoading.value = true
  isEmpty.value = false

  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1500))

  isLoading.value = false

  // 随机决定是否有数据
  const hasData = Math.random() > 0.3 // 70% 概率有数据
  isEmpty.value = !hasData

  if (hasData) {
    // 恢复原始数据
    data.value = [...originalData]
  } else {
    // 清空数据
    data.value = []
  }
}

// 恢复数据
const restoreData = () => {
  data.value = [...originalData]
  isEmpty.value = false
}

// 清空数据
const clearData = () => {
  data.value = []
  isEmpty.value = true
}

// 定义数据类型
interface TableData {
  id: string
  // 债券相关字段
  bondGovernment?: number | null // 国债
  bondCorporate?: number | null // 企业债
  // 股票相关字段
  stockLarge?: number | null // 大盘股
  stockSmall?: number | null // 中小盘
  // 基金相关字段
  fundMoney?: number | null // 货币基金
  fundStock?: number | null // 股票基金
  // 公共字段
  name: string
  value: number
  status: string
  date: string
}

// 原始数据备份
const originalData: TableData[] = [
  {
    id: "1",
    name: "国债10年期",
    bondGovernment: 3.15,
    bondCorporate: null,
    stockLarge: null,
    stockSmall: null,
    fundMoney: null,
    fundStock: null,
    value: 3.15,
    status: "正常",
    date: "2024-01-15",
  },
  {
    id: "2",
    name: "AAA企业债",
    bondGovernment: null,
    bondCorporate: 4.25,
    stockLarge: null,
    stockSmall: null,
    fundMoney: null,
    fundStock: null,
    value: 4.25,
    status: "关注",
    date: "2024-01-16",
  },
  {
    id: "3",
    name: "招商银行",
    bondGovernment: null,
    bondCorporate: null,
    stockLarge: 45.2,
    stockSmall: null,
    fundMoney: null,
    fundStock: null,
    value: 45.2,
    status: "正常",
    date: "2024-01-17",
  },
  {
    id: "4",
    name: "宁德时代",
    bondGovernment: null,
    bondCorporate: null,
    stockLarge: null,
    stockSmall: 180.5,
    fundMoney: null,
    fundStock: null,
    value: 180.5,
    status: "关注",
    date: "2024-01-18",
  },
  {
    id: "5",
    name: "余额宝",
    bondGovernment: null,
    bondCorporate: null,
    stockLarge: null,
    stockSmall: null,
    fundMoney: 2.15,
    fundStock: null,
    value: 2.15,
    status: "正常",
    date: "2024-01-19",
  },
  {
    id: "6",
    name: "易方达蓝筹",
    bondGovernment: null,
    bondCorporate: null,
    stockLarge: null,
    stockSmall: null,
    fundMoney: null,
    fundStock: 15.8,
    value: 15.8,
    status: "风险",
    date: "2024-01-20",
  },
  {
    id: "7",
    name: "五年期国债",
    bondGovernment: 2.85,
    bondCorporate: null,
    stockLarge: null,
    stockSmall: null,
    fundMoney: null,
    fundStock: null,
    value: 2.85,
    status: "正常",
    date: "2024-01-21",
  },
  {
    id: "8",
    name: "BBB企业债",
    bondGovernment: null,
    bondCorporate: 5.2,
    stockLarge: null,
    stockSmall: null,
    fundMoney: null,
    fundStock: null,
    value: 5.2,
    status: "关注",
    date: "2024-01-22",
  },
  {
    id: "9",
    name: "平安银行",
    bondGovernment: null,
    bondCorporate: null,
    stockLarge: 18.5,
    stockSmall: null,
    fundMoney: null,
    fundStock: null,
    value: 18.5,
    status: "正常",
    date: "2024-01-23",
  },
  {
    id: "10",
    name: "比亚迪",
    bondGovernment: null,
    bondCorporate: null,
    stockLarge: null,
    stockSmall: 245.8,
    fundMoney: null,
    fundStock: null,
    value: 245.8,
    status: "正常",
    date: "2024-01-24",
  },
]

// 当前数据状态
const data = ref<TableData[]>([...originalData])

// 表格配置
const tableConfig = ref({
  height: 400, // 表格高度
  stripe: true, // 斑马纹
  border: true, // 边框
  size: "small" as const, // 紧凑尺寸
})

// 格式化数值显示
const formatValue = (value: number | null) => {
  return value !== null && value !== undefined ? value.toFixed(2) : "-"
}

// 获取状态标签样式
const getStatusTagType = (status: string) => {
  switch (status) {
    case "正常":
      return "success"
    case "关注":
      return "warning"
    case "风险":
      return "danger"
    default:
      return "info"
  }
}

// 单元格样式配置
const getCellClassName = ({
  column,
  rowIndex,
}: {
  column: any
  columnIndex: number
  rowIndex: number
}) => {
  const prop = column.property
  const isOddRow = rowIndex % 2 === 1

  // 债券相关列 - 蓝色系
  if (prop === "bondGovernment" || prop === "bondCorporate") {
    return isOddRow ? "bond-cell bond-cell-striped" : "bond-cell"
  }

  // 股票相关列 - 绿色系
  if (prop === "stockLarge" || prop === "stockSmall") {
    return isOddRow ? "stock-cell stock-cell-striped" : "stock-cell"
  }

  // 基金相关列 - 紫色系
  if (prop === "fundMoney" || prop === "fundStock") {
    return isOddRow ? "fund-cell fund-cell-striped" : "fund-cell"
  }

  // 其他列的斑马纹
  return isOddRow ? "normal-cell-striped" : ""
}

// 表头单元格样式配置
const getHeaderCellClassName = ({ column }: { column: any; columnIndex: number }) => {
  const prop = column.property

  // 债券相关列 - 蓝色系表头
  if (prop === "bondGovernment" || prop === "bondCorporate") {
    return "bond-header"
  }

  // 股票相关列 - 绿色系表头
  if (prop === "stockLarge" || prop === "stockSmall") {
    return "stock-header"
  }

  // 基金相关列 - 紫色系表头
  if (prop === "fundMoney" || prop === "fundStock") {
    return "fund-header"
  }

  return ""
}

// 行样式配置
const getRowClassName = ({ rowIndex }: { row: any; rowIndex: number }) => {
  return rowIndex % 2 === 1 ? "striped-row" : ""
}

// 计算显示数据（包括空白行）
const displayData = computed(() => {
  if (isEmpty.value && !isLoading.value) {
    return []
  }
  return data.value
})
</script>

<template>
  <div class="bg-white p-6">
    <div class="mb-6">
      <h1 class="mb-2 text-2xl font-bold text-gray-900">XUI 表格演示 - 分组列合并与填充效果</h1>
      <p class="text-gray-600">使用 xq-table 组件实现多层表头、分组列合并和颜色填充效果</p>
    </div>

    <!-- 控制面板 -->
    <div
      class="mb-6 rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50 p-4"
    >
      <h3 class="mb-4 text-sm font-semibold text-gray-700">表格配置：</h3>
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-2">
          <label class="text-xs font-medium text-gray-600">表格高度:</label>
          <input
            v-model.number="tableConfig.height"
            type="number"
            min="200"
            max="800"
            step="50"
            class="w-20 rounded border border-gray-300 px-2 py-1 text-xs focus:border-blue-500 focus:outline-none"
          />
          <span class="text-xs text-gray-500">px</span>
        </div>

        <div class="flex items-center gap-2">
          <input
            id="stripe"
            v-model="tableConfig.stripe"
            type="checkbox"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label for="stripe" class="text-xs font-medium text-gray-600">斑马纹</label>
        </div>

        <div class="flex items-center gap-2">
          <input
            id="border"
            v-model="tableConfig.border"
            type="checkbox"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label for="border" class="text-xs font-medium text-gray-600">边框</label>
        </div>

        <div class="flex gap-2">
          <button
            :disabled="isLoading"
            class="rounded bg-blue-500 px-3 py-1 text-xs text-white hover:bg-blue-600 disabled:bg-gray-400"
            @click="simulateDataLoading"
          >
            {{ isLoading ? "加载中..." : "模拟加载" }}
          </button>
          <button
            :disabled="isLoading"
            class="rounded bg-green-500 px-3 py-1 text-xs text-white hover:bg-green-600 disabled:bg-gray-400"
            @click="restoreData"
          >
            恢复数据
          </button>
          <button
            :disabled="isLoading"
            class="rounded bg-red-500 px-3 py-1 text-xs text-white hover:bg-red-600 disabled:bg-gray-400"
            @click="clearData"
          >
            清空数据
          </button>
        </div>
      </div>
    </div>

    <!-- 颜色说明 -->
    <div class="mb-6 rounded-lg bg-gray-50 p-4">
      <h3 class="mb-3 text-sm font-semibold text-gray-700">颜色分组说明：</h3>
      <div class="flex flex-wrap gap-4">
        <div class="flex items-center gap-2">
          <div class="h-4 w-4 rounded border border-blue-200 bg-blue-100"></div>
          <span class="text-sm text-gray-600">债券类（蓝色系）</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-4 w-4 rounded border border-green-200 bg-green-100"></div>
          <span class="text-sm text-gray-600">股票类（绿色系）</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-4 w-4 rounded border border-purple-200 bg-purple-100"></div>
          <span class="text-sm text-gray-600">基金类（紫色系）</span>
        </div>
      </div>
    </div>

    <!-- XUI 表格 -->
    <div class="rounded-lg border border-gray-200 shadow-sm">
      <!-- Loading 状态 -->
      <div v-if="isLoading" class="flex items-center justify-center py-16">
        <div class="flex items-center gap-3">
          <div
            class="h-5 w-5 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"
          ></div>
          <span class="text-sm text-gray-600">加载中...</span>
        </div>
      </div>

      <!-- 空数据状态 -->
      <div v-else-if="isEmpty" class="flex flex-col items-center justify-center py-16">
        <div class="mb-4">
          <svg
            class="h-16 w-16 text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h3 class="mb-2 text-lg font-medium text-gray-900">暂无数据</h3>
        <p class="mb-4 text-sm text-gray-500">当前没有可显示的数据，请尝试重新加载</p>
        <button
          class="rounded-md bg-blue-500 px-4 py-2 text-sm text-white hover:bg-blue-600"
          @click="simulateDataLoading"
        >
          重新加载
        </button>
      </div>

      <!-- 表格内容 -->
      <xq-table
        v-else
        :data="displayData"
        :height="tableConfig.height"
        :stripe="false"
        :border="tableConfig.border"
        :size="tableConfig.size"
        :cell-class-name="getCellClassName"
        :header-cell-class-name="getHeaderCellClassName"
        :row-class-name="getRowClassName"
        style="width: 100%"
      >
        <!-- 产品名称列 -->
        <xq-table-column
          prop="name"
          label="产品名称"
          width="140"
          fixed="left"
          align="left"
          class-name="name-cell"
        />

        <!-- 债券分组 -->
        <xq-table-column label="债券" align="center" class-name="bond-group-header">
          <xq-table-column
            prop="bondGovernment"
            label="国债"
            width="100"
            align="center"
            :formatter="row => formatValue(row.bondGovernment)"
          />
          <xq-table-column
            prop="bondCorporate"
            label="企业债"
            width="100"
            align="center"
            :formatter="row => formatValue(row.bondCorporate)"
          />
        </xq-table-column>

        <!-- 股票分组 -->
        <xq-table-column label="股票" align="center" class-name="stock-group-header">
          <xq-table-column
            prop="stockLarge"
            label="大盘股"
            width="100"
            align="center"
            :formatter="row => formatValue(row.stockLarge)"
          />
          <xq-table-column
            prop="stockSmall"
            label="中小盘"
            width="100"
            align="center"
            :formatter="row => formatValue(row.stockSmall)"
          />
        </xq-table-column>

        <!-- 基金分组 -->
        <xq-table-column label="基金" align="center" class-name="fund-group-header">
          <xq-table-column
            prop="fundMoney"
            label="货币基金"
            width="100"
            align="center"
            :formatter="row => formatValue(row.fundMoney)"
          />
          <xq-table-column
            prop="fundStock"
            label="股票基金"
            width="100"
            align="center"
            :formatter="row => formatValue(row.fundStock)"
          />
        </xq-table-column>

        <!-- 总价值列 -->
        <xq-table-column
          prop="value"
          label="总价值"
          width="120"
          align="center"
          :formatter="row => formatValue(row.value)"
        />

        <!-- 状态列 -->
        <xq-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <xq-tag :type="getStatusTagType(row.status)" size="small">
              {{ row.status }}
            </xq-tag>
          </template>
        </xq-table-column>

        <!-- 日期列 -->
        <xq-table-column prop="date" label="日期" width="120" align="center" fixed="right" />
      </xq-table>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 债券相关样式 - 蓝色系 */
:deep(.bond-group-header) {
  background-color: #dbeafe !important;
  color: #1e40af !important;
  font-weight: 600;
}

:deep(.bond-header) {
  background-color: #bfdbfe !important;
  color: #1d4ed8 !important;
  font-weight: 500;
}

:deep(.bond-cell) {
  background-color: #f0f9ff !important;
  color: #1e40af;
}

:deep(.bond-cell-striped) {
  background-color: #e0f2fe !important;
  color: #1e40af;
}

/* 股票相关样式 - 绿色系 */
:deep(.stock-group-header) {
  background-color: #dcfce7 !important;
  color: #14532d !important;
  font-weight: 600;
}

:deep(.stock-header) {
  background-color: #bbf7d0 !important;
  color: #15803d !important;
  font-weight: 500;
}

:deep(.stock-cell) {
  background-color: #f0fdf4 !important;
  color: #14532d;
}

:deep(.stock-cell-striped) {
  background-color: #ecfdf5 !important;
  color: #14532d;
}

/* 基金相关样式 - 紫色系 */
:deep(.fund-group-header) {
  background-color: #f3e8ff !important;
  color: #581c87 !important;
  font-weight: 600;
}

:deep(.fund-header) {
  background-color: #e9d5ff !important;
  color: #7c2d12 !important;
  font-weight: 500;
}

:deep(.fund-cell) {
  background-color: #faf5ff !important;
  color: #581c87;
}

:deep(.fund-cell-striped) {
  background-color: #f5f3ff !important;
  color: #581c87;
}

/* 普通列的斑马纹 */
:deep(.normal-cell-striped) {
  background-color: #f9fafb !important;
}

/* 产品名称列样式 */
:deep(.name-cell) {
  font-weight: 500;
  color: #374151;
}

/* 表格整体样式优化 */
:deep(.#{$ns}-table) {
  font-size: 12px;
}

:deep(.#{$ns}-table .#{$ns}-table__cell) {
  padding: 4px 8px;
  height: 20px;
  line-height: 1;
}

:deep(.#{$ns}-table th.#{$ns}-table__cell) {
  height: 20px;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1;
}

/* 悬停效果 */
:deep(.#{$ns}-table__row:hover td) {
  background-color: rgba(59, 130, 246, 0.05) !important;
}

/* 固定列样式 */
:deep(.#{$ns}-table__fixed-left) {
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

:deep(.#{$ns}-table__fixed-right) {
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
}

:deep(.#{$ns}-table__header .#{$ns}-table__cell > .cell) {
  text-align: center;
}
</style>
