/**
 * 表格分组样式工具函数
 * 用于统一处理分组表头和单元格的样式类名
 */

/**
 * 获取表头单元格样式类名
 * @param column 列配置对象
 * @param columnIndex 列索引
 * @returns 样式类名字符串
 */
export function getGroupedHeaderCellClassName({ 
  column 
}: { 
  column: any; 
  columnIndex?: number 
}): string {
  // 根据分组表头设置样式
  if (column.level === 1) {
    const label = column.label
    if (label === "产品信息") {
      return "product-group-header"
    } else if (label === "指标基础信息") {
      return "indicator-group-header"
    } else if (label === "指标使用情况") {
      return "usage-group-header"
    }
  }
  
  // 子表头样式
  const parentLabel = column.parent?.label
  if (parentLabel === "产品信息") {
    return "product-header"
  } else if (parentLabel === "指标基础信息") {
    return "indicator-header"
  } else if (parentLabel === "指标使用情况") {
    return "usage-header"
  }

  return ""
}

/**
 * 获取单元格样式类名
 * @param column 列配置对象
 * @param row 行数据对象
 * @param rowIndex 行索引
 * @returns 样式类名字符串
 */
export function getGroupedCellClassName({ 
  column, 
  row, 
  rowIndex 
}: { 
  column: any; 
  row?: any; 
  rowIndex: number 
}): string {
  const isOddRow = rowIndex % 2 === 1
  let className = ""

  // 根据列的分组设置背景色
  const parentLabel = column.parent?.label
  if (parentLabel === "产品信息") {
    className = isOddRow ? "product-cell-striped" : "product-cell"
  } else if (parentLabel === "指标基础信息") {
    className = isOddRow ? "indicator-cell-striped" : "indicator-cell"
  } else if (parentLabel === "指标使用情况") {
    className = isOddRow ? "usage-cell-striped" : "usage-cell"
  } else {
    className = isOddRow ? "normal-cell-striped" : ""
  }

  return className
}

/**
 * 获取带状态错误处理的单元格样式类名
 * @param column 列配置对象
 * @param row 行数据对象
 * @param rowIndex 行索引
 * @param statusField 状态字段名
 * @param errorValues 错误状态值数组
 * @returns 样式类名字符串
 */
export function getGroupedCellClassNameWithStatus({ 
  column, 
  row, 
  rowIndex,
  statusField = "vcItStatus",
  errorValues = ["违规", "预警"]
}: { 
  column: any; 
  row: any; 
  rowIndex: number;
  statusField?: string;
  errorValues?: string[];
}): string {
  let className = getGroupedCellClassName({ column, row, rowIndex })

  // 状态错误处理
  if (row && errorValues.includes(row[statusField])) {
    // 如果当前列就是状态列，添加错误样式
    if (column.property === statusField) {
      className += " status-error"
    }
  }

  return className
}

/**
 * 表格分组配置类型定义
 */
export interface TableGroupConfig {
  /** 产品信息分组标签 */
  productLabel?: string
  /** 指标基础信息分组标签 */
  indicatorLabel?: string
  /** 指标使用情况分组标签 */
  usageLabel?: string
}

/**
 * 创建自定义分组的样式函数
 * @param config 分组配置
 * @returns 样式函数对象
 */
export function createGroupedTableStyleFunctions(config: TableGroupConfig = {}) {
  const {
    productLabel = "产品信息",
    indicatorLabel = "指标基础信息", 
    usageLabel = "指标使用情况"
  } = config

  return {
    getHeaderCellClassName: ({ column }: { column: any; columnIndex?: number }) => {
      if (column.level === 1) {
        const label = column.label
        if (label === productLabel) {
          return "product-group-header"
        } else if (label === indicatorLabel) {
          return "indicator-group-header"
        } else if (label === usageLabel) {
          return "usage-group-header"
        }
      }
      
      const parentLabel = column.parent?.label
      if (parentLabel === productLabel) {
        return "product-header"
      } else if (parentLabel === indicatorLabel) {
        return "indicator-header"
      } else if (parentLabel === usageLabel) {
        return "usage-header"
      }

      return ""
    },

    getCellClassName: ({ column, row, rowIndex }: { column: any; row?: any; rowIndex: number }) => {
      return getGroupedCellClassName({ column, row, rowIndex })
    },

    getCellClassNameWithStatus: ({ 
      column, 
      row, 
      rowIndex, 
      statusField, 
      errorValues 
    }: { 
      column: any; 
      row: any; 
      rowIndex: number;
      statusField?: string;
      errorValues?: string[];
    }) => {
      return getGroupedCellClassNameWithStatus({ 
        column, 
        row, 
        rowIndex, 
        statusField, 
        errorValues 
      })
    }
  }
}
