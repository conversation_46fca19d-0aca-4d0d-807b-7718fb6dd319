import { http } from "@/utils/http"

import type {
  InternalFormModel,
  InternalTableData,
} from "../types"

/**
 * 内部管理风险指标查询参数
 */
export interface InternalQueryParams extends InternalFormModel {
  page: number
  pageSize: number
}

/**
 * 内部管理风险指标查询响应
 */
export interface InternalQueryResponse {
  data: InternalTableData[]
  total: number
  page: number
  pageSize: number
}

/**
 * 获取内部管理风险指标列表
 */
export async function getInternalList(
  params: InternalQueryParams
): Promise<InternalQueryResponse> {
  const response = (await http.get("/api/risk-indicators/internal/list", {
    params,
  })) as any
  return response.data || response
}

/**
 * 导出内部管理风险指标数据
 */
export async function exportInternalData(
  params: InternalFormModel
): Promise<Blob> {
  const response = (await http.get("/api/risk-indicators/internal/export", {
    params,
    responseType: "blob",
  })) as any
  return response.data || response
}
