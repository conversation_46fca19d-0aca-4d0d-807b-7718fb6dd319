<script setup lang="ts">
import { createGroupedTableStyleFunctions } from "@/utils/tableStyleUtils"

import { useQueryForm } from "../../hooks/compliance/useQueryForm"

defineOptions({
  name: "ComplianceTable",
})

const { tableData, total, pagination, listLoading, handlePageChange, handlePageSizeChange } =
  useQueryForm()

// 使用通用的分组表格样式函数，配置 compliance 表格的列索引范围
const { getHeaderCellClassName, getCellClassName } = createGroupedTableStyleFunctions({
  columnRanges: {
    groupOne: [0, 5], // 第一组：5列（产品代码、产品名称、专户代码、专户名称、投资部门）
    groupTwo: [5, 11], // 第二组：6列（日期、指标编号、指标名称、资产代码、资产名称、限额值）
    groupThree: [11, Infinity], // 第三组：2列（监控数据、指标状态）
  },
})
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- 表格 -->
    <xq-table
      :data="tableData"
      :loading="listLoading"
      class="flex-1"
      :stripe="false"
      border
      :header-cell-class-name="getHeaderCellClassName"
      :cell-class-name="getCellClassName"
      style="width: 100%"
    >
      <!-- 产品信息分组 -->
      <xq-table-column label="产品信息" align="center">
        <xq-table-column prop="vcFundCode" label="产品代码" min-width="120" align="center" />
        <xq-table-column prop="vcFundName" label="产品名称" min-width="150" align="left" />
        <xq-table-column prop="vcFundCode" label="专户代码" min-width="100" align="center" />
        <xq-table-column prop="vcFundName" label="专户名称" min-width="100" align="center" />
        <xq-table-column prop="vcManagerName" label="投资部门" min-width="100" align="center" />
      </xq-table-column>

      <!-- 指标基础信息分组 -->
      <xq-table-column label="指标基础信息" align="center">
        <xq-table-column prop="vcItCode" label="日期" min-width="100" align="center" />
        <xq-table-column prop="vcItCode" label="指标编号" min-width="120" align="center" />
        <xq-table-column prop="vcItName" label="指标名称" min-width="200" align="left" />
        <xq-table-column prop="vcItCode" label="资产代码" min-width="100" align="center" />
        <xq-table-column prop="vcItName" label="资产名称" min-width="100" align="center" />
        <xq-table-column prop="vcOutlineNum" label="限额值" min-width="100" align="right" />
      </xq-table-column>

      <!-- 指标使用情况分组 -->
      <xq-table-column label="指标使用情况" align="center">
        <xq-table-column prop="fDactualMoney" label="监控数据" min-width="100" align="right" />
        <xq-table-column prop="vcItStatus" label="指标状态" min-width="100" align="center">
          <template #default="{ row }">
            <span
              :class="{ 'font-medium text-red-500': ['违规', '预警'].includes(row.vcItStatus) }"
            >
              {{ row.vcItStatus }}
            </span>
          </template>
        </xq-table-column>
      </xq-table-column>
    </xq-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <xq-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 组件特有样式可以在这里添加 */
</style>
