<script setup lang="ts">
// 异步导入组件
const AsyncInternalQueryForm = defineAsyncComponent(
  () => import("../components/internal/query-form.vue")
)
const AsyncInternalTable = defineAsyncComponent(() => import("../components/internal/table.vue"))

defineOptions({
  name: "RiskIndicatorsInternal",
})

/** 作为 `<suspend />` 加载，不用关闭 */
const isAsyncLoading = ref(true)
</script>

<template>
  <xq-card
    class="h-full !overflow-y-scroll border-none"
    body-class="!p-[12px] h-full"
    shadow="never"
  >
    <div class="flex flex-col h-full">
      <module-title title="内部管理类指标" />

      <!-- 查询表单 -->
      <div class="mt-[8px] bg-[var(--xq-color-info-light-9)] pt-[4px]">
        <suspense>
          <portal-target name="module-loading" :slot-props="{ height: 48 }" />
          <!-- <AsyncInternalQueryForm /> -->
          <template #fallback>
            <portal-target name="module-loading" :slot-props="{ height: 48 }" />
          </template>
        </suspense>
      </div>

      <xq-divider />

      <module-panel title="筛选结果" class="flex flex-col flex-1">
        <!-- 数据表格 -->
        <div class="flex items-center flex-1 overflow-hidden">
          <suspense>
            <AsyncInternalTable />
            <template #fallback>
              <portal-target name="module-loading" />
            </template>
          </suspense>
        </div>
      </module-panel>
    </div>
  </xq-card>

  <portal v-slot="{ height }" to="module-loading">
    <module-loading v-model="isAsyncLoading" :height="height" />
  </portal>
</template>
