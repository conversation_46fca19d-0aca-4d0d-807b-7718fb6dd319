<script setup lang="ts">
import { useQueryForm } from "../../hooks/internal/useQueryForm"

defineOptions({
  name: "InternalTable",
})

const { tableData, total, pagination, listLoading, handlePageChange, handlePageSizeChange } =
  useQueryForm()
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- 表格 -->
    <xq-table :data="tableData" :loading="listLoading" class="flex-1" stripe border>
      <xq-table-column prop="vcFundCode" label="产品代码" width="120" />
      <xq-table-column prop="vcFundName" label="产品名称" width="150" />
      <xq-table-column prop="vcZhCode" label="专户代码" width="120" />
      <xq-table-column prop="vcZhName" label="专户名称" width="150" />
      <xq-table-column prop="vcManagerName" label="投资经理" width="100" />
      <xq-table-column prop="vcDeptName" label="投资部门" width="120" />
      <xq-table-column prop="vcItCode" label="指标编号" width="120" />
      <xq-table-column prop="vcItName" label="指标名称" width="200" />
      <xq-table-column prop="vcItStatus" label="指标状态" width="100" />
      <xq-table-column prop="dtDate" label="日期" width="120" />
      <xq-table-column prop="nItValue" label="指标值" width="100" />
      <xq-table-column prop="nItLimit" label="指标限制" width="100" />
    </xq-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <xq-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
