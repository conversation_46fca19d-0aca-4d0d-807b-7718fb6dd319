<script setup lang="ts">
import type { CSSProperties } from "vue"
import { hasAuth, hasEveryAuth, getAuths } from "@/utils/auth"

defineOptions({
  name: "PermissionButton",
})

const elStyle = computed((): CSSProperties => {
  return {
    width: "85vw",
    justifyContent: "start",
  }
})
</script>

<template>
  <xq-space direction="vertical" size="large">
    <xq-card shadow="never" :style="elStyle" header="当前拥有的权限列表">
      <pre>{{ getAuths() }}</pre>
    </xq-card>

    <xq-card shadow="never" :style="elStyle">
      <template #header>
        <div class="card-header">组件方式判断权限</div>
      </template>
      <Auth :value="['app:role:add']">
        <xq-button type="primary" class="mb-10">拥有code：['app:role:add'] 权限可见</xq-button>
      </Auth>
      <Auth :value="['app:role:add', 'app:role:edit', 'app:role:delete']">
        <xq-button type="danger" class="mb-10">
          拥有code：['app:role:add', 'app:role:edit', 'app:role:delete'] 的任意一个权限就可见
        </xq-button>
      </Auth>
      <Auth :value="['app:role:add', 'app:role:edit', 'app:role:delete']" every>
        <xq-button type="danger" class="mb-10">
          拥有code：['app:role:add', 'app:role:edit', 'app:role:delete'] 权限才可见
        </xq-button>
      </Auth>
    </xq-card>

    <xq-card shadow="never" :style="elStyle">
      <template #header>
        <div class="card-header">函数方式判断权限</div>
      </template>
      <xq-button v-if="hasAuth(['app:role:edit'])" type="primary" class="mb-10">
        拥有code：['app:role:edit'] 权限可见
      </xq-button>
      <xq-button
        v-if="hasAuth(['app:role:add', 'app:role:edit', 'app:role:delete'])"
        type="danger"
        class="mb-10"
      >
        拥有code：['app:role:add', 'app:role:edit', 'app:role:delete'] 的任意一个权限就可见
      </xq-button>
      <xq-button
        v-if="hasEveryAuth(['app:role:add', 'app:role:edit', 'app:role:delete'])"
        class="mb-10"
        type="danger"
      >
        拥有code：['app:role:add', 'app:role:edit', 'app:role:delete'] 权限可见
      </xq-button>
    </xq-card>

    <xq-card shadow="never" :style="elStyle">
      <template #header>
        <div class="card-header">指令方式判断权限（该方式不能动态修改权限）</div>
      </template>
      <xq-button v-auth="['app:role:edit']" type="primary" class="mb-10">
        拥有code：['app:role:edit'] 权限可见
      </xq-button>
      <xq-button
        v-auth.every="['app:role:add', 'app:role:edit', 'app:role:delete']"
        type="danger"
        class="mb-10"
      >
        拥有code：['app:role:add', 'app:role:edit', 'app:role:delete'] 的任意一个权限就可见
      </xq-button>
      <xq-button
        v-auth="['app:role:add', 'app:role:edit', 'app:role:delete']"
        type="danger"
        class="mb-10"
      >
        拥有code：['app:role:add', 'app:role:edit', 'app:role:delete'] 权限可见
      </xq-button>
    </xq-card>
  </xq-space>
</template>
