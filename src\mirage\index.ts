// MirageJS Mock 服务主入口
import { createServer } from "miragejs"

import * as initData from "./init-data"
import { createSpreadAnalysisRoutes } from "./spread-analysis"
import { createSystemRoutes } from "./system"

export function makeServer({ environment = "development" } = {}) {
  const server = createServer({
    environment,

    // 配置 MirageJS 拦截设置
    timing: 200, // 模拟网络延迟（毫秒）

    routes() {
      // 设置 MirageJS 的命名空间，确保正确拦截请求
      this.namespace = ""

      // 注册系统基础功能路由（登录、权限等）
      createSystemRoutes(this)

      // 注册利差分析业务路由
      createSpreadAnalysisRoutes(this)

      // 确保 passthrough 其他未定义的请求到实际服务器
      this.passthrough()
    },
  })

  server.db.loadData(initData)

  return server
}
