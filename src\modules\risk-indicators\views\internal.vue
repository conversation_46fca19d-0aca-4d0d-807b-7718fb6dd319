<script setup lang="ts">
// 异步导入组件
const InternalQueryForm = defineAsyncComponent(
  () => import("../components/internal/query-form.vue")
)
const InternalTable = defineAsyncComponent(() => import("../components/internal/table.vue"))

defineOptions({
  name: "RiskIndicatorsInternal",
})
</script>

<template>
  <xq-card
    class="h-full !overflow-y-scroll border-none"
    body-class="!p-[12px] h-full"
    shadow="never"
  >
    <div class="flex h-full flex-col">
      <module-title title="内部管理类指标" />

      <!-- 查询表单 -->
      <InternalQueryForm />

      <xq-divider />

      <module-panel title="筛选结果">
        <!-- 数据表格 -->
        <InternalTable />
      </module-panel>
    </div>
  </xq-card>
</template>
