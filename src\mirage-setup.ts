// MirageJS setup for development
let mirageInitialized = false

export async function initializeMirageJS() {
  const shouldStartMirage =
    import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === "on"

  if (shouldStartMirage && !mirageInitialized) {
    try {
      const { makeServer } = await import("./mirage/index")
      const server = makeServer()

      // 添加全局引用以便调试
      ;(window as any).mirageServer = server
      mirageInitialized = true

      return server
    } catch (error) {
      console.error("Failed to start MirageJS Server:", error)
      throw error
    }
  } else if (mirageInitialized) {
    return (window as any).mirageServer
  } else {
    return null
  }
}

// 立即初始化（用于非qiankun环境）
if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === "on") {
  initializeMirageJS().catch(console.error)
}
