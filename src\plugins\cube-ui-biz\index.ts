import { cubeUIBizNS } from "@/namespace"
import type { App } from "vue"
import {
  Checkbox,
  Radio,
  Select,
  RadioDict,
  CheckboxDict,
  SelectDict,
  InputEllipsis,
  Steps,
} from "@cube-ui/biz"

const components = [
  Checkbox,
  Radio,
  Select,
  RadioDict,
  CheckboxDict,
  SelectDict,
  InputEllipsis,
  Steps,
]

export function cubeUIBiz(app: App): App {
  components.forEach(c => {
    app.use(c, { namespace: cubeUIBizNS })
  })

  return app
}
