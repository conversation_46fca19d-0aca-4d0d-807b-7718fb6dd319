// 系统基础功能模块 - 登录、权限、路由等
export const createSystemRoutes = (server: any) => {
  // 登录接口
  server.post("/login", (schema: any, request: any) => {
    const {
      jobNo,
      username,
      password: _password,
    } = JSON.parse(request.requestBody)
    const user = jobNo || username || "user"

    if (user === "admin") {
      return {
        success: true,
        data: {
          username: "admin",
          roles: ["admin"],
          accessToken: "eyJhbGciOiJIUzUxMiJ9.admin",
          refreshToken: "eyJhbGciOiJIUzUxMiJ9.adminRefresh",
          expires: "2025/12/30 00:00:00",
        },
      }
    } else {
      return {
        success: true,
        data: {
          username: user,
          roles: ["common"],
          accessToken: "eyJhbGciOiJIUzUxMiJ9.common",
          refreshToken: "eyJhbGciOiJIUzUxMiJ9.commonRefresh",
          expires: "2025/12/30 00:00:00",
        },
      }
    }
  })

  // 刷新token接口
  server.post("/refreshToken", (schema: any, request: any) => {
    const { refreshToken } = JSON.parse(request.requestBody)

    if (refreshToken) {
      return {
        success: true,
        data: {
          accessToken: "newAccessToken_" + Date.now(),
          refreshToken: "newRefreshToken_" + Date.now(),
          expires: "2025/12/30 23:59:59",
        },
      }
    } else {
      return {
        success: false,
        data: {},
        message: "RefreshToken无效",
      }
    }
  })

  // 获取用户权限
  server.get("/user/permissions", (schema: any, request: any) => {
    const { appCode } = request.queryParams

    const response = {
      success: true,
      code: 10200,
      data: {
        appCode: appCode || "cube",
        menuPermissions: [
          {
            menuUrl: "/role",
            permissionCacheDto: {
              buttons: ["app:role:add", "app:role:delete", "app:role:edit"],
              apis: [],
            },
          },
          {
            menuUrl: "/user",
            permissionCacheDto: {
              buttons: ["app:user:add", "app:user:delete", "app:user:edit"],
              apis: [],
            },
          },
          {
            menuUrl: "/spread-analysis",
            permissionCacheDto: {
              buttons: [
                "spread:view:add",
                "spread:view:edit",
                "spread:view:delete",
                "spread:export:excel",
                "spread:analysis:advanced",
              ],
              apis: [
                "spread:data:query",
                "spread:data:export",
                "spread:config:save",
              ],
            },
          },
        ],
      },
    }

    return response
  })

  // 获取异步路由
  server.get("/getAsyncRoutes", () => {
    return {
      success: true,
      data: [
        {
          path: "/permission",
          meta: {
            title: "权限管理",
            icon: "xq-icon-dashboard",
            rank: 10,
          },
          children: [
            {
              path: "/permission/button/index",
              name: "PermissionButton",
              meta: {
                title: "按钮权限",
                roles: ["admin", "common"],
                auths: ["btn_add", "btn_edit", "btn_delete"],
              },
            },
          ],
        },
        {
          path: "/spread-analysis",
          meta: {
            title: "利差分析",
            icon: "xq-icon-chart",
            rank: 20,
          },
          children: [
            {
              path: "/spread-analysis/static-view",
              name: "SpreadAnalysisStatic",
              meta: {
                title: "静态利差分析",
                roles: ["admin", "common"],
                auths: ["spread_view", "spread_export"],
              },
            },
            {
              path: "/spread-analysis/dynamic-view",
              name: "SpreadAnalysisDynamic",
              meta: {
                title: "动态利差分析",
                roles: ["admin"],
                auths: ["spread_dynamic", "spread_advanced"],
              },
            },
          ],
        },
      ],
    }
  })
}
