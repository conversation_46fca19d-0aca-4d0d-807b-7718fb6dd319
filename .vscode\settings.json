{
  "editor.formatOnType": true,
  "editor.formatOnSave": true,  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.wordWrap": "on",
    "editor.wordWrapColumn": 80,
    "editor.rulers": [80, 120],
    "editor.formatOnSave": true
  },
  "editor.tabSize": 2,
  "editor.formatOnPaste": true,
  "editor.guides.bracketPairs": "active",
  "files.autoSave": "onFocusChange",
  "git.confirmSync": false,
  "workbench.startupEditor": "newUntitledFile",
  "editor.suggestSelection": "first",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "css.lint.propertyIgnoredDueToDisplay": "ignore",
  "editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  "files.associations": {
    "editor.snippetSuggestions": "top"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "iconify.excludes": [
    "el"
  ],
  "cSpell.words": [
    "qiankun",
    "unplugin",
    "vueuse",
    "xquant"
  ],
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.preferences.includeCompletionsForModuleExports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",  "favorites.resources": [
    {
      "filePath": "src\\modules\\spread-analysis",
      "group": "Default"
    }
  ],

  // Tailwind CSS 专用设置
  "tailwindCSS.classAttributes": [
    "class",
    "className",
    "ngClass"
  ],
  "tailwindCSS.emmetCompletions": true,
  "tailwindCSS.includeLanguages": {
    "vue": "html",
    "html": "html"
  },

  // HTML 属性格式化
  "html.format.wrapAttributesIndentSize": 2,
  "html.format.wrapAttributes": "force-aligned",
  "html.format.maxPreserveNewLines": 2,
  "github.copilot.advanced": {
    "debug.useHttps": true
  }
}
