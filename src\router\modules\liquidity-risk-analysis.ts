import Layout from "@/layout/LayoutProxy"

export default {
  path: "/liquidity-risk-analysis",
  name: "LiquidityRiskAnalysis",
  component: Layout,
  redirect: "/liquidity-risk-analysis/index",
  meta: {
    icon: "homeFilled",
    title: "流动性风险分析",
  },
  children: [
    {
      path: "/liquidity-risk-analysis/index",
      name: "LiquidityRiskAnalysisIndex",
      component: () =>
        import("@/modules/liquidity-risk-analysis/views/index.vue"),
      meta: {
        title: "流动性风险分析",
      },
    },
  ],
} as RouteConfigsTable
