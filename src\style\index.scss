*,
::before,
::after {
  box-sizing: border-box;
}

html,
body,
#{$app} {
  height: 100%;
}

/* ----------------------------- x-ui-plus 样式覆盖 ----------------------------- */
.#{$ns}-dialog__body {
  padding: 16px 24px;
}

/* ---------------------------------- 公共样式 ---------------------------------- */
.check-tip {
  .#{$ns}-message-box__content,
  .#{$ns}-message-box__container,
  .#{$ns}-message-box__message {
    width: 100%;
  }
  .#{$ns}-message-box__header {
    padding-bottom: 12px!important;
  }
  .#{$ns}-message-box__headerbtn {
    height: 40px;
  }
  .#{$ns}-message-box__title {
    font-size: 14px;
  }
  &:has(.#{$ns}-message-box__status) .#{$ns}-message-box__title,
  &:has(.#{$ns}-message-box__status) .#{$ns}-message-box__message {
    padding-left: 20px!important;
  }
  .#{$ns}-message-box__message {
    padding-left: 0!important;
  }
  .#{$ns}-message-box__status {
    top: 18px;
    font-size: 16px;
  }
}

/* --------------------------------- 分组表格背景色 -------------------------------- */

/* 第一组样式 - 蓝色系 */
.group-one-header {
  background-color: #6b7fd7 !important;
  color: white !important;
  font-weight: 500;
  &.#{$ns}-table__cell > .cell {
    text-align: center;
  }
}

.one-header {
  background-color: #6b7fd7 !important;
  color: white !important;
  font-weight: 500;
  &.#{$ns}-table__cell>.cell {
    text-align: center;
  }
}

.one-cell {
  background-color: #c7d2fe !important;
  color: #374151;
}

.one-cell-striped {
  background-color: #a5b4fc !important;
  color: #374151;
}

/* 第二组样式 - 橙色系 */
.group-two-header {
  background-color: #fb923c !important;
  color: white !important;
  font-weight: 500;
  &.#{$ns}-table__cell>.cell {
    text-align: center;
  }
}

.two-header {
  background-color: #fb923c !important;
  color: white !important;
  font-weight: 500;
  &.#{$ns}-table__cell>.cell {
    text-align: center;
  }
}

.two-cell {
  background-color: #fed7aa !important;
  color: #374151;
}

.two-cell-striped {
  background-color: #fdba74 !important;
  color: #374151;
}

/* 第三组样式 - 绿色系 */
.group-three-header {
  background-color: #4ade80 !important;
  color: white !important;
  font-weight: 500;
  &.#{$ns}-table__cell>.cell {
    text-align: center;
  }
}

.three-header {
  background-color: #4ade80 !important;
  color: white !important;
  font-weight: 500;
  &.#{$ns}-table__cell>.cell {
    text-align: center;
  }
}

.three-cell {
  background-color: #bbf7d0 !important;
  color: #374151;
}

.three-cell-striped {
  background-color: #86efac !important;
  color: #374151;
}



/* 普通列的斑马纹 */
.normal-cell-striped {
  background-color: #f9fafb !important;
}

/* 悬停效果 */
:deep(.#{$ns}-table__row:hover td) {
  background-color: rgba(59, 130, 246, 0.05) !important;
}

/* 表头居中对齐 */
:deep(.#{$ns}-table__header .#{$ns}-table__cell > .cell) {
  text-align: center;
  justify-content: center;
}

/* 分组表头居中对齐 */
:deep(.#{$ns}-table__header-wrapper .#{$ns}-table__header .#{$ns}-table__cell) {
  text-align: center;
}

/* 确保表格占满容器宽度 */
:deep(.#{$ns}-table) {
  width: 100% !important;
}

