# MirageJS Mock 服务模块化架构

## 📁 目录结构

```
src/mirage/
├── index.ts              # 主入口文件，整合所有模块
├── system.ts             # 系统基础功能（登录、权限、路由）
├── spread-analysis.ts    # 利差分析业务模块
└── README.md            # 本文档
```

## 🚀 使用方式

### 启动服务
Mock 服务会在开发环境下自动启动（当 `VITE_USE_MOCK=on` 时）。

### 添加新的业务模块

1. **创建新的模块文件**：
   ```typescript
   // src/mirage/your-module.ts
   export const createYourModuleRoutes = (server: any) => {
     server.get("/api/your-module/data", () => {
       return { success: true, data: [] }
     })
   }
   ```

2. **在主入口文件中注册**：
   ```typescript
   // src/mirage/index.ts
   import { createYourModuleRoutes } from "./your-module"
   
   export function makeServer() {
     return createServer({
       routes() {
         createSystemRoutes(this)
         createSpreadAnalysisRoutes(this)
         createYourModuleRoutes(this)  // 新增
       }
     })
   }
   ```

## 📋 已有模块

### System 模块 (`system.ts`)
提供系统基础功能接口：
- `POST /login` - 用户登录
- `POST /refreshToken` - 刷新token
- `GET /user/permissions` - 获取用户权限
- `GET /getAsyncRoutes` - 获取异步路由

### Spread Analysis 模块 (`spread-analysis.ts`)
提供利差分析业务接口：
- `GET /api/spread-analysis/data` - 获取利差数据列表
- `GET /api/spread-analysis/trend` - 获取利差趋势数据
- `GET /api/spread-analysis/report/:bondCode` - 获取利差分析报告
- `POST /api/spread-analysis/views` - 保存自定义视图
- `GET /api/spread-analysis/views` - 获取自定义视图列表
- `POST /api/spread-analysis/export` - 导出利差数据
- `GET /api/spread-analysis/export/:taskId` - 查询导出任务状态

## 🎯 设计原则

1. **模块化分离**：按业务领域划分模块，便于维护
2. **统一入口**：通过 `index.ts` 统一管理所有模块
3. **数据真实性**：使用 Faker.js 生成接近真实的模拟数据
4. **API 规范**：业务接口统一使用 `/api/` 前缀
5. **调试友好**：添加详细的控制台日志

## 🔧 开发建议

- 新业务模块建议以业务领域命名（如 `bond-assets.ts`, `risk-management.ts`）
- 每个模块导出一个 `create[ModuleName]Routes` 函数
- 使用 TypeScript 类型定义确保接口一致性
- 为复杂的模拟数据添加注释说明
