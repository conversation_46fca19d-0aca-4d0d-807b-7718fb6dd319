import Layout from "@/layout/LayoutProxy"

export default {
  path: "/risk-warnings",
  name: "RiskWarnings",
  component: Layout,
  redirect: "/risk-warnings/index",
  meta: {
    icon: "homeFilled",
    title: "风险预警信息总览",
  },
  children: [
    {
      path: "/risk-warnings/index",
      name: "RiskWarningsIndex",
      component: () => import("@/modules/risk-warnings/views/index.vue"),
      meta: {
        title: "风险预警信息总览",
      },
    },
  ],
} as RouteConfigsTable
