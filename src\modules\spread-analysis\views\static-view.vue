<script setup lang="ts">
import { CustomViewCodeEnum } from "@/constants"

import { SpreadPageTypeEnum } from "../consts"

defineOptions({
  name: "SpreadAnalysisStaticView",
})

const AsyncQueryForm = defineAsyncComponent(() => import("../components/query-form.vue"))
const AsyncCustomViewToolbar = defineAsyncComponent(
  () => import("@/components/custom-view-toolbar/index.vue")
)
const AsyncChartConfig = defineAsyncComponent(() => import("../components/chart-config.vue"))
const AsyncChart = defineAsyncComponent(() => import("../components/chart.vue"))

/** 作为 `<suspend />` 加载，不用关闭 */
const isAsyncLoading = ref(true)

/** 分割面板左侧默认宽度 */
const split = ref("300px")
</script>

<template>
  <xq-card
    class="h-full !overflow-y-scroll border-none"
    body-class="!p-[12px] h-full"
    shadow="never"
  >
    <div class="flex h-full flex-col">
      <!-- /* ---------------------------------- 查询表单 ---------------------------------- */ -->
      <module-title title="自定义数据筛选维度" />
      <div class="mt-[8px] bg-[var(--xq-color-info-light-9)] pt-[4px]">
        <suspense>
          <AsyncQueryForm />
          <template #fallback>
            <portal-target name="module-loading" :slot-props="{ height: 48 }" />
          </template>
        </suspense>
      </div>

      <!-- /* ---------------------------------- 视图模板 ---------------------------------- */ -->
      <div class="border-y-solid my-[12px] border-y-[1px] border-y-[#e5e5e5]">
        <suspense>
          <AsyncCustomViewToolbar :view-code="CustomViewCodeEnum.VIEW_1" />
          <template #fallback>
            <portal-target name="module-loading" :slot-props="{ height: 48 }" />
          </template>
        </suspense>
      </div>

      <div class="flex-1 overflow-hidden">
        <xq-split v-model="split" trigger-size="1px" min="280px">
          <template #left>
            <!-- /* --------------------------------- 表格维度配置 --------------------------------- */ -->
            <div class="flex h-full flex-col">
              <module-title title="自定义数据展示维度" class="mr-[12px]" />
              <div
                class="mt-[8px] mr-[12px] flex flex-1 items-center justify-center bg-[var(--xq-color-info-light-9)] pt-[4px] pr-[12px]"
              >
                <suspense>
                  <AsyncChartConfig :page="SpreadPageTypeEnum.DYNAMIC" />
                  <template #fallback>
                    <portal-target name="module-loading" />
                  </template>
                </suspense>
              </div>
            </div>
          </template>

          <template #right>
            <div class="flex h-full flex-col">
              <div class="flex flex-1 items-center justify-center overflow-hidden">
                <suspense>
                  <AsyncChart :page="SpreadPageTypeEnum.DYNAMIC" />
                  <template #fallback>
                    <portal-target name="module-loading" />
                  </template>
                </suspense>
              </div>
            </div>
          </template>
        </xq-split>
      </div>
    </div>
  </xq-card>

  <portal v-slot="{ height }" to="module-loading">
    <module-loading v-model="isAsyncLoading" :height="height" />
  </portal>
</template>

<style scoped lang="scss">
:deep(.#{$ns}-split-trigger-vertical) {
  --xq-trigger-width: 1px;
  & .#{$ns}-split-trigger-bar-con {
    display: none;
  }
}
</style>
