{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "Node", "strict": true, "noImplicitAny": false, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "strictFunctionTypes": false, "skipLibCheck": true, "esModuleInterop": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "baseUrl": ".", "allowJs": false, "resolveJsonModule": true, "lib": ["dom", "esnext"], "paths": {"@/*": ["src/*"], "@build/*": ["build/*"]}, "types": ["node", "vite/client", "@xquant/x-ui-plus/global"]}, "include": ["mock/*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/*.d.ts", "vite.config.ts", "auto-imports.d.ts", "components.d.ts"], "exclude": ["dist", "**/*.js", "node_modules"]}