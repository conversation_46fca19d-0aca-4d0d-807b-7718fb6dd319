/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module "vue" {
  export interface GlobalComponents {
    RouterLink: typeof import("vue-router")["RouterLink"]
    RouterView: typeof import("vue-router")["RouterView"]
    XqAffix: typeof import("@xquant/x-ui-plus/es")["XqAffix"]
    XqAlert: typeof import("@xquant/x-ui-plus/es")["XqAlert"]
    XqAnchor: typeof import("@xquant/x-ui-plus/es")["XqAnchor"]
    XqAnchorLink: typeof import("@xquant/x-ui-plus/es")["XqAnchorLink"]
    XqAside: typeof import("@xquant/x-ui-plus/es")["XqAside"]
    XqAutocomplete: typeof import("@xquant/x-ui-plus/es")["XqAutocomplete"]
    XqAutoResizer: typeof import("@xquant/x-ui-plus/es")["XqAutoResizer"]
    XqAvatar: typeof import("@xquant/x-ui-plus/es")["XqAvatar"]
    XqBacktop: typeof import("@xquant/x-ui-plus/es")["XqBacktop"]
    XqBadge: typeof import("@xquant/x-ui-plus/es")["XqBadge"]
    XqBreadcrumb: typeof import("@xquant/x-ui-plus/es")["XqBreadcrumb"]
    XqBreadcrumbItem: typeof import("@xquant/x-ui-plus/es")["XqBreadcrumbItem"]
    XqButton: typeof import("@xquant/x-ui-plus/es")["XqButton"]
    XqButtonGroup: typeof import("@xquant/x-ui-plus/es")["XqButtonGroup"]
    XqCalendar: typeof import("@xquant/x-ui-plus/es")["XqCalendar"]
    XqCard: typeof import("@xquant/x-ui-plus/es")["XqCard"]
    XqCarousel: typeof import("@xquant/x-ui-plus/es")["XqCarousel"]
    XqCarouselItem: typeof import("@xquant/x-ui-plus/es")["XqCarouselItem"]
    XqCascader: typeof import("@xquant/x-ui-plus/es")["XqCascader"]
    XqCheckbox: typeof import("@xquant/x-ui-plus/es")["XqCheckbox"]
    XqCheckboxButton: typeof import("@xquant/x-ui-plus/es")["XqCheckboxButton"]
    XqCheckboxGroup: typeof import("@xquant/x-ui-plus/es")["XqCheckboxGroup"]
    XqCol: typeof import("@xquant/x-ui-plus/es")["XqCol"]
    XqCollapse: typeof import("@xquant/x-ui-plus/es")["XqCollapse"]
    XqCollapseBlock: typeof import("@xquant/x-ui-plus/es")["XqCollapseBlock"]
    XqCollapseFormBlock: typeof import("@xquant/x-ui-plus/es")["XqCollapseFormBlock"]
    XqCollapseItem: typeof import("@xquant/x-ui-plus/es")["XqCollapseItem"]
    XqColorPicker: typeof import("@xquant/x-ui-plus/es")["XqColorPicker"]
    XqConfigProvider: typeof import("@xquant/x-ui-plus/es")["XqConfigProvider"]
    XqContainer: typeof import("@xquant/x-ui-plus/es")["XqContainer"]
    XqCountdown: typeof import("@xquant/x-ui-plus/es")["XqCountdown"]
    XqDatePicker: typeof import("@xquant/x-ui-plus/es")["XqDatePicker"]
    XqDescriptions: typeof import("@xquant/x-ui-plus/es")["XqDescriptions"]
    XqDescriptionsItem: typeof import("@xquant/x-ui-plus/es")["XqDescriptionsItem"]
    XqDialog: typeof import("@xquant/x-ui-plus/es")["XqDialog"]
    XqDivider: typeof import("@xquant/x-ui-plus/es")["XqDivider"]
    XqDragger: typeof import("@xquant/x-ui-plus/es")["XqDragger"]
    XqDrawer: typeof import("@xquant/x-ui-plus/es")["XqDrawer"]
    XqDropdown: typeof import("@xquant/x-ui-plus/es")["XqDropdown"]
    XqDropdownItem: typeof import("@xquant/x-ui-plus/es")["XqDropdownItem"]
    XqDropdownMenu: typeof import("@xquant/x-ui-plus/es")["XqDropdownMenu"]
    XqEditTableColumn: typeof import("@xquant/x-ui-plus/es")["XqEditTableColumn"]
    XqEmpty: typeof import("@xquant/x-ui-plus/es")["XqEmpty"]
    XqFieldSelectTree: typeof import("@xquant/x-ui-plus/es")["XqFieldSelectTree"]
    XqFloatButton: typeof import("@xquant/x-ui-plus/es")["XqFloatButton"]
    XqFloatButtonItem: typeof import("@xquant/x-ui-plus/es")["XqFloatButtonItem"]
    XqFooter: typeof import("@xquant/x-ui-plus/es")["XqFooter"]
    XqForm: typeof import("@xquant/x-ui-plus/es")["XqForm"]
    XqFormItem: typeof import("@xquant/x-ui-plus/es")["XqFormItem"]
    XqFormItems: typeof import("@xquant/x-ui-plus/es")["XqFormItems"]
    XqHeader: typeof import("@xquant/x-ui-plus/es")["XqHeader"]
    XqHeaderTitle: typeof import("@xquant/x-ui-plus/es")["XqHeaderTitle"]
    XqIcon: typeof import("@xquant/x-ui-plus/es")["XqIcon"]
    XqImage: typeof import("@xquant/x-ui-plus/es")["XqImage"]
    XqInput: typeof import("@xquant/x-ui-plus/es")["XqInput"]
    XqInputAmount: typeof import("@xquant/x-ui-plus/es")["XqInputAmount"]
    XqInputNumber: typeof import("@xquant/x-ui-plus/es")["XqInputNumber"]
    XqLink: typeof import("@xquant/x-ui-plus/es")["XqLink"]
    XqList: typeof import("@xquant/x-ui-plus/es")["XqList"]
    XqListItem: typeof import("@xquant/x-ui-plus/es")["XqListItem"]
    XqListItemMeta: typeof import("@xquant/x-ui-plus/es")["XqListItemMeta"]
    XqMain: typeof import("@xquant/x-ui-plus/es")["XqMain"]
    XqMenu: typeof import("@xquant/x-ui-plus/es")["XqMenu"]
    XqMenuItem: typeof import("@xquant/x-ui-plus/es")["XqMenuItem"]
    XqMore: typeof import("@xquant/x-ui-plus/es")["XqMore"]
    XqOption: typeof import("@xquant/x-ui-plus/es")["XqOption"]
    XqPageHeader: typeof import("@xquant/x-ui-plus/es")["XqPageHeader"]
    XqPagination: typeof import("@xquant/x-ui-plus/es")["XqPagination"]
    XqPopconfirm: typeof import("@xquant/x-ui-plus/es")["XqPopconfirm"]
    XqPopover: typeof import("@xquant/x-ui-plus/es")["XqPopover"]
    XqProgress: typeof import("@xquant/x-ui-plus/es")["XqProgress"]
    XqProTable: typeof import("@xquant/x-ui-plus/es")["XqProTable"]
    XqRadio: typeof import("@xquant/x-ui-plus/es")["XqRadio"]
    XqRadioButton: typeof import("@xquant/x-ui-plus/es")["XqRadioButton"]
    XqRadioGroup: typeof import("@xquant/x-ui-plus/es")["XqRadioGroup"]
    XqRate: typeof import("@xquant/x-ui-plus/es")["XqRate"]
    XqResult: typeof import("@xquant/x-ui-plus/es")["XqResult"]
    XqRow: typeof import("@xquant/x-ui-plus/es")["XqRow"]
    XqScrollbar: typeof import("@xquant/x-ui-plus/es")["XqScrollbar"]
    XqSegmented: typeof import("@xquant/x-ui-plus/es")["XqSegmented"]
    XqSelect: typeof import("@xquant/x-ui-plus/es")["XqSelect"]
    XqSelectField: typeof import("@xquant/x-ui-plus/es")["XqSelectField"]
    XqSelectV2: typeof import("@xquant/x-ui-plus/es")["XqSelectV2"]
    XqSkeleton: typeof import("@xquant/x-ui-plus/es")["XqSkeleton"]
    XqSlider: typeof import("@xquant/x-ui-plus/es")["XqSlider"]
    XqSpace: typeof import("@xquant/x-ui-plus/es")["XqSpace"]
    XqSplit: typeof import("@xquant/x-ui-plus/es")["XqSplit"]
    XqStatistic: typeof import("@xquant/x-ui-plus/es")["XqStatistic"]
    XqStep: typeof import("@xquant/x-ui-plus/es")["XqStep"]
    XqSteps: typeof import("@xquant/x-ui-plus/es")["XqSteps"]
    XqSubMenu: typeof import("@xquant/x-ui-plus/es")["XqSubMenu"]
    XqSwitch: typeof import("@xquant/x-ui-plus/es")["XqSwitch"]
    XqTable: typeof import("@xquant/x-ui-plus/es")["XqTable"]
    XqTableColumn: typeof import("@xquant/x-ui-plus/es")["XqTableColumn"]
    XqTableV2: typeof import("@xquant/x-ui-plus/es")["XqTableV2"]
    XqTabPane: typeof import("@xquant/x-ui-plus/es")["XqTabPane"]
    XqTabs: typeof import("@xquant/x-ui-plus/es")["XqTabs"]
    XqTag: typeof import("@xquant/x-ui-plus/es")["XqTag"]
    XqText: typeof import("@xquant/x-ui-plus/es")["XqText"]
    XqTimeline: typeof import("@xquant/x-ui-plus/es")["XqTimeline"]
    XqTimelineItem: typeof import("@xquant/x-ui-plus/es")["XqTimelineItem"]
    XqTimePicker: typeof import("@xquant/x-ui-plus/es")["XqTimePicker"]
    XqTimeSelect: typeof import("@xquant/x-ui-plus/es")["XqTimeSelect"]
    XqToolbar: typeof import("@xquant/x-ui-plus/es")["XqToolbar"]
    XqTooltip: typeof import("@xquant/x-ui-plus/es")["XqTooltip"]
    XqTour: typeof import("@xquant/x-ui-plus/es")["XqTour"]
    XqTourStep: typeof import("@xquant/x-ui-plus/es")["XqTourStep"]
    XqTransfer: typeof import("@xquant/x-ui-plus/es")["XqTransfer"]
    XqTree: typeof import("@xquant/x-ui-plus/es")["XqTree"]
    XqTreeSelect: typeof import("@xquant/x-ui-plus/es")["XqTreeSelect"]
    XqTreeV2: typeof import("@xquant/x-ui-plus/es")["XqTreeV2"]
    XqUpload: typeof import("@xquant/x-ui-plus/es")["XqUpload"]
    XqWatermark: typeof import("@xquant/x-ui-plus/es")["XqWatermark"]
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import("@xquant/x-ui-plus/es")["XqLoadingDirective"]
  }
}
