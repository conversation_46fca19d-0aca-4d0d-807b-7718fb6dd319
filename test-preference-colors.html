<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>偏好类指标表格颜色测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .color-demo {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .color-box {
            padding: 15px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            text-align: center;
            min-width: 150px;
        }
        
        /* 第一组样式 - 蓝色系 */
        .group-one-header {
            background-color: #6b7fd7;
        }
        
        /* 第二组样式 - 橙色系 */
        .group-two-header {
            background-color: #fb923c;
        }
        
        /* 第三组样式 - 绿色系 */
        .group-three-header {
            background-color: #4ade80;
        }
        
        .table-demo {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        
        .header-group-1 {
            background-color: #6b7fd7 !important;
            color: white;
            font-weight: 500;
        }
        
        .header-group-2 {
            background-color: #fb923c !important;
            color: white;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <h1>偏好类指标表格颜色配置测试</h1>
    
    <h2>颜色方案预览</h2>
    <div class="color-demo">
        <div class="color-box group-one-header">
            指标基础信息<br>
            (蓝色 #6b7fd7)
        </div>
        <div class="color-box group-two-header">
            指标使用情况<br>
            (橙色 #fb923c)
        </div>
        <div class="color-box group-three-header">
            扩展信息<br>
            (绿色 #4ade80)
        </div>
    </div>
    
    <div class="table-demo">
        <h3>偏好类指标表格布局</h3>
        <table>
            <thead>
                <tr>
                    <th colspan="5" class="header-group-1">指标基础信息</th>
                    <th colspan="2" class="header-group-2">指标使用情况</th>
                </tr>
                <tr>
                    <th>指标类型</th>
                    <th>指标名称</th>
                    <th>额度值</th>
                    <th>预警值</th>
                    <th>指标管理部门</th>
                    <th>实际值</th>
                    <th>指标状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>CCBL</td>
                    <td>投资额类指标</td>
                    <td>1000万</td>
                    <td>800万</td>
                    <td>风险管理部</td>
                    <td>750万</td>
                    <td style="color: #ef4444; font-weight: 500;">预警</td>
                </tr>
                <tr>
                    <td>MUTEX</td>
                    <td>联合风控类指标</td>
                    <td>500万</td>
                    <td>400万</td>
                    <td>投资管理部</td>
                    <td>300万</td>
                    <td style="color: #22c55e; font-weight: 500;">合规</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background: #e3f2fd; border-radius: 8px;">
        <h3>配置说明</h3>
        <ul>
            <li><strong>指标基础信息</strong>：使用蓝色系 (#6b7fd7)，包含 5 列</li>
            <li><strong>指标使用情况</strong>：使用橙色系 (#fb923c)，包含 2 列</li>
            <li><strong>列索引范围</strong>：第一组 [0, 5)，第二组 [5, 7)</li>
            <li><strong>状态颜色</strong>：违规/预警显示红色，合规显示绿色</li>
        </ul>
    </div>
</body>
</html>
