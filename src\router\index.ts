import type { RouterHistory } from "vue-router"
import { createRouter, RouteComponent, Router, RouteRecordRaw } from "vue-router"

import { APP, POWERED_BY_QIANKUN, VITE_AUTH } from "@/business/dictionary"
import { usePermissionStore } from "@/store/modules/permission"
import { type DataInfo, sessionKey } from "@/utils/auth"
import { storageSession } from "@/utils/legacy"
import NProgress from "@/utils/progress"
import { buildHierarchyTree } from "@/utils/tree"

import remainingRouter from "./modules/remaining"
import {
  ascending,
  formatFlatteningRoutes,
  formatTwoStageRoutes,
  initRouter,
  isOneOfArray,
} from "./utils"

/** 自动导入全部静态路由，无需再手动引入！匹配 src/router/modules 目录（任何嵌套级别）中具有 .ts 扩展名的所有文件，除了 remaining.ts 文件
 * 如何匹配所有文件请看：https://github.com/mrmlnc/fast-glob#basic-syntax
 * 如何排除文件请看：https://cn.vitejs.dev/guide/features.html#negative-patterns
 */
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
    eager: true,
  }
)

/** 原始静态路由（未做任何处理） */
const routes: Array<RouteRecordRaw> = []

Object.keys(modules).forEach(key => {
  routes.push(modules[key].default)
})

/** 导出处理后的静态路由（三级及以上的路由全部拍成二级） */
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
)

/** 用于渲染菜单，保持原始层级 */
export const constantMenus: Array<RouteComponent> = ascending(routes.flat(Infinity)).concat(
  ...remainingRouter
)

/** 不参与菜单的路由 */
export const remainingPaths = Object.keys(remainingRouter).map(v => {
  return remainingRouter[v].path
})

let router: Router
/** 创建路由实例 */
export const createRouterInstance = (history: RouterHistory) => {
  router = createRouter({
    history: history,
    routes: constantRoutes.concat(...(remainingRouter as any)),
    strict: true,
    scrollBehavior(to, from, savedPosition) {
      return new Promise(resolve => {
        if (savedPosition) {
          return savedPosition
        } else {
          if (from.meta.saveScrollTop) {
            const top: number = document.documentElement.scrollTop || document.body.scrollTop
            resolve({ left: 0, top })
          }
        }
      })
    },
  })
  attachRouterListener()
  return router
}

/** 重置路由 */
export function resetRouter() {
  flag = 1
  router.getRoutes().forEach(route => {
    const { name, meta } = route
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name)
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
      )
    }
  })
  usePermissionStore().clearAllCachePage()
}

let flag = 1

/**
 * 监听 router 相关的生命周期
 */
const attachRouterListener = () => {
  /** 路由白名单 */
  const whiteList = ["/login"]

  router.beforeEach(async (to: ToRouteType, _from, next) => {
    if (
      (POWERED_BY_QIANKUN && to.redirectedFrom && to.redirectedFrom.path == "/") ||
      to.path == "/login"
    )
      return next()
    const userInfo = storageSession().getItem<DataInfo<number>>(sessionKey)
    NProgress.start()
    /** 如果已经登录并存在登录信息后不能跳转到路由白名单，而是继续保持在当前页面 */
    function toCorrectRoute() {
      whiteList.includes(to.fullPath) ? next(_from.fullPath) : next()
    }
    if (flag == 1) {
      flag++
      await usePermissionStore().setPermissions(APP)
    }
    // 如果不开启认证授权验证，则只加载静态路由并跳转任何页面
    if (!VITE_AUTH) {
      usePermissionStore().handleWholeMenus([])
      next()
      return
    }
    if (userInfo) {
      // 无权限跳转403页面
      if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles)) {
        next({ path: "/error/403" })
      }
      if (usePermissionStore().wholeMenus.length === 0 && to.path !== "/login") {
        initRouter().then(() => {
          toCorrectRoute()
        })
      } else {
        toCorrectRoute()
      }
    } else {
      if (to.path !== "/login") {
        if (whiteList.indexOf(to.path) !== -1) {
          next()
        } else {
          next({ path: "/login" })
        }
      } else {
        next()
      }
    }
  })

  router.afterEach(() => {
    NProgress.done()
  })
}

const getRouter = () => router

export default getRouter
