import {
  CustomDynamicXAxisEnum,
  CustomFilterTabEnum,
  CustomStaticXAxisEnum,
  ExerciseDeadlineEnum,
  SpreadPageTypeEnum,
} from "./consts"

/**
 * 利差页面类型
 */
export type SpreadPageType =
  (typeof SpreadPageTypeEnum)[keyof typeof SpreadPageTypeEnum]

/** 自定义筛选条件窗口选项卡类型 */
export type CustomFilterTabType =
  (typeof CustomFilterTabEnum)[keyof typeof CustomFilterTabEnum]

/** 自定义主轴类型（静态） */
export type CustomStaticXAxisType =
  (typeof CustomStaticXAxisEnum)[keyof typeof CustomStaticXAxisEnum]

/** 自定义主轴类型（动态） */
export type CustomDynamicXAxisType =
  (typeof CustomDynamicXAxisEnum)[keyof typeof CustomDynamicXAxisEnum]

/** 自定义横轴类型 */
export type CustomXAxisType = CustomStaticXAxisType | CustomDynamicXAxisType

/** 行权期限类型 */
export type ExerciseDeadlineType =
  (typeof ExerciseDeadlineEnum)[keyof typeof ExerciseDeadlineEnum]

/**
 * 查询表单模型
 */
export interface QueryFormModel {
  /** 内部行业 */
  industry: string
  /** 债券代码 */
  bondCode: string[]
  /** 债券简称 */
  bondName: string[]
  /** 债券品种 */
  bondType: string[]
  /** 额度占用方 */
  quotaOccupy: string[]
  /** 发行方式 */
  issueMethod: string[]
  /** 行权期限 */
  exerciseDeadline: ""
  /** 企业性质 */
  enterpriseNature: string[]
  /** 内部评级 */
  internalRating: string
  /** 个性化标签 */
  personalizedTags: string
  /** 利差区间 */
  interestMarginRange: string[]
  /** 利差区间开始值 */
  interestMarginRangeStart: string
  /** 利差区间结束值 */
  interestMarginRangeEnd: string
  /** 查询日期 */
  queryDate: string
  /** 查询区间 */
  queryDateRange: [string, string]
  /** 查询区间开始值 */
  queryDateRangeStart: string
  /** 查询区间结束值 */
  queryDateRangeEnd: string
}

/**
 * 静态自定义数据展示维度配置
 */
export interface StaticChartFormModel {
  /** 横轴 */
  xAxis: CustomXAxisType
  /** 横轴：行权期限 */
  xAxisExerciseDeadline?: ExerciseDeadlineType
  /** 横轴：利差区间 */
  xAxisInterestMarginRange?: number[]
  /** 主纵轴 */
  yAxis?: string
  /** 次纵轴 */
  yAxisSecondary?: string
}

/**
 * 动态自定义数据展示维度配置
 */
export interface DynamicChartConfig {}
