# Mockoon API 模拟服务配置

本项目已从 MirageJS 迁移到 Mockoon 进行 API 模拟。

## 目录结构

```
mockoon/
├── unified-risk-platform.json    # Mockoon 环境配置文件
├── data/                          # 静态数据文件
│   ├── login-response.json        # 登录响应数据
│   ├── permissions.json           # 用户权限数据
│   ├── async-routes.json          # 异步路由数据
│   ├── compliance-list.json       # 合规指标数据
│   ├── internal-list.json         # 内部指标数据
│   ├── preference-list.json       # 偏好指标数据
│   └── spread-analysis-views.json # 利差分析视图数据
└── README.md                      # 本文档
```

## 使用方法

### 1. 安装 Mockoon

#### 桌面应用（推荐）
- 访问 https://mockoon.com/download/ 下载安装
- 或使用包管理器：
  ```bash
  # Windows (Chocolatey)
  choco install mockoon
  
  # macOS (Homebrew)
  brew install --cask mockoon
  ```

#### CLI 工具（CI/CD 使用）
```bash
npm install -g @mockoon/cli
```

### 2. 导入配置

1. 打开 Mockoon 桌面应用
2. 点击 "Import environment"
3. 选择 `mockoon/unified-risk-platform.json` 文件
4. 确认导入成功

### 3. 启动服务

1. 在 Mockoon 中选择 "Unified risk platform" 环境
2. 点击绿色的 "Start" 按钮
3. 确认服务运行在 `http://localhost:3001`

### 4. 配置项目

确保 `.env.development` 文件中设置：
```bash
VITE_USE_MOCK = on
```

### 5. 启动项目

```bash
pnpm run dev
```

现在所有 API 请求将通过 Mockoon 服务处理，您可以在浏览器网络面板中看到真实的 HTTP 请求。

## API 接口列表

### 系统接口
- `POST /login` - 用户登录
- `POST /refreshToken` - 刷新Token
- `GET /user/permissions` - 获取用户权限
- `GET /getAsyncRoutes` - 获取异步路由

### 风险指标接口
- `GET /api/risk-indicators/compliance/list` - 监管合规风险指标明细
- `GET /api/risk-indicators/internal/list` - 内部管理风险指标明细
- `GET /api/risk-indicators/preference/list` - 风险偏好指标明细

### 利差分析接口
- `GET /api/spread-analysis/views` - 获取视图列表
- `POST /api/spread-analysis/views` - 创建视图
- `DELETE /api/spread-analysis/views/*` - 删除视图

## 切换到真实接口

如需连接到真实的开发服务器：

1. 修改 `.env.development`：
   ```bash
   VITE_USE_MOCK = off
   ```

2. 重启项目：
   ```bash
   pnpm run dev
   ```

## 数据维护

### 修改响应数据
直接编辑 `mockoon/data/` 目录下的 JSON 文件，Mockoon 会自动重载数据。

### 添加新接口
1. 在 Mockoon 界面中点击 "Add route"
2. 配置接口信息（方法、路径、响应等）
3. 导出配置：File → Export environment
4. 覆盖 `mockoon/unified-risk-platform.json` 文件

## 团队协作

1. 修改配置后，将 `mockoon/unified-risk-platform.json` 提交到 Git
2. 团队成员重新导入配置文件到 Mockoon
3. 统一的 Mock 数据和接口定义

## 优势

- ✅ 在浏览器网络面板可见请求
- ✅ 可视化界面，易于维护
- ✅ 支持动态数据和模板
- ✅ 独立服务，更接近生产环境
- ✅ 支持团队协作和版本控制
