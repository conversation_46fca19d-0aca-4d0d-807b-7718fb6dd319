/**
 * 大文件下载功能使用示例
 * 展示在不同场景下如何使用下载工具
 */

import {
  downloadFile,
  isFileSystemAccessSupported,
  formatFileSize,
} from "@/utils/file-download"

/**
 * 示例1: 简单的文件下载
 */
export async function simpleDownloadExample() {
  const url = "https://jsonplaceholder.typicode.com/posts"

  try {
    const result = await downloadFile(url, "posts.json", {
      types: [
        {
          description: "JSON files",
          accept: { "application/json": [".json"] },
        },
      ],
    })

    if (result.success) {
      console.log("✅ 下载成功:", result.filename)
    } else {
      console.error("❌ 下载失败:", result.error)
    }
  } catch (error) {
    console.error("❌ 下载异常:", error)
  }
}

/**
 * 示例2: 带进度监控的下载
 */
export async function downloadWithProgress() {
  const url = "https://sample-files.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"

  try {
    const result = await downloadFile(url, "sample-video.mp4", {
      types: [
        {
          description: "Video files",
          accept: { "video/*": [".mp4", ".avi", ".mov"] },
        },
      ],
      onProgress: progress => {
        const { percentage, downloaded, total, speed } = progress

        console.log(`📊 下载进度: ${percentage.toFixed(1)}%`)
        console.log(`📁 已下载: ${formatFileSize(downloaded)}`)
        if (total) {
          console.log(`📦 总大小: ${formatFileSize(total)}`)
        }
        console.log(`⚡ 速度: ${formatFileSize(speed)}/s`)
        console.log("---")
      },
      onError: error => {
        console.error("💥 下载过程中出错:", error.message)
      },
    })

    return result
  } catch (error) {
    console.error("❌ 下载失败:", error)
    throw error
  }
}

/**
 * 示例3: 生成数据并下载
 */
export async function generateAndDownloadData() {
  try {
    // 检查浏览器支持
    if (!isFileSystemAccessSupported()) {
      console.warn("⚠️ 浏览器不支持高级下载功能，将使用传统方式")
    }

    // 生成大量测试数据
    const data = generateLargeDataset(10000) // 生成1万条记录
    const csvContent = convertArrayToCSV(data)

    // 创建 Blob
    const blob = new Blob([csvContent], {
      type: "text/csv;charset=utf-8",
    })
    const url = URL.createObjectURL(blob)

    try {
      const result = await downloadFile(url, "large-dataset.csv", {
        types: [
          {
            description: "CSV files",
            accept: { "text/csv": [".csv"] },
          },
        ],
        onProgress: progress => {
          // 更新 UI 进度条
          updateProgressBar(progress.percentage)
        },
      })

      if (result.success) {
        console.log(`✅ 成功导出 ${data.length} 条记录到: ${result.filename}`)
      }

      return result
    } finally {
      // 清理临时 URL
      URL.revokeObjectURL(url)
    }
  } catch (error) {
    console.error("❌ 生成和下载数据失败:", error)
    throw error
  }
}

/**
 * 示例4: 使用 Vue 组合函数
 */
export function useAdvancedDownload() {
  const isDownloading = ref(false)
  const downloadProgress = ref<DownloadProgress | null>(null)
  const downloadError = ref<string | null>(null)

  const startDownload = async (url: string, filename?: string) => {
    try {
      isDownloading.value = true
      downloadError.value = null
      downloadProgress.value = null

      const result = await downloadFile(url, filename, {
        onProgress: progress => {
          downloadProgress.value = progress
        },
        onError: error => {
          downloadError.value = error.message
        },
      })

      return result
    } finally {
      isDownloading.value = false
    }
  }

  const formatProgress = computed(() => {
    if (!downloadProgress.value) return null

    const p = downloadProgress.value
    return {
      percentage: p.percentage.toFixed(1) + "%",
      downloaded: formatFileSize(p.downloaded),
      total: p.total ? formatFileSize(p.total) : "未知",
      speed: formatFileSize(p.speed) + "/s",
      remainingTime: p.remainingTime
        ? formatRemainingTime(p.remainingTime)
        : null,
    }
  })

  return {
    isDownloading: readonly(isDownloading),
    downloadProgress: readonly(downloadProgress),
    downloadError: readonly(downloadError),
    formatProgress,
    startDownload,
    isSupported: isFileSystemAccessSupported(),
  }
}

// ================== 辅助函数 ==================

/**
 * 生成大量测试数据
 */
function generateLargeDataset(count: number) {
  const data = []
  const categories = ["A", "B", "C", "D", "E"]

  for (let i = 0; i < count; i++) {
    data.push({
      id: i + 1,
      name: `Item ${i + 1}`,
      category: categories[i % categories.length],
      value: Math.round(Math.random() * 1000),
      date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      description: `这是第 ${i + 1} 条测试数据，包含一些中文字符和符号！@#$%`,
    })
  }

  return data
}

/**
 * 将数组转换为 CSV 格式
 */
function convertArrayToCSV(data: any[]): string {
  if (!data || data.length === 0) {
    return ""
  }

  // 获取表头
  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(",")

  // 转换数据行
  const csvRows = data.map(row => {
    return headers
      .map(header => {
        const value = row[header]
        // 处理包含逗号、换行符或引号的值
        if (
          typeof value === "string" &&
          (value.includes(",") || value.includes("\n") || value.includes('"'))
        ) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value ?? ""
      })
      .join(",")
  })

  return [csvHeaders, ...csvRows].join("\n")
}

/**
 * 更新进度条 UI（示例）
 */
function updateProgressBar(percentage: number) {
  // 这里可以更新实际的 UI 组件
  console.log(`进度条更新: ${percentage.toFixed(1)}%`)

  // 如果有进度条元素，可以这样更新
  const progressBar = document.querySelector(
    "#download-progress"
  ) as HTMLProgressElement
  if (progressBar) {
    progressBar.value = percentage
  }
}

/**
 * 格式化剩余时间
 */
function formatRemainingTime(seconds: number): string {
  if (!seconds || !isFinite(seconds)) return "计算中..."

  if (seconds < 60) {
    return `${Math.round(seconds)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return `${minutes}分${remainingSeconds}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// ================== 使用示例 ==================

// 在 Vue 组件中使用
/*
<script setup>
import { useAdvancedDownload } from './download-examples'

const {
  isDownloading,
  downloadProgress,
  formatProgress,
  startDownload,
  isSupported
} = useAdvancedDownload()

async function handleDownload() {
  if (!isSupported) {
    alert('您的浏览器不支持高级下载功能')
    return
  }

  const url = 'https://example.com/large-file.zip'
  const result = await startDownload(url, 'my-file.zip')

  if (result.success) {
    console.log('下载成功!')
  }
}
</script>

<template>
  <div>
    <button @click="handleDownload" :disabled="isDownloading">
      {{ isDownloading ? '下载中...' : '开始下载' }}
    </button>

    <div v-if="downloadProgress" class="progress-info">
      <div class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: formatProgress?.percentage }"
        ></div>
      </div>
      <p>
        {{ formatProgress?.downloaded }} / {{ formatProgress?.total }}
        ({{ formatProgress?.speed }})
      </p>
      <p v-if="formatProgress?.remainingTime">
        剩余时间: {{ formatProgress.remainingTime }}
      </p>
    </div>
  </div>
</template>
*/
