<script setup lang="ts">
import { useUserStore } from "@/store/modules/user"
import type { FormInstance } from "@xquant/x-ui-plus"
import { XqMessage } from "@xquant/x-ui-plus"

import { loginRules } from "./utils/rule"
// import { initRouter, getTopMenu } from "@/router/utils";
import { bg, illustration } from "./utils/static"

defineOptions({
  name: "Login",
})
const router = useRouter()
const loading = ref(false)
const ruleFormRef = ref<FormInstance>()
const userStore = useUserStore()

const ruleForm = reactive({
  username: "admin",
  password: "admin123",
})

const onLogin = async (formEl: FormInstance | undefined) => {
  loading.value = true
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      userStore
        .loginByUsername({
          jobNo: ruleForm.username,
          password: ruleForm.password,
        })
        .then(() => {
          router.push("/")
          XqMessage({
            message: "登录成功",
            type: "success",
          })
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      loading.value = false
      return fields
    }
  })
}

/** 使用公共函数，避免`removeEventListener`失效 */
function onkeypress({ code }: KeyboardEvent) {
  if (code === "Enter") {
    onLogin(ruleFormRef.value)
  }
}

onMounted(() => {
  window.document.addEventListener("keypress", onkeypress)
})

onBeforeUnmount(() => {
  window.document.removeEventListener("keypress", onkeypress)
})
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="login-container">
      <div class="img">
        <img :src="illustration" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <h2 class="outline-none">管理系统</h2>

          <xq-form ref="ruleFormRef" :model="ruleForm" :rules="loginRules" size="large">
            <xq-form-item
              :rules="[
                {
                  required: true,
                  message: '请输入账号',
                  trigger: 'blur',
                },
              ]"
              prop="username"
              class="mb-[20px] w-full"
            >
              <xq-input v-model="ruleForm.username" clearable placeholder="账号" />
            </xq-form-item>

            <xq-form-item prop="password">
              <xq-input v-model="ruleForm.password" clearable show-password placeholder="密码" />
            </xq-form-item>

            <xq-button
              class="mt-4 w-full"
              type="primary"
              :loading="loading"
              @click="onLogin(ruleFormRef)"
            >
              登录
            </xq-button>
          </xq-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.xq-input-group__append, .xq-input-group__prepend) {
  padding: 0;
}
</style>
