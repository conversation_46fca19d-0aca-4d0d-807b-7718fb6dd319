/**
 * Build-time utility functions (Node.js environment)
 */

import { readdir, stat } from "fs/promises"
import { join } from "path"

/**
 * Format bytes to human readable string
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i]
}

/**
 * Get package size utility (for build)
 */
export const utils = {
  async getPackageSize(options: { folder: string; callback: (size: string) => void }) {
    try {
      const size = await getFolderSize(options.folder)
      options.callback(formatBytes(size))
    } catch (error) {
      console.error("Error calculating package size:", error)
      options.callback("Unknown")
    }
  },
}

async function getFolderSize(folderPath: string): Promise<number> {
  let totalSize = 0

  try {
    const items = await readdir(folderPath)

    for (const item of items) {
      const itemPath = join(folderPath, item)
      const stats = await stat(itemPath)

      if (stats.isDirectory()) {
        totalSize += await getFolderSize(itemPath)
      } else {
        totalSize += stats.size
      }
    }
  } catch {
    // If folder doesn't exist or can't be read, return 0
    return 0
  }

  return totalSize
}
