<template>
  <div class="micro-app-layout">
    <router-view v-slot="{ Component }">
      <!-- 不要使用 vue 的 transition 组件，其不兼容自定义 keep-alive 组件 -->
      <KeepAliveKey :keys="cachedRoutePaths">
        <RefreshComp v-if="refreshing" key="never-cache-this-path" @mounted="refreshed" />
        <component :is="Component" v-else :key="route.path" />
      </KeepAliveKey>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { KeepAliveKey, RefreshComp } from "@xquant/xp-admin-layout"
import { emitter } from "@/utils/mitt"

const parentProps: QianKunProps = inject("parentProps")

const replaceRouteBase = path => {
  return path.replace(parentProps.baseroute, "/")
}

// 初次打开以及后续路由发生变化时，缓存当前路由
const route = useRoute()
const cachedRoutePaths = reactive([route.path])

/**
 * 当前是不是页签关闭中
 */
const closed = ref(false)
/**
 * 当前是不是页签刷新中
 */
const refreshing = ref(false)
const refreshed = () => {
  // 如果组件挂载完成后发现是 close 导致的，先不要去渲染页面和修改缓存，等待新的路由变化，否则当前组件又会被再次加到缓存，没有达成清除目标
  if (closed.value) {
    // 关闭当前 tag 可能跳转到了另一个应用，并且再次跳转回来的时候又刚好是曾经关闭的页面，从而 route 不会变化，也不渲染页面，通过另一个事件处理
    return
  }
  refreshing.value = false
  cachedRoutePaths.includes(route.path) === false && cachedRoutePaths.push(route.path)
}

watch(
  () => route.fullPath,
  () => {
    // 第一次路由变化都要重置状态并渲染组件
    closed.value = false
    refreshing.value = false
    cachedRoutePaths.includes(route.path) === false && cachedRoutePaths.push(route.path)
  }
)

// 路由不变化则可能是从别的应用跳转当前应用上一次打开的页面，也可能是刷新
emitter.on("gotoSameRoute", () => {
  refreshing.value = false
  cachedRoutePaths.includes(route.path) === false && cachedRoutePaths.push(route.path)
})

// 监听主应用关闭路由关闭事件
if (parentProps.onGlobalStateChange) {
  parentProps.onGlobalStateChange((state, prev) => {
    console.log("onGlobalStateChange", state)
    if (state.clearRoutePath !== prev.clearRoutePath) {
      const isRefresh = /@00\d+$/.test(state.clearRoutePath)
      const pathName = replaceRouteBase(state.clearRoutePath).replace(/@\d+$/, "")
      const index = cachedRoutePaths.indexOf(pathName)
      if (index > -1) {
        cachedRoutePaths.splice(index, 1)
        refreshing.value = true
        if (isRefresh === false && pathName === route.path) {
          closed.value = true
        }
      }
    }
  })
}
</script>

<style scoped>
.micro-app-layout {
  height: 100%;
}
</style>
