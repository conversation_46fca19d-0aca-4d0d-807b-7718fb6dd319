/**
 * Web Worker 用于处理大文件下载
 * 避免阻塞主线程
 */

import type { WorkerMessage, ChunkInfo } from "./types"

// Web Worker 环境下的全局对象
declare const self: Worker

interface DownloadContext {
  url: string
  filename: string
  chunkSize: number
  maxRetries: number
  headers: Record<string, string>
  totalSize?: number
  abortController: AbortController
}

let currentDownload: DownloadContext | null = null

/**
 * 发送消息到主线程
 */
function postMessage(message: WorkerMessage) {
  self.postMessage(message)
}

/**
 * 延迟函数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 下载单个分块
 */
async function downloadChunk(
  url: string,
  chunk: ChunkInfo,
  headers: Record<string, string>,
  signal: AbortSignal,
  maxRetries: number = 3
): Promise<ArrayBuffer> {
  let attempt = 0

  while (attempt < maxRetries) {
    try {
      const response = await fetch(url, {
        headers: {
          ...headers,
          Range: `bytes=${chunk.start}-${chunk.end}`,
        },
        signal,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.arrayBuffer()
    } catch (error) {
      attempt++

      if (signal.aborted) {
        throw new Error("下载已取消")
      }

      if (attempt >= maxRetries) {
        throw error
      }

      // 指数退避重试
      const delayTime = Math.min(1000 * Math.pow(2, attempt), 10000)
      await delay(delayTime)
    }
  }

  throw new Error("达到最大重试次数")
}

/**
 * 流式下载文件
 */
async function streamDownload(
  url: string,
  headers: Record<string, string>,
  signal: AbortSignal
): Promise<void> {
  try {
    const response = await fetch(url, {
      headers,
      signal,
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    if (!response.body) {
      throw new Error("响应体为空")
    }

    const reader = response.body.getReader()
    let downloaded = 0
    const startTime = Date.now()

    while (true) {
      const { done, value } = await reader.read()

      if (done) break

      if (signal.aborted) {
        throw new Error("下载已取消")
      }

      downloaded += value.length

      // 发送数据块到主线程
      postMessage({
        type: "chunk",
        data: {
          chunk: value,
          downloaded,
          startTime,
        },
      })
    }

    postMessage({
      type: "complete",
      data: { downloaded },
    })
  } catch (error) {
    postMessage({
      type: "error",
      error: error instanceof Error ? error.message : String(error),
    })
  }
}

/**
 * 分块下载文件（支持断点续传）
 */
async function chunkedDownload(
  url: string,
  totalSize: number,
  chunkSize: number,
  headers: Record<string, string>,
  signal: AbortSignal,
  maxRetries: number
): Promise<void> {
  try {
    const chunks: ChunkInfo[] = []
    let start = 0

    // 计算分块
    while (start < totalSize) {
      const end = Math.min(start + chunkSize - 1, totalSize - 1)
      chunks.push({
        index: chunks.length,
        start,
        end,
        size: end - start + 1,
      })
      start = end + 1
    }

    let downloaded = 0
    const startTime = Date.now()

    // 并行下载分块（限制并发数）
    const concurrency = 4
    const executing: Promise<void>[] = []

    for (const chunk of chunks) {
      const downloadPromise = downloadChunk(
        url,
        chunk,
        headers,
        signal,
        maxRetries
      ).then(data => {
        if (signal.aborted) return

        downloaded += data.byteLength

        // 发送数据块到主线程
        postMessage({
          type: "chunk",
          data: {
            chunk: new Uint8Array(data),
            chunkIndex: chunk.index,
            downloaded,
            totalSize,
            startTime,
          },
        })
      })

      executing.push(downloadPromise)

      // 控制并发数
      if (executing.length >= concurrency) {
        await Promise.race(executing)
        executing.splice(
          executing.findIndex(p => p === downloadPromise),
          1
        )
      }
    }

    // 等待所有分块下载完成
    await Promise.all(executing)

    postMessage({
      type: "complete",
      data: { downloaded: totalSize },
    })
  } catch (error) {
    postMessage({
      type: "error",
      error: error instanceof Error ? error.message : String(error),
    })
  }
}

/**
 * 监听主线程消息
 */
self.addEventListener("message", async event => {
  const { type, data } = event.data

  switch (type) {
    case "start": {
      const {
        url,
        filename,
        chunkSize = 1024 * 1024, // 1MB
        maxRetries = 3,
        headers = {},
        totalSize,
      } = data

      // 创建新的下载上下文
      currentDownload = {
        url,
        filename,
        chunkSize,
        maxRetries,
        headers,
        totalSize,
        abortController: new AbortController(),
      }

      postMessage({
        type: "progress",
        data: { status: "starting" },
      })

      // 根据是否已知文件大小选择下载策略
      if (totalSize && totalSize > chunkSize * 2) {
        // 大文件使用分块下载
        await chunkedDownload(
          url,
          totalSize,
          chunkSize,
          headers,
          currentDownload.abortController.signal,
          maxRetries
        )
      } else {
        // 小文件或未知大小使用流式下载
        await streamDownload(
          url,
          headers,
          currentDownload.abortController.signal
        )
      }
      break
    }

    case "cancel": {
      if (currentDownload) {
        currentDownload.abortController.abort()
        currentDownload = null

        postMessage({
          type: "progress",
          data: { status: "cancelled" },
        })
      }
      break
    }
  }
})

// Worker 初始化完成
postMessage({
  type: "progress",
  data: { status: "ready" },
})
