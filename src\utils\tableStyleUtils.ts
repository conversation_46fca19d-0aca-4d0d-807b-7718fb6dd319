/**
 * 表格分组样式工具函数
 * 用于统一处理分组表头和单元格的样式类名
 */

/**
 * 获取表头单元格样式类名
 * @param column 列配置对象
 * @param columnIndex 列索引
 * @returns 样式类名字符串
 */
export function getGroupedHeaderCellClassName({
  column,
}: {
  column: any
  columnIndex?: number
}): string {
  // 根据分组表头设置样式
  if (column.level === 1) {
    const label = column.label
    if (label === "产品信息") {
      return "product-group-header"
    } else if (label === "指标基础信息") {
      return "indicator-group-header"
    } else if (label === "指标使用情况") {
      return "usage-group-header"
    }
  }

  // 子表头样式
  const parentLabel = column.parent?.label
  if (parentLabel === "产品信息") {
    return "product-header"
  } else if (parentLabel === "指标基础信息") {
    return "indicator-header"
  } else if (parentLabel === "指标使用情况") {
    return "usage-header"
  }

  return ""
}

/**
 * 获取单元格样式类名
 * @param column 列配置对象
 * @param row 行数据对象
 * @param rowIndex 行索引
 * @returns 样式类名字符串
 */
export function getGroupedCellClassName({
  column,
  row,
  rowIndex,
}: {
  column: any
  row?: any
  rowIndex: number
}): string {
  const isOddRow = rowIndex % 2 === 1
  let className = ""

  // 根据列的分组设置背景色
  const parentLabel = column.parent?.label
  if (parentLabel === "产品信息") {
    className = isOddRow ? "product-cell-striped" : "product-cell"
  } else if (parentLabel === "指标基础信息") {
    className = isOddRow ? "indicator-cell-striped" : "indicator-cell"
  } else if (parentLabel === "指标使用情况") {
    className = isOddRow ? "usage-cell-striped" : "usage-cell"
  } else {
    className = isOddRow ? "normal-cell-striped" : ""
  }

  return className
}

/**
 * 表格分组配置类型定义
 */
export interface TableGroupConfig {
  /** 第一个分组标签 */
  groupOneLabel?: string
  /** 第二个分组标签 */
  groupTwoLabel?: string
  /** 第三个分组标签 */
  groupThreeLabel?: string
}

/**
 * 创建自定义分组的样式函数
 * @param config 分组配置，最多支持3个分组
 * @returns 样式函数对象
 */
export function createGroupedTableStyleFunctions(
  config: TableGroupConfig = {}
) {
  const {
    groupOneLabel = "产品信息",
    groupTwoLabel = "指标基础信息",
    groupThreeLabel = "指标使用情况",
  } = config

  // 分组标签到样式类名的映射
  const groupLabelToStyleMap = {
    [groupOneLabel]: {
      groupHeader: "product-group-header",
      header: "product-header",
      cell: "product-cell",
      cellStriped: "product-cell-striped",
    },
    [groupTwoLabel]: {
      groupHeader: "indicator-group-header",
      header: "indicator-header",
      cell: "indicator-cell",
      cellStriped: "indicator-cell-striped",
    },
    [groupThreeLabel]: {
      groupHeader: "usage-group-header",
      header: "usage-header",
      cell: "usage-cell",
      cellStriped: "usage-cell-striped",
    },
  }

  return {
    getHeaderCellClassName: ({
      column,
    }: {
      column: any
      columnIndex?: number
    }) => {
      if (column.level === 1) {
        // 第一级表头（分组表头）
        const label = column.label
        const styleConfig = groupLabelToStyleMap[label]
        return styleConfig?.groupHeader || ""
      }

      // 第二级表头（子表头）
      const parentLabel = column.parent?.label
      const styleConfig = groupLabelToStyleMap[parentLabel]
      return styleConfig?.header || ""
    },

    getCellClassName: ({
      column,
      rowIndex,
    }: {
      column: any
      row?: any
      rowIndex: number
    }) => {
      const isOddRow = rowIndex % 2 === 1
      const parentLabel = column.parent?.label
      const styleConfig = groupLabelToStyleMap[parentLabel]

      if (styleConfig) {
        return isOddRow ? styleConfig.cellStriped : styleConfig.cell
      }

      // 其他列的斑马纹
      return isOddRow ? "normal-cell-striped" : ""
    },
  }
}
