import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { VueQueryPlugin } from '@tanstack/vue-query'

import QueryForm from '../query-form.vue'
import Table from '../table.vue'

// 创建测试用的 Vue Query 客户端
const queryClient = {
  getQueryData: () => null,
  setQueryData: () => {},
  invalidateQueries: () => {},
  removeQueries: () => {},
}

describe('Preference Components Integration', () => {
  const createWrapper = (component: any) => {
    const pinia = createPinia()
    
    return mount(component, {
      global: {
        plugins: [
          pinia,
          [VueQueryPlugin, { queryClient }]
        ],
        stubs: {
          'xq-form-query': true,
          'xq-form-layout-item': true,
          'xq-input': true,
          'xq-date-picker': true,
          'xq-button': true,
          'xq-icon': true,
          'xq-table': true,
          'xq-table-column': true,
          'xq-pagination': true,
        }
      }
    })
  }

  it('should render QueryForm component without errors', () => {
    const wrapper = createWrapper(QueryForm)
    expect(wrapper.exists()).toBe(true)
  })

  it('should render Table component without errors', () => {
    const wrapper = createWrapper(Table)
    expect(wrapper.exists()).toBe(true)
  })

  it('should have correct form fields in QueryForm', () => {
    const wrapper = createWrapper(QueryForm)
    
    // 检查是否包含必要的表单字段
    const formItems = wrapper.findAll('[label]')
    const labels = formItems.map(item => item.attributes('label'))
    
    expect(labels).toContain('指标编码')
    expect(labels).toContain('指标管理部门')
    expect(labels).toContain('指标状态')
    expect(labels).toContain('查询区间')
  })

  it('should have correct table columns in Table', () => {
    const wrapper = createWrapper(Table)
    
    // 检查是否包含必要的表格列
    const tableColumns = wrapper.findAll('[label]')
    const labels = tableColumns.map(col => col.attributes('label'))
    
    expect(labels).toContain('指标基础信息')
    expect(labels).toContain('指标使用情况')
    expect(labels).toContain('指标类型')
    expect(labels).toContain('指标名称')
    expect(labels).toContain('额度值')
    expect(labels).toContain('预警值')
    expect(labels).toContain('指标管理部门')
    expect(labels).toContain('实际值')
    expect(labels).toContain('指标状态')
  })
})
